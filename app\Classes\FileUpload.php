<?php

namespace App\Classes;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUpload
{
    private static $instance;

    /** upload file
     * ============ uploadFile ==========
     *
     * @param request
     * @param fieldname
     * @param file
     * @param folder
     * @return imageName
     */
    public static function upload($request, $fieldname, $file, $folder)
    {
        if ($request->hasFile($fieldname)) {
            $source = $request->file($fieldname);
            $image_name = 'egens' . '-' . Str::random(8) . '.' . str_replace(' ', '-', $source->getClientOriginalName());

            if ($file != '') {

                if (Storage::disk('local')->exists('public/' . $folder . '/' . $file)) {
                    Storage::disk('local')->delete('public/' . $folder . '/' . $file);
                }
            }
            $source->storeAs('public/' . $folder, $image_name);

            return $image_name;
        }
    }

    public static function base64ImgUpload($requesFile, $file, $folder)
    {
        $extension = explode('/', mime_content_type($requesFile))[1];
        str_replace('data:image/svg+xml;base64,', '', $requesFile, $count);
        if ($count > 0) {
            $image = base64_decode(str_replace('data:image/svg+xml;base64,', '', $requesFile));
            $imageName = 'egens' . '-' . Str::random(10) . '.svg';
        } else {
            $image = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $requesFile));
            $imageName = 'egens' . '-' . Str::random(10) . '.' . $extension;
        }
        if ($file != '') {

            if (Storage::disk('local')->exists('public/' . $folder . '/' . $file)) {
                Storage::disk('local')->delete('public/' . $folder . '/' . $file);
            }
        }

        Storage::disk('local')->put('public/' . $folder . '/' . $imageName, $image);

        return $imageName;
    }


    /** file delete
     * ============ filedelele ==========
     *
     * @param folder
     * @param  file
     * @return response true|false
     */
    public static function fileDelete($folder, $file)
    {
        if (Storage::disk('local')->exists('public/' . $folder . '/' . $file)) {
            Storage::disk('local')->delete('public/' . $folder . '/' . $file);
        }

        return true;
    }

    public static function getInstance()
    {
        if (self::$instance == null) {
            self::$instance = new fileUpload;
        }

        return self::$instance;
    }
}
