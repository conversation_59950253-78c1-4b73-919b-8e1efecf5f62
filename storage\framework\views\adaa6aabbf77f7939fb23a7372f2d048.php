


<?php
     $amount = implode(",", $reportSubscription);
     $months=implode('","', $subscriptionMonth);
     $companyUser = implode(",", $reportCompany);
     $cMonth=implode('","', $companyMonth);







?>
<script>

   let cmonth =["<?php echo $cMonth ?>"];
   let smonth =["<?php echo $months ?>"];

    (function($) {
        "use strict";

        if (sessionStorage.getItem("showWelcomeMessage") === '0') {
            toastr.options.closeButton = true;
            toastr.options.positionClass = 'toast-bottom-right';
            toastr.options.showDuration = 1000;
            toastr['success']('Hello, welcome to E-job.');
            sessionStorage.setItem("showWelcomeMessage", "1");
        }

        const ctx = document.getElementById('myChart');
        const registerCompany = document.getElementById('registerCompany');


        const data = {
            labels:smonth,
            datasets: [{
                label: 'Earning',
                data:[<?php echo e($amount); ?>] ,
                fill: false,
                borderColor: 'rgb(75, 192, 192)',
            }]
        };

        new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        const datad = {
            labels:cmonth,
            datasets: [{
                label: 'Company',
                data: [<?php echo e($companyUser); ?>],
                fill: false,
                borderColor: 'rgb(75, 192, 192)',
            }]
        };


        const config = {
            type: 'line',
            data: datad,
            options: {
                animations: {
                    tension: {
                        duration: 1000,
                        easing: 'linear',
                        from: 1,
                        to: 0,
                        loop: true
                    }
                },
                scales: {
                    y: { // defining min and max so hiding the dataset does not change scale range
                        min: 0,
                        max: 100
                    }
                }
            }
        };
        const chart = new Chart(registerCompany, config);

    })(jQuery);
</script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/admin/dashboard.blade.php ENDPATH**/ ?>