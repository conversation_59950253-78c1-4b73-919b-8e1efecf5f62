<?php

use App\Models\Admin\Blog;
use App\Models\Admin\BlogCategory;
use App\Models\Admin\BlogComment;
use App\Models\Admin\Candidate;
use App\Models\Admin\CareerLevel;
use App\Models\Admin\Category;
use App\Models\Admin\City;
use App\Models\Admin\Company;
use App\Models\Admin\CompanyType;
use App\Models\Admin\Country;
use App\Models\Admin\Currency;
use App\Models\Admin\EducationLavel;
use App\Models\Admin\Faq;
use App\Models\Admin\Job;
use App\Models\Admin\JobExperience;
use App\Models\Admin\JobRole;
use App\Models\Admin\JobType;
use App\Models\Admin\Language;
use App\Models\Admin\Menu;
use App\Models\Admin\MenuItem;
use App\Models\Admin\Package;
use App\Models\Admin\PackageAddon;
use App\Models\Admin\Profession;
use App\Models\Admin\State;
use App\Models\Admin\Testimonial;
use App\Models\Admin\ThemeOption;
use App\Models\Admin\TrustedCompany;
use App\Models\Admin\WidgetsContents;
use App\Models\Front\ApplyJob;
use App\Models\Front\Subscription;
use App\Models\Translation;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;

/**
 * =============  auth check =============
 *
 * @return object $user
 */
if (! function_exists('authCheck')) {

    function authCheck()
    {
        if (Auth::check()) {
            $user = Auth::user();
            if ($user->company !== '' || $user->candidate !== '' || $user->bookmarkJobs !== '') {
                return $user;
            }
        }
    }
}

/**
 * =============  auth check =============
 *
 * @return object $user
 */
if (! function_exists('defaultCurrency')) {

    function defaultCurrency()
    {
        return isset(getThemeOption('backend_general')['default_currency']) ? getThemeOption('backend_general')['default_currency'] : '';
    }
}

/**
 * ============= userBookBarkJobs =============
 *
 * @return Response
 */
if (! function_exists('userBookBarkJobs')) {

    function userBookBarkJobs()
    {
        if (Auth::check()) {
            $user = Auth::user()->load('bookmarkJobs');
            return $user->bookmarkJobs->pluck('id')->toArray();
        }
    }
}

/**
 * ============= userApplyJob =============
 *
 * @return Response
 */
if (! function_exists('userApplyJob')) {

    function userApplyJob()
    {
        if (Auth::check()) {
            $user = Auth::user()->load('applyJobs');

            return $user->applyJobs->pluck('id')->toArray();
        }
    }
}

/**
 * ============= check array value =============
 *
 * @return object $user
 */
if (! function_exists('checkArrayValue')) {

    function checkArrayValue($value, $array)
    {
        return in_array($value, $array) ? true : false;
    }
}

/*============= slug generate =============*/

/**
 * Generate a URL friendly "slug" from a given string.
 *
 * @param  string  $name
 * @param  string  $separator
 * @return string
 */
if (! function_exists('slug')) {

    function slug($title, $separator = '-')
    {

        // Convert all dashes/underscores into separator
        $flip = $separator === '-' ? '_' : '-';

        $title = preg_replace('!['.preg_quote($flip).']+!u', $separator, $title);

        // Replace @ with the word 'at'
        $title = str_replace('@', $separator.'at'.$separator, $title);

        // Remove all characters that are not the separator, letters, numbers, or whitespace.
        $title = preg_replace('![^'.preg_quote($separator).'\pL\pN\s]+!u', '', strtolower($title));

        // Replace all separator characters and whitespace by a single separator
        $title = preg_replace('!['.preg_quote($separator).'\s]+!u', $separator, $title);

        return trim($title, $separator);
    }
}

/** subcription check for company
 * ========== subcriptionCheck========
 *
 * @return response true|false
 */
if (! function_exists('subcriptionCheck')) {

    function subcriptionCheck()
    {
        $subscription = Subscription::where('company_id', authCheck()->company->id)
            ->where(['status' => 'Active', 'payment_status' => 'Paid'])
            ->latest()
            ->first();
       return  $subscription ?? false;
    }
}

/** check featured job
 * ========== featuredJob========
 *
 * @return response true|false
 */
if (! function_exists('featuredJob')) {
    function featuredJob()
    {
        $featuredJob = Subscription::where('company_id', authCheck()->company->id)
            ->where(['status' => 'Active', 'payment_status' => 'Paid'])
            ->select('avilable_featured_qty')
            ->first();
        if ($featuredJob) {
            return $featuredJob->avilable_featured_qty > 0 ? true : false;
        } else {
            return false;
        }
    }
}

/** subcription job qty update when job post
 * ========== subcriptionJob========
 */
if (! function_exists('subcriptionJob')) {

    function subcriptionJob()
    {
        $job = Subscription::where('user_id', authCheck()->id)
            ->where('company_id', authCheck()->company->id)
            ->where(['status' => 'Active', 'payment_status' => 'Paid'])
            ->latest()
            ->first();
        $job->avilable_qty -= 1;
        $job->update();
    }
}

/** subcription cv view qty upda
 * ========== subcriptionJob========
 */
if (! function_exists('viewCvQtyUpdate')) {

    function viewCvQtyUpdate()
    {
        $job = Subscription::where('user_id', authCheck()->id)
            ->where('company_id', authCheck()->company->id)
            ->where(['status' => 'Active', 'payment_status' => 'Paid'])
            ->latest()
            ->first();
        $job->avilable_cv_view_qty -= 1;
        $job->update();
    }
}

/** subcription featured job qty update when job post is featured
 *
 * ========== featuredJobUpdate ========
 */
if (! function_exists('featuredJobUpdate')) {

    function featuredJobUpdate()
    {
        $featuredQty = Subscription::where('user_id', authCheck()->id)
            ->where('company_id', authCheck()->company->id)
            ->where(['status' => 'Active', 'payment_status' => 'Paid'])
            ->latest()
            ->first();
        $featuredQty->avilable_featured_qty -= 1;
        $featuredQty->update();
    }
}

/** translate language
 * ========= translate ===========
 *
 * @param  key
 * @param  lang = null
 * @param  addslashes= false
 * @return
 */
if (! function_exists('translate')) {

    function translate($key, $value = null, $lang = null, $addslashes = false)
    {
        if ($lang == null) {
            $lang = App::getLocale();
        }
        $lang_key = preg_replace('/[^A-Za-z0-9\_]/', '', str_replace(' ', '_', strtolower($key)));

        $translations_default = Cache::rememberForever('translations-'.$lang, function () use ($lang) {
            return Translation::where('lang', $lang)->pluck('lang_value', 'lang_key')->toArray();
        });

        if (! isset($translations_default[$lang_key])) {
            $translation_def = new Translation;
            $translation_def->lang = $lang;
            $translation_def->lang_key = $lang_key;
            $translation_def->lang_value = $value ? $value : $key;
            $translation_def->save();
            Cache::forget('translations-'.$lang);
        }
        $translation_locale = Cache::rememberForever('translations-'.$lang, function () use ($lang) {
            return Translation::where('lang', $lang)->pluck('lang_value', 'lang_key')->toArray();
        });

        if (isset($translation_locale[$lang_key])) {
            return $addslashes ? addslashes($translation_locale[$lang_key]) : $translation_locale[$lang_key];
        } elseif (isset($translations_default[$lang_key])) {
            return $translations_default[$lang_key];
        } else {
            return $key;
        }
    }
}

/** active language
 * ========== activeLanguage =========
 *
 * @return Response
 */
if (! function_exists('activeLanguage')) {
    function activeLanguage()
    {
        if (Session::has('locale')) {

            $locale = Session::get('locale', Config::get('app.locale'));
        } else {
            $locale = env('DEFAULT_LANGUAGE', 'en');
        }

        return $locale;
    }
}

/** default language
 * ============ default_language ==========
 *
 * @return Response
 */
if (! function_exists('default_language')) {

    function default_language()
    {
        return env('DEFAULT_LANGUAGE');
    }
}

/** language
 *========= language ==========
 *
 * @return Response
 */
if (! function_exists('language')) {

    function language()
    {
        return Language::where('status', 1)->get();
    }
}

/** professions
 *========= professions ==========
 *
 * @return Response
 */
if (! function_exists('professions')) {

    function professions()
    {
        return Profession::all();
    }
}

/** jobRole
 *
 *========= jobRole ==========
 *
 * @return Response
 */
if (! function_exists('jobRole')) {

    function jobRole()
    {
        return JobRole::all();
    }
}

/** fileExists
 *
 *========= fileExists ==========
 *
 * @return Response
 */
if (! function_exists('fileExists')) {
    function fileExists($folder, $fileName)
    {

        return !empty($fileName) && Storage::disk('public')->exists("$folder/$fileName");
    }
}


/** Country List
 *
 *========= countryAll ==========
 *
 * @return Response
 */
if (! function_exists('countryAll')) {
    function countryAll($status = null)
    {
        if ($status) {
            return Country::where('status', $status)->get();
        }

        return Country::all();
    }
}

/** State List
 *
 *========= stateAll ==========
 *
 * @return Response
 */
if (! function_exists('stateAll')) {
    function stateAll()
    {
        return State::all();
    }
}

/** Job Category List
 *
 *========= jobCategory ==========
 *
 * @return Response
 */
if (! function_exists('jobCategory')) {
    function jobCategory($status = null)
    {
        if ($status) {
            return Category::with(['jobs' => function ($q) {
                $q->withWhereHas('company', function ($query) {
                    $query->withWhereHas('user', function ($query) {
                        $query->where('status', 1);
                        $query->where('is_email_verified', 1);
                    });
                    $query->where('status', 1);
                    $query->select('id', 'company_name', 'slug', 'status');
                })
                    ->where('status', 1)
                    ->where('job_deadline', '>=', now())
                    ->select('id', 'company_id', 'category_id', 'job_deadline', 'status');
            }])
                ->withCount('activeJobs')
                ->where('status', $status)->get();
        } else {
            return Category::all();
        }
    }
}

/** Job type List
 *
 *========= jobType ==========
 *
 * @return Response
 */
if (! function_exists('jobType')) {
    function jobType($status = null)
    {
        if ($status) {
            return JobType::where('status', $status)
                ->with(['jobJobTypes' => function ($query) {
                    $query->withCount(['activeJobs' => function ($q) {
                        $q->withWhereHas('company', function ($query) {
                            $query->withWhereHas('user', function ($query) {
                                $query->where('status', 1);
                                $query->where('is_email_verified', 1);
                            });
                            $query->where('status', 1);
                            $query->select('id', 'company_name', 'slug', 'status');
                        })
                            ->where('status', 1)
                            ->where('job_deadline', '>=', now())
                            ->select('id', 'company_id', 'category_id', 'job_deadline', 'status');
                    }]);
                }])
                ->withCount('jobJobTypes as jobs')
                ->get();
        }

        return JobType::all();
    }
}

/** Job type List
 *
 *========= jobcareerLevel ==========
 *
 * @return Response
 */
if (! function_exists('jobcareerLevel')) {
    function jobcareerLevel($status = null)
    {
        if ($status) {
            return CareerLevel::withCount('activeJobs')->where('status', $status)->get();
        }

        return CareerLevel::all();
    }
}

/** Job Experience List
 *
 *========= jobExperience==========
 *
 * @return Response
 */
if (! function_exists('jobExperience')) {
    function jobExperience($status = null)
    {
        if ($status) {
            return JobExperience::withCount('activeJobs')->where('status', $status)->get();
        }

        return JobExperience::all();
    }
}

/** Education Level
 *
 *========= Education Level ==========
 *
 * @return Response
 */
if (! function_exists('educationLevel')) {
    function educationLevel($status = null)
    {
        if ($status) {
            return EducationLavel::where('status', $status)->get();
        }

        return EducationLavel::all();
    }
}

/** Country List
 *
 *========= countryAll ==========
 *
 * @return Response
 */
if (! function_exists('companyAll')) {
    function companyAll($status = null)
    {
        if ($status) {
            return Company::where('status', $status)->get();
        }

        return Company::all();
    }
}

/** Job Role List
 *
 *========= countryAll ==========
 *
 * @return Response
 */
if (! function_exists('jobRole')) {
    function jobRole($status = null)
    {
        if ($status) {
            return JobRole::where('status', $status)->get();
        }

        return JobRole::all();
    }
}

/** Job Custom Date
 *
 *========= countryAll ==========
 *
 * @return Response
 */
if (! function_exists('customDateFormate')) {
    function customDateFormate($date, $format = null)
    {
        $parse = Carbon::parse($date);

        return $format ? $parse->format($format) : $parse->format('d M, Y');
    }
}

/** submenu get
 *
 *========= countryAll ==========
 *
 * @return Response
 */
if (! function_exists('getChildrens')) {

    function getChildrens($menu_id, $parent_id = null, $orderBy = 'asc')
    {

        if (alreadyInstalled() !== false) {
            return MenuItem::with('childrens')
                ->where(['menu_id' => $menu_id, 'parent_id' => $parent_id])
                ->orderBy('order', $orderBy)
                ->get();
        }

        return false;
    }
}

/** submenu get
 *
 *========= countryAll ==========
 *
 * @return Response
 */
if (! function_exists('activeCategoryCountJob')) {

    function activeCategoryCountJob($limit = null, $perPage = null)
    {

        // dd($perPage);
        $categories = Category::with(['jobs' => function ($q) {
            $q->withWhereHas('company', function ($query) {
                $query->withWhereHas('user', function ($query) {
                    $query->where('status', 1);
                    $query->where('is_email_verified', 1);
                });
                $query->where('status', 1);
                $query->select('id', 'company_name', 'slug', 'status');
            })
                ->where('status', 1)
                ->where('job_deadline', '>=', now())
                ->select('id', 'company_id', 'category_id', 'job_deadline', 'status');
        }])
            ->withCount('jobs')->where('status', 1)
            ->orderBy('id', 'DESC');

        if (isset($limit) || isset($perPage)) {
            return $limit ? $categories->limit($limit)->get() : $categories->paginate($perPage);
        } else {
            return $categories->paginate(15);
        }
    }
}

/** featuredJobs get
 *
 *========= featuredJobs ==========
 *
 * @return Response
 */
if (! function_exists('featuredJobs')) {

    function featuredJobs($limit = null, $perPage = null)
    {

        $job = Job::withWhereHas('company', function ($query) {
            $query->withWhereHas('user', function ($query) {
                $query->where('status', 1);
                $query->where('is_email_verified', 1);
            })
                ->where('status', 1)
                ->select('id', 'company_name', 'slug', 'status');
        })
            ->with('jobJobTypes')
            ->where('status', 1)
            ->where('job_deadline', '>=', Carbon::now())
            ->where('job_featured', 1)
            ->orderBy('id', 'DESC')
            ->countryCity();

        if (isset($limit) || isset($perPage)) {
            return $limit ? $job->limit($limit)->get() : $job->paginate($perPage);
        } else {
            return $job->get();
        }
    }
}

/** submenu get
 *
 *========= countryAll ==========
 *
 * @return Response
 */
if (! function_exists('topRecruiter')) {

    function topRecruiter($limit = null, $perPage = null)
    {

        $company = Company::withWhereHas('user', function ($query) {
            $query->where('status', 1);
            $query->where('is_email_verified', 1)
                ->select('id', 'status', 'is_email_verified');
        })
            ->withWhereHas('jobs', function ($q) {
                $q->where('job_deadline', '>=', now())->where('status', 1)->select('id', 'company_id', 'job_deadline', 'job_vacancy');
            })
            ->select('id', 'company_name', 'company_type_id', 'user_id', 'company_logo', 'slug', 'status')
            ->countryCity()
            ->withCount('jobs')
            ->withSum('jobs', 'job_vacancy')
            ->orderBy('jobs_sum_job_vacancy', 'desc')
            ->where('status', 1);

        if (isset($limit) || isset($perPage)) {
            return $limit ? $company->limit($limit)->get() : $company->paginate($perPage);
        } else {
            return $company->get();
        }
    }
}

/**blog post
 *
 *========= blog ==========
 * @return Response
 */

if (! function_exists('blog')) {

    function blog($limit = null, $perPage = null)
    {
        $blog = Blog::where('status', 1)
            ->orderBy('id', 'DESC')
            ->select('id', 'blog_title', 'slug', 'blog_thumbnail', 'blog_details', 'created_at', 'user_id')
            ->authorName()
            ->withCount('comments');

        if (isset($limit) || isset($perPage)) {
            return $limit ? $blog->limit($limit)->get() : $blog->paginate($perPage);
        } else {
            return $blog->get();
        }
    }
}

/**====location jobs
 *
 *========= locationJobs ==========
 * @return Response
 */

if (! function_exists('locationJobs')) {

    function locationJobs($limit = null, $perPage = null)
    {

        $city = City::withWhereHas('jobs', function ($q) {
            $q->withWhereHas('company', function ($query) {
                $query->withWhereHas('user', function ($query) {
                    $query->where('status', 1);
                    $query->where('is_email_verified', 1);
                });
                $query->where('status', 1);
                $query->select('id', 'company_name', 'slug', 'status');
            })
                ->where('job_deadline', '>=', now())->where('status', 1)->select('id', 'city_id', 'company_id', 'job_deadline', 'job_vacancy');
        })
            ->select('name', 'location_image')
            ->country()
            ->withCount('jobs')
            ->where('status', 1);
        if (isset($limit) || isset($perPage)) {
            return $limit ? $city->limit($limit)->get() : $city->paginate($perPage);
        } else {
            return $city->get();
        }
    }
}

/**============ truestCompany
 *
 *========= truestCompany ==========
 * @return Response
 */

if (! function_exists('truestCompany')) {

    function truestCompany($limit = null, $perPage = null)
    {
        $trustedCompany = TrustedCompany::where('status', 1)
            ->orderBy('id', 'DESC');

        if (isset($limit) || isset($perPage)) {
            return $limit ? $trustedCompany->limit($limit)->get() : $trustedCompany->paginate($perPage);
        } else {
            return $trustedCompany->get();
        }
    }
}

/**============ testimonial
 *
 *========= testimonial ==========
 * @return Response
 */

if (! function_exists('testimonial')) {

    function testimonial($limit = null, $perPage = null)
    {
        $testimonial = Testimonial::where('status', 1)
            ->orderBy('id', 'DESC');
        if (isset($limit) || isset($perpage)) {
            return $limit ? $testimonial->limit($limit)->get() : $testimonial->paginate($perPage);
        } else {
            return $testimonial->get();
        }
    }
}

/**======== related Jobs ===========
 *
 * ======= relatedJobs =========
 * @param int $param category id
 * @param int $id
 * @return Response
 *
 **/

if (! function_exists('relatedJobs')) {

    function relatedJobs($param, $id)
    {
        return Job::withWhereHas('company', function ($query) {
            $query->withWhereHas('user', function ($query) {
                $query->where('status', 1);
                $query->where('is_email_verified', 1);
            });
            $query->where('status', 1);
            $query->select('id', 'company_name', 'slug', 'status');
        })
            ->with('jobJobTypes')->where('status', 1)
            ->where('job_deadline', '>=', Carbon::now())
            ->orderBy('id', 'DESC')
            ->where('category_id', $param)
            ->whereNot('id', $id)
            ->get();
    }
}

/** getmap link
 *========= getmapLink ===========
 *
 * @param  string iframeLink
 * @return Response
 */
if (! function_exists('getmapLink')) {

    function getmapLink($iframeLink)
    {
        if ($iframeLink != null) {
            preg_match('~iframe.*src="([^"]*)"~', $iframeLink, $result);

            return $result[1];
        }

        return false;
    }
}

/** Company Latest Job
 *========= companyLatestJob ===========
 *
 * @param int int (company Id)
 * @return Response
 */
if (! function_exists('companyLatestJob')) {

    function companyLatestJob($perPage, $companyId)
    {
        return Job::withWhereHas('company', function ($query) {
            $query->withWhereHas('user', function ($query) {
                $query->where('status', 1);
                $query->where('is_email_verified', 1);
            });
            $query->where('status', 1);
            $query->select('id', 'company_name', 'slug', 'status');
        })
            ->with('jobJobTypes', 'experiences')

            ->select('id', 'job_title', 'slug', 'company_id', 'job_deadline', 'job_vacancy', 'job_image', 'salary_mode', 'min_salary', 'max_salary', 'salary_type')
            ->where('job_deadline', '>=', Carbon::now())
            ->where('company_id', $companyId)
            ->where('status', 1)
            ->orderBy('job_vacancy', 'DESC')
            ->countryCity()
            ->paginate($perPage);
    }
}

/** job count by date
 *
 * ======= jobCountByDate ========
 *
 * @param
 * @return Response
 */
if (! function_exists('jobCountByDate')) {

    function jobCountByDate($param)
    {
        $today = Carbon::now();
        $lastWeek = $today->copy()->subWeek();
        $lastMonth = $today->copy()->subMonth();
        $lastSixMonths = $today->copy()->subMonths(6);
        $lastYear = $today->copy()->subYear();
        if ($param == 1) {
            return Job::where('job_deadline', '>=', $today)->whereBetween('created_at', [$today, $today])->where('status', 1)->count();
        } elseif ($param == 7) {
            return Job::where('job_deadline', '>=', $today)->whereBetween('created_at', [$lastWeek, $today])->where('status', 1)->count();
        } elseif ($param == 30) {
            return Job::where('job_deadline', '>=', $today)->whereBetween('created_at', [$lastMonth, $today])->where('status', 1)->count();
        } elseif ($param == 180) {
            return Job::where('job_deadline', '>=', $today)->whereBetween('created_at', [$lastSixMonths, $today])->where('status', 1)->count();
        } elseif ($param == 365) {
            return Job::where('job_deadline', '>=', $today)->whereBetween('created_at', [$lastYear, $today])->where('status', 1)->count();
        }
    }
}

/**========= job Count By Salery
 *
 *============= jobCountBySalery ==========
 *
 * @param  minSalery
 * @param  maxSalery
 * @return Response
 */

if (! function_exists('jobCountBySalery')) {

    function jobCountBySalery($minSalery, $maxSalery)
    {
        return Job::withWhereHas('company', function ($query) {
            $query->withWhereHas('user', function ($query) {
                $query->where('status', 1);
                $query->where('is_email_verified', 1);
            });
            $query->where('status', 1);
        })
            ->where('job_deadline', '>=', Carbon::now())
            ->where('status', 1)
            ->whereBetween('min_salary', [$minSalery,  $maxSalery])
            ->orWhere(function ($query) use ($minSalery, $maxSalery) {
                $query->whereBetween('max_salary', [$minSalery,  $maxSalery]);
            })
            ->count();
    }
}

/** Company get by  companyType
 *
 * ======= companyTypeWithCompany ========
 *
 * @return Response
 */
if (! function_exists('companyTypeWithCompany')) {
    function companyTypeWithCompany()
    {
        return CompanyType::with(['companies' => function ($query) {
            $query->withWhereHas('user', function ($query) {
                $query->where('is_email_verified', 1)
                    ->where('status', 1);
            });
            $query->select('id', 'company_type_id', 'user_id', 'status', 'company_name', 'slug')
                ->owerVerify()
                ->where('status', 1);
        }])
            ->where('status', 1)
            ->get();
    }
}

/** Company get by location
 *
 * ======= locationWithCompany ========
 *
 * @return Response
 */
if (! function_exists('locationWithCompany')) {
    function locationWithCompany()
    {
        return City::with(['companies' => function ($query) {
            $query->withWhereHas('user', function ($query) {
                $query->where('is_email_verified', 1)
                    ->where('status', 1);
            });
            $query->select('id', 'city_id', 'user_id', 'status', 'company_name', 'slug')
                ->owerVerify()
                ->where('status', 1);
        }])
            ->country()
            ->where('status', 1)
            ->get();
    }
}

function facebookFormattter($digit)
{
    if ($digit >= 1000000000) {
        return round($digit / 1000000000, 1).'G';
    }
    if ($digit >= 1000000) {
        return round($digit / 1000000, 1).'M';
    }
    if ($digit >= 1000) {
        return round($digit / 1000, 1).'K';
    }

    return $digit;
}

/** Job match by candidate  Profession
 *============ jobByProfession ============
 *
 *
 * @return response
 */
if (! function_exists('jobByProfession')) {

    function jobByProfession($limit = null, $perPage = null)
    {
        $jobs = Job::withWhereHas('company', function ($query) {
            $query->withWhereHas('user', function ($query) {
                $query->where('status', 1);
                $query->where('is_email_verified', 1);
            });
            $query->where('status', 1);
            $query->select('id', 'company_name', 'slug', 'status');
        })
            ->where('job_role_id', authCheck()->candidate->job_role_id)
            ->where('status', 1)
            ->where('job_deadline', '>=', now())
            ->select('id', 'job_title', 'slug', 'job_image', 'company_id', 'job_deadline', 'created_at');

        if (isset($limit) || isset($perPage)) {
            return $limit ? $jobs->limit($limit)->get() : $jobs->paginate($perPage);
        } else {
            return $jobs->get();
        }
    }
}

/** Job match by Faq
 *============ Faq ============
 *
 *
 * @return response
 */
if (! function_exists('faq')) {

    function faq($limit = null, $perPage = null)
    {
        $jobs = Faq::where('status', 1);
        if (isset($limit) || isset($perPage)) {
            return $limit ? $jobs->limit($limit)->get() : $jobs->paginate($perPage);
        } else {
            return $jobs->get();
        }
    }
}

/** Company Type
 *============ CompanyType ============
 *
 *
 * @return response
 */
if (! function_exists('CompanyType')) {
    function CompanyType()
    {
        return CompanyType::where('status', 1)->get();
    }
}

if (! function_exists('get_options')) {
    function get_options($key = 'options')
    {
        return Cache::rememberForever($key, function () {
            return ThemeOption::all()->keyBy('key');
        });
    }
}

/** GetThemeOption
 *============ getThemeOption ============
 *
 *
 * @return response
 */
if (! function_exists('getThemeOption')) {

    function getThemeOption($key , $parent_key = null) {

        $options = app('options') ?? [];
        if (empty($options)) {
            $options = get_options();
        }
        $option_name = $key;
        if ($parent_key) {
            $option_name = $parent_key;
        }
        $option = $options[$option_name] ?? null;
        $option_value = $option ? json_decode($option->value, true) : [];
        if ($parent_key && is_array($option_value) && isset($option_value[$key])) {
            return $option_value[$key];
        }
        return $option_value;
    }
}


if (! function_exists('refresh_options')) {
    function refresh_options($key = 'options')
    {
        if (Cache::has($key)) {
            Cache::forever($key, ThemeOption::all()->keyBy('key'));
        } else {
            get_options($key);
        }
    }
}


/** footer menu get
 *============ footerMenu ============
 *
 *
 * @return response
 */
if (! function_exists('footerMenu')) {
    function footerMenu()
    {
        $menu = Menu::where('name', 'Footer Menu')->first();

        return MenuItem::where('menu_id', $menu->id)->get();
    }
}

/** Currency
 *============ currency ============
 *
 *
 * @return response
 */
if (! function_exists('currencyAll')) {
    function currencyAll($status = null)
    {
        return $status ? Currency::where('status', $status)->get() : Currency::get();
    }
}

/**
 * applyJobCountByStatus
 *
 * @param  int  $jobId
 * @param  int  $companyId
 * @param  $status
 */
if (! function_exists('applyJobCountByStatus')) {

    function applyJobCountByStatus($jobId, $companyId, $status)
    {
        return ApplyJob::where(['job_id' => $jobId, 'company_id' => $companyId, 'status' => $status])->count();
    }
}

if (! function_exists('packageWithAddons')) {

    function packageWithAddons()
    {
        $package = Package::with('packageItems')->first();
        $packageAddons = PackageAddon::with('addonItems')->where('package_id', $package->id)->get();

        $data = [
            'package' => $package,
            'packageAddons' => $packageAddons,
        ];

        return $data;
    }
}

/**
 * randString
 *
 * @param  int  $lenght
 */
if (! function_exists('randString')) {
    function randString($length)
    {
        return substr(str_shuffle(str_repeat($x = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length / strlen($x)))), 1, $length);
    }
}

if (! function_exists('allJobs')) {

    function allJobs($perPage)
    {

        return Job::withWhereHas('company', function ($query) {
            $query->withWhereHas('user', function ($query) {
                $query->where('status', 1);
                $query->where('is_email_verified', 1);
            });
            $query->where('status', 1);
            $query->select('id', 'company_name', 'slug', 'status');
        })
            ->where('job_deadline', '>=', Carbon::now())
            ->where('status', 1)
            ->select('id', 'job_title', 'slug', 'company_id', 'job_deadline', 'job_vacancy', 'job_image', 'salary_mode', 'min_salary', 'max_salary', 'salary_type')
            ->paginate($perPage);
    }
}

if (! function_exists('getWidgetContent')) {
    function getWidgetContent($pageId, $widgetName)
    {
        return WidgetsContents::where(['page_id' => $pageId, 'widget_slug' => $widgetName])->first();
    }
}

if (! function_exists('allCompany')) {

    function allCompany($perPage)
    {

        return Company::withWhereHas('user', function ($query) {
            $query->where('status', 1);
            $query->where('is_email_verified', 1)
                ->select('id', 'status', 'is_email_verified');
        })
            ->with('jobs', function ($q) {
                $q->where('job_deadline', '>=', now())->where('status', 1)->select('id', 'company_id', 'job_deadline', 'job_vacancy');
            })
            ->select('id', 'company_name', 'company_type_id', 'user_id', 'company_logo', 'slug', 'status')
            ->countryCity()
            ->withCount('jobs')
            ->withSum('jobs', 'job_vacancy')
            ->orderBy('jobs_sum_job_vacancy', 'desc')
            ->where('status', 1)
            ->paginate($perPage);
    }
}

if (! function_exists('CountLiveJobs')) {
    function CountLiveJobs()
    {
        return Job::where('job_deadline', '>=', Carbon::now())->count('id');
    }
}

if (! function_exists('CountCompany')) {
    function CountCompany()
    {
        return Company::count('id');
    }
}

if (! function_exists('CountCandidate')) {
    function CountCandidate()
    {
        return Candidate::count('id');
    }
}

if (! function_exists('CountNewJobs')) {
    function CountNewJobs()
    {
        return Job::where('job_deadline', '>=', Carbon::now()->copy()->subWeek())->count('id');
    }
}

if (! function_exists('CountBlogComment')) {
    function CountBlogComment($param)
    {
        return BlogComment::where('blog_id', $param)->count();
    }
}

if (! function_exists('countCategoryJobs')) {
    function countCategoryJobs($param)
    {
        return Job::where('category_id', $param)->where('job_deadline', '>=', Carbon::now())->count();
    }
}

if (! function_exists('LanguageImage')) {
    function LanguageImage()
    {
        return 'storage/lang/flags/';
    }
}

if (! function_exists('Category')) {
    function Category()
    {
        return Category::where('status', 1)->get();
    }
}

if (! function_exists('CompanyGalleryImage')) {
    function CompanyGalleryImageShow()
    {
        return 'company/galleries';
    }
}

if (! function_exists('CompanyImage')) {
    function CompanyImage()
    {
        return 'company';
    }
}

if (! function_exists('countAllAppliedList')) {
    function countAllAppliedList($jobId)
    {
        $countAppliedJob = \App\Models\Front\ApplyJob::where('job_id', $jobId)->get();

        return count($countAppliedJob);
    }
}

if (! function_exists('countStatusWize')) {
    function countStatusWize($jobId, $status)
    {
        return \App\Models\Front\ApplyJob::where(['job_id' => $jobId, 'status' => $status])->count();
    }
}

if (! function_exists('BlogCategoryShow')) {
    function BlogCategoryShow()
    {
        return BlogCategory::get();
    }
}

if (! function_exists('CountCategoryBlog')) {
    function CountCategoryBlog($param)
    {
        return Blog::where('blog_category_id', $param)->count();
    }
}

/**
 * alreadyInstalled
 *
 * @return response
 */
if (! function_exists('alreadyInstalled')) {

    function alreadyInstalled()
    {
        return file_exists(storage_path('installed'));
    }
}

/**
 * indexFile
 *
 * @return Response
 */
if (! function_exists('indexFile')) {
    function indexFile()
    {
        if (File::exists(public_path('index.php'))) {
            return true;
        }

        return false;
    }
}

/**
 *  dotZeroRemovePrice
 */
if (! function_exists('dotZeroRemovePrice')) {
    function dotZeroRemovePrice($price)
    {

        return preg_replace('/[\.,]0{2}$/', '', $price);
    }
}
