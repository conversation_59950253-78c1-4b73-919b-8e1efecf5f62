{"__meta": {"id": "01JXYN1B4BCMFQ67GBZQ10V31P", "datetime": "2025-06-17 09:55:28", "utime": **********.524527, "method": "POST", "uri": "/admin/theme-option/image-upload-file", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.216637, "end": **********.524548, "duration": 0.3079111576080322, "duration_str": "308ms", "measures": [{"label": "Booting", "start": **********.216637, "relative_start": 0, "end": **********.462663, "relative_end": **********.462663, "duration": 0.*****************, "duration_str": "246ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.462673, "relative_start": 0.*****************, "end": **********.52455, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "61.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.473645, "relative_start": 0.*****************, "end": **********.477145, "relative_end": **********.477145, "duration": 0.0034999847412109375, "duration_str": "3.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.521522, "relative_start": 0.****************, "end": **********.521952, "relative_end": **********.521952, "duration": 0.0004298686981201172, "duration_str": "430μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/theme-option/image-upload-file", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "controller": "App\\Http\\Controllers\\Backend\\ThemeOptionController@imageUpload<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FThemeOptionController.php&line=65\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.theme.image.upload", "prefix": "admin/theme-option", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FThemeOptionController.php&line=65\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/ThemeOptionController.php:65-78</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00098, "accumulated_duration_str": "980μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.4906268, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 41.837}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": "middleware", "name": "prevent-back-history", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Middleware\\PreventBackHistory.php", "line": 19}], "start": **********.500124, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 41.837, "width_percent": 58.163}]}, "models": {"data": {"App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/frontendassets/images/icon/apply-ellipse.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/theme-option\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/theme-option/image-upload-file", "action_name": "admin.theme.image.upload", "controller_action": "App\\Http\\Controllers\\Backend\\ThemeOptionController@imageUpload", "uri": "POST admin/theme-option/image-upload-file", "controller": "App\\Http\\Controllers\\Backend\\ThemeOptionController@imageUpload<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FThemeOptionController.php&line=65\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/theme-option", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FThemeOptionController.php&line=65\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/ThemeOptionController.php:65-78</a>", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "duration": "311ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-124142486 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-124142486\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1273027843 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"69283 characters\">data:image/webp;base64,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</span>\"\n  \"<span class=sf-dump-key>old_file</span>\" => \"<span class=sf-dump-str title=\"20 characters\">egens-owWOseA9Oh.svg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273027843\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1851749844 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">73657</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/theme-option</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6Im90UXFhK1UvZnUvN0cxV0VLU1JFeEE9PSIsInZhbHVlIjoiT0V0UjN0T2VqNUsyL0tRN3I4VXdOcUl1U1dBZEVCYlJXK0NwRWduYnNNZEFDck8ya0hheUVPOWpJd0ZjTUduMUtnTUI4dmROdGlCM1dJYXBYeWJyQm5vdTRaMmNxanhnV3p4ZDduMVhOL2FINWJTTkMzQ05EZGRGazNVZGFyV2giLCJtYWMiOiIwZjhmZTliMjY1MGQ0YzUxOWI0OTJkMmZjYWMyYzVjNjM1OGI4MDQ0NmIyNzgxNGJlZjRkMzUzMWU1NjBjMWUzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRuOTdsWUhSWERWVy9iSkZKcjlHVFE9PSIsInZhbHVlIjoidVpTeld4enA2elZPSkRhWEw5am9yNElaSmdoU3JKS1FqbFEzZlozOWxRU09FT3NlOUVSdDduTENEQkhxc3NCaHo3L3gwRXgzV2lDbm9vbjRXKy94MkViT05JeEwvRm9KWEhMRkpjdmgycWdKZ25vdHY3Nnl0TlpYczVubFYvd0UiLCJtYWMiOiJlYjljYjRmMjU1NTZhOWMxYmVlYzdkNjQ4OTc0MDE5NjVlMGJjM2U1MDI3NDZiZjE4ZTJkYmRkNjA0MTQzNmU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851749844\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1154714873 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DuRe3UYDshvVdLe1lxmS8WfvwPktcikZxAETD6MP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154714873\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1145787525 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 09:55:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145787525\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1035110307 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">http://127.0.0.1:8000/frontendassets/images/icon/apply-ellipse.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/theme-option</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035110307\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/theme-option/image-upload-file", "action_name": "admin.theme.image.upload", "controller_action": "App\\Http\\Controllers\\Backend\\ThemeOptionController@imageUpload"}, "badge": null}}