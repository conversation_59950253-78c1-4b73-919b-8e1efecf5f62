<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\TrustedCompanyRequest;
use App\Repositories\Admin\TrustedCompanyRepository;
use Illuminate\Http\Request;

class TrustedCompanyController extends Controller
{
    public function __construct(protected TrustedCompanyRepository $trustedCompany) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->trustedCompany->content();
        }

        return view('admin.pages.trusted-company.index');
    }

    /** new Job Sub Category store
     * ========= store============
     *
     * @param App\Http\Requests\TrustedCompanyRequest
     * @return Response
     */
    public function store(TrustedCompanyRequest $request)
    {
        if (!$this->hasPermissions(['trustedcompany.add', 'trustedcompany.edit'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->trustedCompany->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $trustedCompany = $this->trustedCompany->getById($id);
        return response()->json($trustedCompany);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {

        if (!$this->hasPermissions(['trustedcompany.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->trustedCompany->destroy($id);
        return $this->formatResponse($result);

    }

    /** sub category status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['trustedcompany.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->trustedCompany->statusChange($id);
        return $this->formatResponse($result);
    }
}
