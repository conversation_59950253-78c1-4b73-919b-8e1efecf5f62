<script>
    (function($) {

        "use strict";

        let table = $('#datatable').DataTable({
            processing: true,
            serverSide: true,
            destroy: true,
            ajax: {
                url: "<?php echo e(route('admin.candidate')); ?>",
            },
            columns: [{
                    data: 'Candidate',
                    name: 'Candidate'
                },
                {
                    data: 'Profession',
                    name: 'Profession'
                },
                {
                    data: 'Role/Position',
                    name: 'Role/Position'
                },
                {
                    data: 'Experience',
                    name: 'Experience'
                },
                {
                    data: 'Joining Date',
                    name: 'Joining Date'
                },
                {
                    data: 'Verified',
                    name: 'Verified'
                },

                {
                    data: 'Status',
                    name: 'Status'
                },


                {
                    data: 'action',
                    name: 'action',
                    orderable: false
                }
            ]
        });


        $(document).on('click', '.deleteCandidate', function(e) {
            e.preventDefault();
            var id = $(this).data('id');

            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '/admin/candidate/delete/' + id,
                        type: "GET",
                        dataType: "JSON",
                        success: function(data) {
                            table.draw();
                            if (data.hasOwnProperty('message')) {
                                Swal.fire(
                                    'error!',
                                    `${data.message}`,
                                )
                            } else {
                                Swal.fire(
                                    'Deleted!',
                                    'Your file has been deleted.',
                                    'success'
                                )
                            }

                        },
                    });
                    Swal.fire(
                        'Deleted!',
                        'Your file has been deleted.',
                        'success'
                    )
                } else if (
                    /* Read more about handling dismissals below */
                    result.dismiss === Swal.DismissReason.cancel
                ) {
                    Swal.fire(
                        'Cancelled',
                        'Your file is safe :)',
                        'error'
                    )
                }
            })
        });

        //status

        $(document).on('change', '.status-change', function(e) {
            e.preventDefault();
            let action = $(this).data('action');
            $.ajax({
                url: action,
                type: "GET",
                dataType: "JSON",
                success: function(data) {
                    if (data.status === true) {
                        toastr["success"](`${data.message}`);
                        table.draw();
                    } else if (data.status == false) {
                        toastr["error"](`${data.message}`);
                    }
                },
            });
        });
    })(jQuery);
</script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/candidate/candidate.blade.php ENDPATH**/ ?>