{"__meta": {"id": "01JXYNR3RKFE0CTHZQR0Z5RQ98", "datetime": "2025-06-17 10:07:54", "utime": **********.644727, "method": "GET", "uri": "/admin/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.08252, "end": **********.644747, "duration": 0.5622270107269287, "duration_str": "562ms", "measures": [{"label": "Booting", "start": **********.08252, "relative_start": 0, "end": **********.313393, "relative_end": **********.313393, "duration": 0.*****************, "duration_str": "231ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.313416, "relative_start": 0.**************, "end": **********.644749, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.322843, "relative_start": 0.*****************, "end": **********.325343, "relative_end": **********.325343, "duration": 0.002499818801879883, "duration_str": "2.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.398416, "relative_start": 0.*****************, "end": **********.642486, "relative_end": **********.642486, "duration": 0.*****************, "duration_str": "244ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "1x admin.pages.dashboard", "param_count": null, "params": [], "start": **********.400544, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.phpadmin.pages.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.dashboard"}, {"name": "1x js.admin.dashboard", "param_count": null, "params": [], "start": **********.495431, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/admin/dashboard.blade.phpjs.admin.dashboard", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Fadmin%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.admin.dashboard"}, {"name": "1x admin.master", "param_count": null, "params": [], "start": **********.495893, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/master.blade.phpadmin.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.master"}, {"name": "1x admin.includes.head", "param_count": null, "params": [], "start": **********.49622, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/head.blade.phpadmin.includes.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.includes.head"}, {"name": "1x admin.includes.header", "param_count": null, "params": [], "start": **********.497826, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/header.blade.phpadmin.includes.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.includes.header"}, {"name": "1x admin.includes.sidebar", "param_count": null, "params": [], "start": **********.505601, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/sidebar.blade.phpadmin.includes.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.includes.sidebar"}, {"name": "1x admin.includes.script", "param_count": null, "params": [], "start": **********.631425, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/script.blade.phpadmin.includes.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.includes.script"}, {"name": "1x js.translate", "param_count": null, "params": [], "start": **********.631811, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/translate.blade.phpjs.translate", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftranslate.blade.php&line=1", "ajax": false, "filename": "translate.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.translate"}]}, "route": {"uri": "GET admin/dashboard", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "controller": "App\\Http\\Controllers\\Backend\\DashboardController@adminDashboard<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.dashboard", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/DashboardController.php:23-38</a>"}, "queries": {"count": 53, "nb_statements": 53, "nb_visible_statements": 53, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.033800000000000004, "accumulated_duration_str": "33.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.3383338, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 1.243}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": "middleware", "name": "prevent-back-history", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Middleware\\PreventBackHistory.php", "line": 19}], "start": **********.3468509, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 1.243, "width_percent": 1.805}, {"sql": "select * from `companies` order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 29}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.349969, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:76", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=76", "ajax": false, "filename": "DashboardRepostitory.php", "line": "76"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 3.047, "width_percent": 3.166}, {"sql": "select * from `company_types` where `company_types`.`id` in (1, 2, 3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 76}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 29}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.354314, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:76", "source": {"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=76", "ajax": false, "filename": "DashboardRepostitory.php", "line": "76"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 6.213, "width_percent": 1.893}, {"sql": "select * from `candidates` order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 58}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 30}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.356488, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:58", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=58", "ajax": false, "filename": "DashboardRepostitory.php", "line": "58"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 8.107, "width_percent": 3.787}, {"sql": "select * from `job_roles` where `job_roles`.`id` in (1, 4, 5, 6, 7, 8, 16, 17, 18, 21, 22, 26, 34)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 58}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.359715, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:58", "source": {"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=58", "ajax": false, "filename": "DashboardRepostitory.php", "line": "58"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 11.893, "width_percent": 1.686}, {"sql": "select * from `jobs` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 139}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 31}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3623762, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:139", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=139", "ajax": false, "filename": "DashboardRepostitory.php", "line": "139"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 13.58, "width_percent": 3.402}, {"sql": "select * from `jobs` where `status` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 111}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 32}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3647928, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:111", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=111", "ajax": false, "filename": "DashboardRepostitory.php", "line": "111"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 16.982, "width_percent": 1.775}, {"sql": "select * from `jobs` order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 96}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.36641, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:96", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=96", "ajax": false, "filename": "DashboardRepostitory.php", "line": "96"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 18.757, "width_percent": 2.692}, {"sql": "select * from `job_roles` where `job_roles`.`id` in (1, 26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 96}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3688369, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:96", "source": {"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=96", "ajax": false, "filename": "DashboardRepostitory.php", "line": "96"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 21.45, "width_percent": 1.598}, {"sql": "select * from `jobs` where `job_featured` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 125}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 34}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3704321, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:125", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=125", "ajax": false, "filename": "DashboardRepostitory.php", "line": "125"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 23.047, "width_percent": 2.367}, {"sql": "select sum(`total_price`) as aggregate from `subscriptions` where `payment_status` = 'Paid'", "type": "query", "params": [], "bindings": ["Paid"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 167}, {"index": 17, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 35}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3724172, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:167", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=167", "ajax": false, "filename": "DashboardRepostitory.php", "line": "167"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 25.414, "width_percent": 1.331}, {"sql": "select count(*) as aggregate from `subscriptions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 198}, {"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 36}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.373773, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:198", "source": {"index": 19, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=198", "ajax": false, "filename": "DashboardRepostitory.php", "line": "198"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 26.746, "width_percent": 1.036}, {"sql": "select sum(total_price) as sums, DATE_FORMAT(created_at,'%M %Y') as months from `subscriptions` group by `months`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 213}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 37}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.375618, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:213", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=213", "ajax": false, "filename": "DashboardRepostitory.php", "line": "213"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 27.781, "width_percent": 1.775}, {"sql": "select DATE_FORMAT(created_at,'%M') as months from `subscriptions` group by `months`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 223}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 38}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.377502, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:223", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 223}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=223", "ajax": false, "filename": "DashboardRepostitory.php", "line": "223"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 29.556, "width_percent": 2.041}, {"sql": "select count(*) as total, DATE_FORMAT(created_at,'%M %Y') as months from `companies` group by `months`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 233}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 39}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3799362, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:233", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 233}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=233", "ajax": false, "filename": "DashboardRepostitory.php", "line": "233"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 31.598, "width_percent": 2.367}, {"sql": "select * from `users` order by `created_at` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 181}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.382056, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:181", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=181", "ajax": false, "filename": "DashboardRepostitory.php", "line": "181"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 33.964, "width_percent": 1.864}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (418, 429, 431, 432, 433, 434) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 181}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 32}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.384119, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:181", "source": {"index": 19, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=181", "ajax": false, "filename": "DashboardRepostitory.php", "line": "181"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 35.828, "width_percent": 2.041}, {"sql": "select * from `companies` order by `created_at` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 76}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3861048, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:76", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=76", "ajax": false, "filename": "DashboardRepostitory.php", "line": "76"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 37.87, "width_percent": 1.686}, {"sql": "select * from `company_types` where `company_types`.`id` in (1, 8, 12, 15, 20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 76}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 33}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.38776, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:76", "source": {"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=76", "ajax": false, "filename": "DashboardRepostitory.php", "line": "76"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 39.556, "width_percent": 1.479}, {"sql": "select * from `candidates` order by `created_at` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 58}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.389385, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:58", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=58", "ajax": false, "filename": "DashboardRepostitory.php", "line": "58"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 41.036, "width_percent": 1.953}, {"sql": "select * from `job_roles` where `job_roles`.`id` in (16, 21, 34)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 58}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 34}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.391135, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:58", "source": {"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=58", "ajax": false, "filename": "DashboardRepostitory.php", "line": "58"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 42.988, "width_percent": 1.479}, {"sql": "select `subscriptions`.*, (select `payment_method` from `payments` where `subscription_id` = `subscriptions`.`id`) as `payment_method` from `subscriptions` order by `created_at` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 196}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.393049, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:196", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=196", "ajax": false, "filename": "DashboardRepostitory.php", "line": "196"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 44.467, "width_percent": 1.893}, {"sql": "select * from `companies` where `companies`.`id` in (63, 66, 70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 196}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/DashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\DashboardController.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.395329, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "DashboardRepostitory.php:196", "source": {"index": 20, "namespace": null, "name": "app/Repositories/Admin/DashboardRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\DashboardRepostitory.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FDashboardRepostitory.php&line=196", "ajax": false, "filename": "DashboardRepostitory.php", "line": "196"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 46.361, "width_percent": 5.03}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 65 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 214}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.439405, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 51.391, "width_percent": 1.716}, {"sql": "select * from `users` where `users`.`id` = 271 limit 1", "type": "query", "params": [], "bindings": [271], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4409251, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:215", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=215", "ajax": false, "filename": "dashboard.blade.php", "line": "215"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 53.107, "width_percent": 1.391}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 20 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.44269, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 54.497, "width_percent": 1.538}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 64 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 214}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.445224, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 56.036, "width_percent": 1.864}, {"sql": "select * from `users` where `users`.`id` = 265 limit 1", "type": "query", "params": [], "bindings": [265], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.44684, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:215", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=215", "ajax": false, "filename": "dashboard.blade.php", "line": "215"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 57.899, "width_percent": 1.509}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 8 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.448395, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 59.408, "width_percent": 1.568}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 63 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 214}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.450312, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 60.976, "width_percent": 1.627}, {"sql": "select * from `users` where `users`.`id` = 264 limit 1", "type": "query", "params": [], "bindings": [264], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.451865, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:215", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=215", "ajax": false, "filename": "dashboard.blade.php", "line": "215"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 62.604, "width_percent": 1.598}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 1 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.4538891, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 64.201, "width_percent": 2.041}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 62 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 214}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.456263, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.243, "width_percent": 2.189}, {"sql": "select * from `users` where `users`.`id` = 263 limit 1", "type": "query", "params": [], "bindings": [263], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.458318, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:215", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=215", "ajax": false, "filename": "dashboard.blade.php", "line": "215"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 68.432, "width_percent": 1.716}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 60 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 214}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.4606378, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 70.148, "width_percent": 2.337}, {"sql": "select * from `users` where `users`.`id` = 257 limit 1", "type": "query", "params": [], "bindings": [257], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4630551, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:215", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=215", "ajax": false, "filename": "dashboard.blade.php", "line": "215"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 72.485, "width_percent": 1.982}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 15 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.4648352, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 74.467, "width_percent": 1.923}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 59 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 214}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.466815, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 76.391, "width_percent": 1.598}, {"sql": "select * from `users` where `users`.`id` = 256 limit 1", "type": "query", "params": [], "bindings": [256], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.468214, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:215", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 215}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=215", "ajax": false, "filename": "dashboard.blade.php", "line": "215"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.988, "width_percent": 1.479}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 12 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.469635, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 79.467, "width_percent": 1.509}, {"sql": "select * from `users` where `users`.`id` = 434 limit 1", "type": "query", "params": [], "bindings": [434], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4789112, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:275", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=275", "ajax": false, "filename": "dashboard.blade.php", "line": "275"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.976, "width_percent": 3.018}, {"sql": "select * from `professions` where `professions`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 282}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.481528, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:282", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 282}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=282", "ajax": false, "filename": "dashboard.blade.php", "line": "282"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 83.994, "width_percent": 1.538}, {"sql": "select * from `users` where `users`.`id` = 433 limit 1", "type": "query", "params": [], "bindings": [433], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.483175, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:275", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=275", "ajax": false, "filename": "dashboard.blade.php", "line": "275"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 85.533, "width_percent": 1.45}, {"sql": "select * from `users` where `users`.`id` = 432 limit 1", "type": "query", "params": [], "bindings": [432], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.484765, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:275", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=275", "ajax": false, "filename": "dashboard.blade.php", "line": "275"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 86.982, "width_percent": 1.42}, {"sql": "select * from `users` where `users`.`id` = 431 limit 1", "type": "query", "params": [], "bindings": [431], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.486303, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:275", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=275", "ajax": false, "filename": "dashboard.blade.php", "line": "275"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 88.402, "width_percent": 1.331}, {"sql": "select * from `professions` where `professions`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 282}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.487607, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:282", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 282}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=282", "ajax": false, "filename": "dashboard.blade.php", "line": "282"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 89.734, "width_percent": 1.331}, {"sql": "select * from `users` where `users`.`id` = 386 limit 1", "type": "query", "params": [], "bindings": [386], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.489156, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:275", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=275", "ajax": false, "filename": "dashboard.blade.php", "line": "275"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 91.065, "width_percent": 1.45}, {"sql": "select * from `professions` where `professions`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 282}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4905112, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:282", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 282}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=282", "ajax": false, "filename": "dashboard.blade.php", "line": "282"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 92.515, "width_percent": 1.331}, {"sql": "select * from `users` where `users`.`id` = 283 limit 1", "type": "query", "params": [], "bindings": [283], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.492037, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:275", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=275", "ajax": false, "filename": "dashboard.blade.php", "line": "275"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 93.846, "width_percent": 1.42}, {"sql": "select * from `professions` where `professions`.`id` = 35 limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 282}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.493381, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "admin.pages.dashboard:282", "source": {"index": 21, "namespace": "view", "name": "admin.pages.dashboard", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/dashboard.blade.php", "line": 282}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fdashboard.blade.php&line=282", "ajax": false, "filename": "dashboard.blade.php", "line": "282"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 95.266, "width_percent": 1.331}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "admin.includes.header", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/header.blade.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.499758, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "admin.includes.header:40", "source": {"index": 16, "namespace": "view", "name": "admin.includes.header", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/header.blade.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fheader.blade.php&line=40", "ajax": false, "filename": "header.blade.php", "line": "40"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 96.598, "width_percent": 1.568}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": [1, "App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.514295, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 98.166, "width_percent": 1.834}]}, "models": {"data": {"App\\Models\\Admin\\Job": {"value": 77, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Admin\\Candidate": {"value": 56, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=1", "ajax": false, "filename": "Candidate.php", "line": "?"}}, "App\\Models\\Admin\\Company": {"value": 44, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Front\\Subscription": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FFront%2FSubscription.php&line=1", "ajax": false, "filename": "Subscription.php", "line": "?"}}, "App\\Models\\Admin\\CompanyType": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=1", "ajax": false, "filename": "CompanyType.php", "line": "?"}}, "App\\Models\\Admin\\CompanyTypeTranslation": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyTypeTranslation.php&line=1", "ajax": false, "filename": "CompanyTypeTranslation.php", "line": "?"}}, "App\\Models\\Admin\\JobRole": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJobRole.php&line=1", "ajax": false, "filename": "JobRole.php", "line": "?"}}, "App\\Models\\User": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin\\Profession": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FProfession.php&line=1", "ajax": false, "filename": "Profession.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}}, "count": 299, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 35, "messages": [{"message": "[\n  ability => dashoard.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-75057368 data-indent-pad=\"  \"><span class=sf-dump-note>dashoard.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">dashoard.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75057368\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.517622, "xdebug_link": null}, {"message": "[\n  ability => subscription.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1346831211 data-indent-pad=\"  \"><span class=sf-dump-note>subscription.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">subscription.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346831211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.519528, "xdebug_link": null}, {"message": "[\n  ability => subscription.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1615170806 data-indent-pad=\"  \"><span class=sf-dump-note>subscription.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">subscription.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615170806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.520708, "xdebug_link": null}, {"message": "[\n  ability => companytype.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1367967156 data-indent-pad=\"  \"><span class=sf-dump-note>companytype.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">companytype.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367967156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.522658, "xdebug_link": null}, {"message": "[\n  ability => company.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1372909727 data-indent-pad=\"  \"><span class=sf-dump-note>company.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1372909727\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.523801, "xdebug_link": null}, {"message": "[\n  ability => candidate.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>candidate.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">candidate.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.524955, "xdebug_link": null}, {"message": "[\n  ability => jobcategory.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1395286655 data-indent-pad=\"  \"><span class=sf-dump-note>jobcategory.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">jobcategory.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395286655\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527432, "xdebug_link": null}, {"message": "[\n  ability => jobcategory.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-50053322 data-indent-pad=\"  \"><span class=sf-dump-note>jobcategory.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">jobcategory.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-50053322\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529808, "xdebug_link": null}, {"message": "[\n  ability => jobsubcategory.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-435011059 data-indent-pad=\"  \"><span class=sf-dump-note>jobsubcategory.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">jobsubcategory.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435011059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.53138, "xdebug_link": null}, {"message": "[\n  ability => jobtype.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-391867092 data-indent-pad=\"  \"><span class=sf-dump-note>jobtype.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">jobtype.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391867092\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.532784, "xdebug_link": null}, {"message": "[\n  ability => jobskill.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1855548227 data-indent-pad=\"  \"><span class=sf-dump-note>jobskill.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">jobskill.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855548227\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.534207, "xdebug_link": null}, {"message": "[\n  ability => jobrole.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1820366243 data-indent-pad=\"  \"><span class=sf-dump-note>jobrole.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">jobrole.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820366243\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535658, "xdebug_link": null}, {"message": "[\n  ability => jobexperience.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-409074444 data-indent-pad=\"  \"><span class=sf-dump-note>jobexperience.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">jobexperience.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409074444\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.537078, "xdebug_link": null}, {"message": "[\n  ability => languagelevel.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-994793629 data-indent-pad=\"  \"><span class=sf-dump-note>languagelevel.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">languagelevel.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994793629\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.538532, "xdebug_link": null}, {"message": "[\n  ability => careerlevel.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-576947369 data-indent-pad=\"  \"><span class=sf-dump-note>careerlevel.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">careerlevel.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576947369\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.540037, "xdebug_link": null}, {"message": "[\n  ability => profession.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1576069336 data-indent-pad=\"  \"><span class=sf-dump-note>profession.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">profession.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576069336\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541554, "xdebug_link": null}, {"message": "[\n  ability => education.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1510717453 data-indent-pad=\"  \"><span class=sf-dump-note>education.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">education.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510717453\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543091, "xdebug_link": null}, {"message": "[\n  ability => package.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-245276063 data-indent-pad=\"  \"><span class=sf-dump-note>package.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-245276063\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.546901, "xdebug_link": null}, {"message": "[\n  ability => user.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1029214248 data-indent-pad=\"  \"><span class=sf-dump-note>user.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029214248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54958, "xdebug_link": null}, {"message": "[\n  ability => permission.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-561061377 data-indent-pad=\"  \"><span class=sf-dump-note>permission.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">permission.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-561061377\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.551265, "xdebug_link": null}, {"message": "[\n  ability => role.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-645769021 data-indent-pad=\"  \"><span class=sf-dump-note>role.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645769021\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.552906, "xdebug_link": null}, {"message": "[\n  ability => themeoption.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1424562792 data-indent-pad=\"  \"><span class=sf-dump-note>themeoption.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">themeoption.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424562792\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.555389, "xdebug_link": null}, {"message": "[\n  ability => emailtemplate.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1478389544 data-indent-pad=\"  \"><span class=sf-dump-note>emailtemplate.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">emailtemplate.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478389544\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.557209, "xdebug_link": null}, {"message": "[\n  ability => menu.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2053925171 data-indent-pad=\"  \"><span class=sf-dump-note>menu.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">menu.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053925171\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.558909, "xdebug_link": null}, {"message": "[\n  ability => page.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-7357365 data-indent-pad=\"  \"><span class=sf-dump-note>page.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">page.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7357365\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.592669, "xdebug_link": null}, {"message": "[\n  ability => blogcategory.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1766824064 data-indent-pad=\"  \"><span class=sf-dump-note>blogcategory.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">blogcategory.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766824064\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.597492, "xdebug_link": null}, {"message": "[\n  ability => blog.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1929158094 data-indent-pad=\"  \"><span class=sf-dump-note>blog.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">blog.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929158094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.599093, "xdebug_link": null}, {"message": "[\n  ability => testimonial.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-62875620 data-indent-pad=\"  \"><span class=sf-dump-note>testimonial.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">testimonial.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62875620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.60075, "xdebug_link": null}, {"message": "[\n  ability => trustedcompany.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>trustedcompany.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">trustedcompany.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.602366, "xdebug_link": null}, {"message": "[\n  ability => faq.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2025473402 data-indent-pad=\"  \"><span class=sf-dump-note>faq.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">faq.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025473402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.604088, "xdebug_link": null}, {"message": "[\n  ability => language.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-876066231 data-indent-pad=\"  \"><span class=sf-dump-note>language.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876066231\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.605793, "xdebug_link": null}, {"message": "[\n  ability => country.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1667238671 data-indent-pad=\"  \"><span class=sf-dump-note>country.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">country.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1667238671\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.607782, "xdebug_link": null}, {"message": "[\n  ability => state.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1366667311 data-indent-pad=\"  \"><span class=sf-dump-note>state.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">state.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1366667311\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.626635, "xdebug_link": null}, {"message": "[\n  ability => city.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1707809804 data-indent-pad=\"  \"><span class=sf-dump-note>city.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">city.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707809804\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.628621, "xdebug_link": null}, {"message": "[\n  ability => backendsetting.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1711675574 data-indent-pad=\"  \"><span class=sf-dump-note>backendsetting.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">backendsetting.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711675574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.630336, "xdebug_link": null}]}, "session": {"_token": "joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/lang/flags/bd.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/theme-option\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/dashboard", "action_name": "admin.dashboard", "controller_action": "App\\Http\\Controllers\\Backend\\DashboardController@adminDashboard", "uri": "GET admin/dashboard", "controller": "App\\Http\\Controllers\\Backend\\DashboardController@adminDashboard<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FDashboardController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/DashboardController.php:23-38</a>", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "duration": "567ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-182592817 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-182592817\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1999372778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1999372778\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1505931255 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6IjdLbHNoV2kwVFNCcCtXb3R0WFREaGc9PSIsInZhbHVlIjoiZjZaNjBUd1FSUmUrR3IwdGxRL3g3VGRHVTNneXdOOWFYWlUvSFljb2N5ZGRkd1pSWDQ1TEJ6N0JsU1VZbGgxL1I5aUpKZ3JTejNaTmhPYk81N0VkMnVoYURjRzdNUFc4cUJDK3VXMHdaRDlwTGNUNlpWMG1PL2luK0FDcXByTWsiLCJtYWMiOiJiNzdiNWZiYjdiMjI0ZGVhNjgxNTgwZTk1MDEwNWNiYTE4Mjk1OTNiZDljYzcxYWJjZmMzZDlkZWMxZGFlODAwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im1ka1ZDeXlHRGZva2RqQzdkTGRxTFE9PSIsInZhbHVlIjoiWnIvM0VyL1FGZGZtaG5ZbW1UN0RhZzdHUHk4NXowWW5VcWZRL1pEcU9PSytyMFJoL3FrNS9Ib2VPem5Wdm5nZEY4T1lackhMbE9yNUJMeDhLMUdUMk85Uk45TmhabllaYmZwZGlKRDd6U1B3Y1JtU3RPZTRzMURPTTNmNzhqb3UiLCJtYWMiOiJjNGEwZjc2NTQ2ZjUwYTU1Y2ZjNzMwNDM1OWY5YWNhMGJmYmZlZmY3Mjg1MjEwMWE2NWRhMTkwNWQ3NzU1NDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505931255\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-158367459 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DuRe3UYDshvVdLe1lxmS8WfvwPktcikZxAETD6MP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-158367459\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1971738275 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 10:07:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971738275\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-597787661 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/storage/lang/flags/bd.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/theme-option</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597787661\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/dashboard", "action_name": "admin.dashboard", "controller_action": "App\\Http\\Controllers\\Backend\\DashboardController@adminDashboard"}, "badge": null}}