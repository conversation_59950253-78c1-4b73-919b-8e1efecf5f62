<?php if(@isset($widgetContent)): ?>
    <div class="sortable-item accordion-item allowPrimary" data-code="<?php echo e($widgetContent->ui_card_number); ?>">
        <div class="accordion-header">
            <div class="section-name"> <?php echo e($widgetContent->widget?->widget_name); ?>

                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="<?php echo e(route('admin.pages.widget.status.change', $widgetContent->id)); ?>"
                            <?php echo e($widgetContent->status == 1 ? 'checked' : ''); ?> type="checkbox" role="switch"
                            id="<?php echo e($widgetContent->id); ?>">
                        <label class="form-check-label" for="<?php echo e($widgetContent->id); ?>"> </label>
                    </div>

                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="<?php echo e($widgetContent->id); ?>">
                        <i class="bi bi-trash"></i>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse">
            <div class="accordion-body">
                <form enctype="multipart/form-data" class="form" data-action="<?php echo e(route('admin.pages.widget.save')); ?>"
                    method="POST">
                    <?php echo csrf_field(); ?>

                    <input type="hidden" name="ui_card_number" value="<?php echo e($widgetContent->ui_card_number); ?>">
                    <input type="hidden" name="page_id" value="<?php echo e($widgetContent->page_id); ?>">
                    <input type="hidden" name="widget_slug" class="widget-slug"
                        value="<?php echo e($widgetContent->widget_slug); ?>">

                    <div class="row">
                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Show Item Per Page')); ?></label>
                                <input type="number" class="username-input"
                                    placeholder="<?php echo e(translate('Show Number of item')); ?>" name="content[0][show_item]"
                                    value="<?php echo e(isset($widgetContent->widget_content['show_item']) ? $widgetContent->widget_content['show_item'] : ''); ?>">
                            </div>
                        </div>
                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Select Style')); ?></label>
                                <select name="content[0][style]" class="username-input form-control">
                                    <option value="default"
                                        <?php echo e(isset($widgetContent->widget_content['style']) ? ($widgetContent->widget_content['style'] == 'default' ? 'selected' : '') : ''); ?>>
                                        <?php echo e(translate('Default')); ?></option>
                                    <option value="standard"
                                        <?php echo e(isset($widgetContent->widget_content['style']) ? ($widgetContent->widget_content['style'] == 'standard' ? 'selected' : '') : ''); ?>>
                                        <?php echo e(translate('Standard')); ?></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow"><?php echo e(translate('Update')); ?></button>
                    </div>
                </form>
            </div>
        </div>

    </div>
<?php else: ?>
    <div class="sortable-item accordion-item allowPrimary" data-code="<?php echo e($randomId); ?>">
        <div class="accordion-header">
            <div class="section-name"> <?php echo e($widgetName); ?>

                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change" checked
                            data-action="<?php echo e(route('admin.pages.widget.status.change', $randomId)); ?>" type="checkbox"
                            role="switch" id="<?php echo e($randomId); ?>">
                        <label class="form-check-label" for="<?php echo e($randomId); ?>"> </label>
                    </div>
                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="<?php echo e($randomId); ?>">
                        <i class="bi bi-trash"></i>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse show">
            <div class="accordion-body">
                <form enctype="multipart/form-data" class="form" method="POST"
                    data-action="<?php echo e(route('admin.pages.widget.save')); ?>">
                    <?php echo csrf_field(); ?>

                    <input type="hidden" name="ui_card_number" value="<?php echo e($randomId); ?>">
                    <input type="hidden" name="page_id" value="<?php echo e($pageId); ?>">
                    <input type="hidden" name="widget_slug" class="widget-slug" value="<?php echo e($slug); ?>">

                    <div class="row">

                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Show Item Per Page')); ?></label>
                                <input type="number" class="username-input"
                                    placeholder="<?php echo e(translate('Show Number of item')); ?>" min="1"
                                    name="content[0][show_item]">
                            </div>
                        </div>
                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Select Style')); ?></label>
                                <select name="content[0][style]" class="username-input form-control">
                                    <option value="default"><?php echo e(translate('Default')); ?></option>
                                    <option value="standard"><?php echo e(translate('Standard')); ?></option>
                                </select>
                            </div>
                        </div>

                    </div>
                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow"><?php echo e(translate('Save')); ?></button>
                    </div>
                </form>
            </div>
        </div>

    </div>
<?php endif; ?>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/admin/pages/widgets/all-blog.blade.php ENDPATH**/ ?>