<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class ForgotPasswordController extends Controller
{
    public function __construct(protected UserRepository $user) {}

    /**
     * showForgetPassword
     *
     * @return view
     */
    public function showForgetPassword()
    {
        return view('front.pages.auth.forget-password');
    }

    /**
     * forgetPassword
     *
     * @param  mixed  $request
     * @return Response
     */
    public function forgetPassword(Request $request)
    {

        $request->validate([
            'email' => 'required|email|exists:users',
        ], [
            'email.required' => 'Email Field is requred',
            'email.exists' => 'Email is not found.',

        ]);

        $result = $this->user->forgetPassword($request);

        if ($result['status'] == true) {
            Toastr()->success('', $result['message'], ['positionClass' => 'toast-top-right']);
            return redirect()->back();
        } else {
            Toastr()->error('', $result['message'], ['positionClass' => 'toast-top-right']);
            return redirect()->back();

        }

    }

    /**
     * showResetPassword
     *
     * @param  string  $email
     * @param  string  $token
     * @return view
     */
    public function showResetPassword($email, $token)
    {
        return view('front.pages.auth.reset-password', compact('email', 'token'));
    }

    /**
     * resetPassword
     *
     * @param  mixed  $request
     * @return Response
     */
    public function resetPassword(Request $request)
    {

        $request->validate([
            'email' => 'required|email|exists:users',
            'password' => 'required|min:5|max:12|confirmed',
        ], [
            'email.required' => 'Email Field is requred',
            'email.exists' => 'Email is not found.',
            'password.required' => 'Password Field is required',
            'password.confirmed' => 'Password Confirmation does not match',

        ]);

        $result = $this->user->resetPassword($request);

        if ($result['status'] == true) {
            toastr()->success('', $result['message'], ['positionClass' => 'toast-top-right']);

            return redirect()->route('auth.user.login');
        } else {
            toastr()->error('', $result['message'], ['positionClass' => 'toast-top-right']);

            return redirect()->back();

        }

    }
}
