<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class TestimonialRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            //
            'name' => 'required',
            'image' => isset(Request()->id) ? (isset(Request()->image) ? 'required|image' : '') : 'required|image',
            'title' => 'required', Rule::unique('testimonials', 'title')->ignore(Request()->id),
            'rating' => 'required',
            'description' => 'required',

        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Name is required.',
            'title.required' => 'Title is required.',
            'title.unique' => 'Title Already exists.',
            'image.required' => 'Image is required',
            'image.image' => 'Image will be must image',
            'rating' => 'Rating is required.',
            'description' => 'Description is required.',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => false,
            'errors' => $validator->errors(),
        ]));
    }
}
