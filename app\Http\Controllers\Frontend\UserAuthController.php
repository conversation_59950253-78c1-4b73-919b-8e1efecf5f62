<?php

namespace App\Http\Controllers\Frontend;

use App\Classes\EmailFormat;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterCompanyRequest;
use App\Http\Requests\RegisterRequest;
use App\Models\Admin\Candidate;
use App\Models\Admin\Company;
use App\Models\Admin\CompanyType;
use App\Models\Admin\UserLoginLog;
use App\Models\User;
use App\Models\UserVerify;
use App\Repositories\CompanyRepository;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

use function PHPSTORM_META\type;

class UserAuthController extends Controller
{
    public function __construct(
        protected UserRepository $user,
        protected CompanyRepository $company,
        protected EmailFormat $emailFormat

    ) {}

    public function userLogin()
    {
        return view('front.pages.auth.login');
    }

    public function userSignUp()
    {
        return view('front.pages.auth.signup');
    }

    /** new Candidate User Register
     *=========== candidateRegister ============
     *
     * @param RegisterRequest
     * @return Response
     */
    public function candidateRegister(RegisterRequest $request)
    {
        try {
            DB::beginTransaction();
            $user = $this->user->create($request, CANDIDATE_ROLE);
            $user->assignRole('candidate');
            Candidate::create([
                'user_id' => $user->id,
            ]);

            DB::commit();
            if ($user) {
                $backendGeneral= getThemeOption('backend_general') ?? [];
                $data = [
                    'company_name' =>  $backendGeneral['application_name']?? 'Jobes',
                    'token' => $this->user->getToken(),
                ];
                $this->emailFormat->condidateVerifyMail($user, $data);
                toastr()->info('', translate('Please Verify your E-mail'), ['positionClass' => 'toast-top-right']);
                return response()->json([
                    'status' => true,
                    'type' => true,
                    'url' => route('home')
                ]);
            }

            return redirect()->back()->withInput($request->all());
        } catch (\Throwable $th) {
            //throw $th;
            // dd($th->getMessage());
        }
    }

    /** new company user Register
     *=========== companyRegister ===========
     *
     * @param RegisterCompanyRequest
     * @return Response
     */
    public function companyRegister(RegisterCompanyRequest $request)
    {
        try {

            DB::beginTransaction();

           $backendGeneral= getThemeOption('backend_general') ?? [];
            $user = $this->user->create($request, COMPANY_ROLE);
            $user->assignRole('company');
            $result = $this->company->create($request, $user->id);
            DB::commit();
            if ($user) {
                $data = [
                    'company_name' => $backendGeneral['application_name'] ?? 'Jobes',
                    'token' => $this->user->getToken(),
                ];
                $this->emailFormat->companyVerifyMail($user, $data);
                return response()->json([
                    'status' => true,
                    'message' => translate('Please Verify Your Email'),
                    'url' => route('home'),
                    'type' => true,
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * verifyAccount
     *
     * @param  mixed  $token
     * @return response
     */
    public function verifyAccount($token)
    {

        try {
            $verifyUser = UserVerify::where('token', $token)->first();

            $message =  translate('Sorry your email cannot be identified.');

            if (! is_null($verifyUser)) {
                $user = $verifyUser->user;
                if (! $user->is_email_verified) {
                    $verifyUser->user->is_email_verified = 1;
                    $verifyUser->user->status = 1;
                    if ($verifyUser->user->save()) {
                        $backendGeneral= getThemeOption('backend_general') ?? [];
                        $data = [
                            'company_name' =>  $backendGeneral['application_name'] ?? 'Jobes',
                            'token' => $this->user->getToken(),
                        ];

                        if ($verifyUser->user->hasRole('candidate')) {
                            $this->candidateStatusUpdate($verifyUser->user->id);
                            $this->emailFormat->condidateConfirmationMail($user, $data);
                        }
                        if ($verifyUser->user->hasRole('company')) {
                            $this->companyStatusUpdate($verifyUser->user->id);
                            $this->emailFormat->companyConfirmationMail($user, $data);
                        }

                        $message = translate('Your e-mail is verified. You can now login.');
                        $verifyUser->delete();
                    }
                } else {
                    $message = translate('Your e-mail is already verified. You can now login.');
                }
                toastr()->info($message);

                return redirect()->route('auth.user.login');
            }

            toastr()->info($message);

            return redirect()->route('auth.user.signup');
        } catch (\Throwable $th) {
            return view('front.pages.404');
        }
    }

    /**
     * userLoginSuccess
     *
     * @param  mixed $request
     */
    public function userLoginSuccess(Request $request)
    {
        if ($request->ajax()) {
            $validator = Validator::make($request->all(), [
                'email' => 'required',
            ], [
                'email.required' => 'Email Field is Required',
                'password.required' => 'Password Field is Required',
            ]);

            if ($validator->fails()) {
                return response()->json(['status' => false, 'message' => $validator->errors()->first()]);
            }
            $userLogin = $this->user->login($request);
            return response()->json($userLogin);
        }
        $userLogin = $this->user->login($request);

        if ($userLogin['status'] == true) {
            toastr()->success($userLogin['message']);
            return redirect($userLogin['url']);
        } else if ($userLogin['status'] == false) {
            toastr()->error($userLogin['message']);
            return redirect($userLogin['url']);
        }
    }

    /**
     * candidateLogout
     */
    public function candidateLogout()
    {
        if (Auth::check()) {
            Session::forget('candidateLogin');
            Auth::logout();
            toastr()->success('', translate('Logout Successfully'), ['positionClass' => 'toast-top-right']);

            return redirect()->route('auth.user.login');
        }

        return redirect()->back();
    }

    /**
     * companyLogout
     *
     */
    public function companyLogout()
    {
        if (Auth::check()) {
            Session::forget('companyLogin');
            Auth::logout();
            toastr()->success('', translate('Logout Successfully'), ['positionClass' => 'toast-top-right']);

            return redirect()->route('auth.user.login');
        }

        return redirect()->back();
    }

    /**
     * candidateStatusUpdate
     *
     * @param  mixed  $id
     * @return Response
     */
    public function candidateStatusUpdate($id)
    {
        $candidate = Candidate::where('user_id', $id)->first();
        $candidate->update([
            'status' => 1,
        ]);
    }

    /**
     * companyStatusUpdate
     *
     * @param  mixed  $id
     * @return Response
     */
    public function companyStatusUpdate($id)
    {

        $company = Company::where('user_id', $id)->first();
        $company->update([
            'status' => 1,
        ]);
    }

    /**
     * companyType
     *
     * @param string $name
     */
    public function companyType($name)
    {
        $pressions = CompanyType::where('company_type_name', 'like', '%' . $name . '%')->get();
        return response()->json($pressions);
    }
}
