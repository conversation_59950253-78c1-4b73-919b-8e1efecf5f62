<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Admin\Company;
use App\Models\Admin\Job;
use App\Models\Front\ApplyJob;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AppliedJobController extends Controller
{
    public function applyJob(Request $request)
    {
        if (!Auth::check()) {
            toastr()->error('Not Authorised! Please login first', '', ['positionClass' => 'toast-top-right']);
            return redirect()->back();
        }

        $extra_website_dataItem = array_map(fn($item) => [
            'title' => $item['title'],
            'link'  => $item['link'],
        ], $request->input('extra_website', []));

        $apply = ApplyJob::create([
            'user_id'      => $request->user_id,
            'candidate_id' => $request->candidate_id,
            'company_id'   => $request->company_id,
            'job_id'       => $request->job_id,
            'extra_link'   => $extra_website_dataItem,
        ]);

        if (!$apply) {
            toastr()->error('Job application failed.', '', ['positionClass' => 'toast-top-right']);
            return redirect()->back();
        }

        // Fetch notification data
        $notificationData = [
            'user_id'      => $request->user_id,
            'candidate_id' => $request->candidate_id,
            'company_id'   => $request->company_id,
            'job_id'       => $request->job_id,
            'msg'          => 'Job Apply Successfully',
            'time'         => now()->format('Y-m-d H:i:s'),
            'companyInfo'  => Company::with('user')->find($request->company_id),
            'userInfo'     => User::with('candidate')->find($request->user_id),
            'jobInfo'      => Job::find($request->job_id),
        ];

        $notifications = (new NotificationController)->getCookieData();
        $notifications[] = $notificationData;

        toastr()->success('Job Apply Successfully', '', ['positionClass' => 'toast-top-right']);

        return redirect()->back()->withCookie('notification', json_encode($notifications));

    }
}
