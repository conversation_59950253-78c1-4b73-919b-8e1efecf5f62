@if (@isset($widgetContent))
    <div class="sortable-item accordion-item allowPrimary" data-code="{{ $widgetContent->ui_card_number }}">
        <div class="accordion-header">
            <div class="section-name"> {{ $widgetContent->widget?->widget_name }}
                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="{{ route('admin.pages.widget.status.change', $widgetContent->id) }}"
                            {{ $widgetContent->status == 1 ? 'checked' : '' }} type="checkbox" role="switch"
                            id="{{ $widgetContent->id }}">
                        <label class="form-check-label" for="{{ $widgetContent->id }}"> </label>
                    </div>

                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="{{ $widgetContent->id }}">
                        <i class="bi bi-trash"></i>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse">
            <div class="accordion-body">
                @php
                $widgetContents= $widgetContent->getTranslation("widget_content",$lang);
               @endphp
                <form enctype="multipart/form-data" data-action="{{ route('admin.pages.widget.save') }}" class="form"
                    method="POST">
                    @csrf

                    <input type="hidden" name="ui_card_number" value="{{ $widgetContent->ui_card_number }}">
                    <input type="hidden" name="page_id" value="{{ $widgetContent->page_id }}">
                    <input type="hidden" name="widget_slug" class="widget-slug"
                        value="{{ $widgetContent->widget_slug }}">
                    <div class="row">
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Banner Tag') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Enter Banner Tag') }}" name="content[0][banner_tag]"
                                    value="{{ isset($widgetContents['banner_tag']) ? $widgetContents['banner_tag'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Title (HTML Allowed)') }}</label>
                                <textarea class="username-input" rows="3"
                                    placeholder="{{ translate('Enter Main Title with HTML: e.g., Discover Work <strong>That <span>Works for You</span></strong>') }}"
                                    name="content[0][title]">{{ isset($widgetContents['title']) ? $widgetContents['title'] : '' }}</textarea>
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Description') }}</label>
                                <textarea class="username-input" rows="3"
                                    placeholder="{{ translate('Enter Description') }}"
                                    name="content[0][description]">{!! isset($widgetContents['description']) ? $widgetContents['description'] : '' !!}</textarea>
                            </div>
                        </div>


                        <div class="col-sm-3 mb-2">
                            <div class="form-inner ">
                                <label>{{ translate('Keyword One (Live Jobs)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Live Jobs') }}" name="content[0][keyword_one]"
                                    value="{{ isset($widgetContents['keyword_one']) ? $widgetContents['keyword_one'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner ">
                                <label>{{ translate('Keyword Two (Companies)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Companies') }}" name="content[0][keyword_two]"
                                    value="{{ isset($widgetContents['keyword_two']) ? $widgetContents['keyword_two'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Three (Candidates)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Candidates') }}" name="content[0][keyword_three]"
                                    value="{{ isset($widgetContents['keyword_three']) ? $widgetContents['keyword_three'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Four (New Jobs)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('New Jobs') }}" name="content[0][keyword_four] "
                                    value="{{ isset($widgetContents['keyword_four']) ? $widgetContents['keyword_four'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label">{{ translate('Person Image') }} <b>(600px × 800px)</b></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p>{{ translate('Choose an image file or drag it here') }}</p>
                                    </div>
                                    <input type="file" name="content[0][person_image]" class="dropzone dropzone-image" accept="image/*">
                                </div>
                                <div class="preview-zone {{ isset($widgetContents['person_image']) && $widgetContents['person_image'] ? '' : 'hidden' }}">
                                    <div class="box box-solid">
                                        <div class="box-body">
                                            @if(isset($widgetContents['person_image']) && $widgetContents['person_image'])
                                                <div class="img-thumb-wrapper card shadow">
                                                    <span class="remove text-danger"><i class="bi bi-trash"></i></span>
                                                    <img class="img-thumb" width="100" src="{{ asset('storage/' . $widgetContents['person_image']) }}" alt="Person Image"/>
                                                </div>
                                                <input type="hidden" name="content[0][existing_person_image]" value="{{ $widgetContents['person_image'] }}">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label">{{ translate('Background Image') }} <b>(1920px × 1080px)</b></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p>{{ translate('Choose an image file or drag it here') }}</p>
                                    </div>
                                    <input type="file" name="content[0][background_image]" class="dropzone dropzone-image" accept="image/*">
                                </div>
                                <div class="preview-zone {{ isset($widgetContents['background_image']) && $widgetContents['background_image'] ? '' : 'hidden' }}">
                                    <div class="box box-solid">
                                        <div class="box-body">
                                            @if(isset($widgetContents['background_image']) && $widgetContents['background_image'])
                                                <div class="img-thumb-wrapper card shadow">
                                                    <span class="remove text-danger"><i class="bi bi-trash"></i></span>
                                                    <img class="img-thumb" width="100" src="{{ asset('storage/' . $widgetContents['background_image']) }}" alt="Background Image"/>
                                                </div>
                                                <input type="hidden" name="content[0][existing_background_image]" value="{{ $widgetContents['background_image'] }}">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch"
                                           name="content[0][show_banner_vector]" value="1"
                                           {{ (isset($widgetContents['show_banner_vector']) && $widgetContents['show_banner_vector'] == '1') ? 'checked' : 'checked' }}
                                           id="showBannerVector">
                                    <label class="form-check-label" for="showBannerVector">
                                        {{ translate('Show Banner Vector') }}
                                    </label>
                                </div>
                            </div>
                        </div>






                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow">{{ translate('Update') }}</button>
                    </div>
                </form>
            </div>
        </div>

    </div>
@else
    <div class="sortable-item accordion-item allowPrimary" data-code="{{ $randomId }}">
        <div class="accordion-header" id="herosection">
            <div class="section-name"> {{ $widgetName }}
                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="{{ route('admin.pages.widget.status.change', $randomId) }}" checked
                            type="checkbox" role="switch" id="{{ $randomId }}">
                        <label class="form-check-label" for="{{ $randomId }}"> </label>
                    </div>
                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="{{ $randomId }}">
                        <i class="bi bi-trash"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse show">
            <div class="accordion-body">
                <form enctype="multipart/form-data" data-action="{{ route('admin.pages.widget.save') }}"
                    class="form" method="POST">
                    @csrf

                    <input type="hidden" name="ui_card_number" value="{{ $randomId }}">
                    <input type="hidden" name="page_id" value="{{ $pageId }}">
                    <input type="hidden" name="widget_slug" class="widget-slug" value="{{ $slug }}">

                    <div class="row">
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Banner Tag') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Enter Banner Tag (e.g., Your Dream Job)') }}" name="content[0][banner_tag]">
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Title (HTML Allowed)') }}</label>
                                <textarea class="username-input" rows="3"
                                    placeholder="{{ translate('Enter Main Title with HTML: e.g., Discover Work <strong>That <span>Works for You</span></strong>') }}"
                                    name="content[0][title]"></textarea>
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Description') }}</label>
                                <textarea class="username-input" rows="3"
                                    placeholder="{{ translate('Enter Description') }}"
                                    name="content[0][description]"></textarea>
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner ">
                                <label>{{ translate('Keyword One (Live Jobs)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Live Jobs') }}" name="content[0][keyword_one]">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner ">
                                <label>{{ translate('Keyword Two (Companies)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Companies') }}" name="content[0][keyword_two]">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Three (Candidates)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Candidates') }}" name="content[0][keyword_three]">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Four (New Jobs)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('New Jobs') }}" name="content[0][keyword_four]">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label">{{ translate('Person Image') }} <b>(600px × 800px)</b></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p>{{ translate('Choose an image file or drag it here') }}</p>
                                    </div>
                                    <input type="file" name="content[0][person_image]" class="dropzone dropzone-image" accept="image/*">
                                </div>
                                <div class="preview-zone hidden">
                                    <div class="box box-solid">
                                        <div class="box-body"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label">{{ translate('Background Image') }} <b>(1920px × 1080px)</b></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p>{{ translate('Choose an image file or drag it here') }}</p>
                                    </div>
                                    <input type="file" name="content[0][background_image]" class="dropzone dropzone-image" accept="image/*">
                                </div>
                                <div class="preview-zone hidden">
                                    <div class="box box-solid">
                                        <div class="box-body"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch"
                                           name="content[0][show_banner_vector]" value="1" checked
                                           id="showBannerVectorNew">
                                    <label class="form-check-label" for="showBannerVectorNew">
                                        {{ translate('Show Banner Vector') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow">{{ translate('Save') }}</button>
                    </div>
                </form>
            </div>
        </div>

    </div>
@endif
