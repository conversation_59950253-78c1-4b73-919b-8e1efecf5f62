@if (@isset($widgetContent))
    <div class="sortable-item accordion-item allowPrimary" data-code="{{ $widgetContent->ui_card_number }}">
        <div class="accordion-header">
            <div class="section-name"> {{ $widgetContent->widget?->widget_name }}
                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="{{ route('admin.pages.widget.status.change', $widgetContent->id) }}"
                            {{ $widgetContent->status == 1 ? 'checked' : '' }} type="checkbox" role="switch"
                            id="{{ $widgetContent->id }}">
                        <label class="form-check-label" for="{{ $widgetContent->id }}"> </label>
                    </div>

                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="{{ $widgetContent->id }}">
                        <i class="bi bi-trash"></i>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse">
            <div class="accordion-body">
                @php
                $widgetContents= $widgetContent->getTranslation("widget_content",$lang);
               @endphp
                <form enctype="multipart/form-data" data-action="{{ route('admin.pages.widget.save') }}" class="form"
                    method="POST">
                    @csrf

                    <input type="hidden" name="ui_card_number" value="{{ $widgetContent->ui_card_number }}">
                    <input type="hidden" name="page_id" value="{{ $widgetContent->page_id }}">
                    <input type="hidden" name="widget_slug" class="widget-slug"
                        value="{{ $widgetContent->widget_slug }}">
                    <div class="row">
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Title') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Enter Main Title') }}" name="content[0][title]"
                                    value="{{ isset($widgetContents['title']) ? $widgetContents['title'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-inner">
                                <label> {{ translate('Title Color') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Enter Secondary Title') }}"
                                    name="content[0][span_color_title]"
                                    value="{{ isset($widgetContents['span_color_title']) ? $widgetContents['span_color_title'] : '' }}">
                            </div>
                        </div>

                        <div class="col-sm-6 mb-2">
                            <div class="form-inner ">
                                <label>{{ translate('Keyword one') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Keyword One') }}" name="content[0][keyword_one]"
                                    value="{{ isset($widgetContents['keyword_one']) ? $widgetContents['keyword_one'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner ">
                                <label>{{ translate('Keyword Two') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Keyword Two') }}" name="content[0][keyword_two]"
                                    value="{{ isset($widgetContents['keyword_two']) ? $widgetContents['keyword_two'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Three') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Keyword Three') }}" name="content[0][keyword_three]"
                                    value="{{ isset($widgetContents['keyword_three']) ? $widgetContents['keyword_three'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Four') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Keyword Four') }}" name="content[0][keyword_four] "
                                    value="{{ isset($widgetContents['keyword_four']) ? $widgetContents['keyword_four'] : '' }}">
                            </div>
                        </div>

                        <div class="col-sm-12 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label">{{ translate('Backgroud Image') }} <span>*</span></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p>{{ translate('Choose an image file or drag it here') }}</p>
                                    </div>
                                    <input type="file" name="image" class="dropzone dropzone-image">

                                </div>


                                <div class="preview-zone hidden">
                                    <div class="box box-solid">
                                        <div class="box-body"></div>
                                    </div>
                                </div>
                            </div>
                        </div>





                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Description') }}</label>
                                <textarea name="content[0][description]"> {!! isset($widgetContents['description']) ? $widgetContents['description'] : '' !!}  </textarea>
                            </div>
                        </div>
                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow">{{ translate('Update') }}</button>
                    </div>
                </form>
            </div>
        </div>

    </div>
@else
    <div class="sortable-item accordion-item allowPrimary" data-code="{{ $randomId }}">
        <div class="accordion-header" id="herosection">
            <div class="section-name"> {{ $widgetName }}
                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="{{ route('admin.pages.widget.status.change', $randomId) }}" checked
                            type="checkbox" role="switch" id="{{ $randomId }}">
                        <label class="form-check-label" for="{{ $randomId }}"> </label>
                    </div>
                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="{{ $randomId }}">
                        <i class="bi bi-trash"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse show">
            <div class="accordion-body">
                <form enctype="multipart/form-data" data-action="{{ route('admin.pages.widget.save') }}"
                    class="form" method="POST">
                    @csrf

                    <input type="hidden" name="ui_card_number" value="{{ $randomId }}">
                    <input type="hidden" name="page_id" value="{{ $pageId }}">
                    <input type="hidden" name="widget_slug" class="widget-slug" value="{{ $slug }}">

                    <div class="row">
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Title') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Enter Main Title') }}" name="content[0][title]">
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-inner">
                                <label> {{ translate('Title Color') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Enter Secondary Title') }}"
                                    name="content[0][span_color_title]">
                            </div>
                        </div>

                        <div class="col-sm-6 mb-2">
                            <div class="form-inner ">
                                <label>{{ translate('Keyword one') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Keyword One') }}" name="content[0][keyword_one]">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner ">
                                <label>{{ translate('Keyword Two') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Keyword Two') }}" name="content[0][keyword_two]">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Three') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Keyword Three') }}" name="content[0][keyword_three]">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Four') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Keyword Four') }}" name="content[0][keyword_four] ">
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Description') }}</label>
                                <textarea name="content[0][description]"> </textarea>
                            </div>
                        </div>
                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow">{{ translate('Save') }}</button>
                    </div>
                </form>
            </div>
        </div>

    </div>
@endif
