@if (@isset($widgetContent))
    <div class="sortable-item accordion-item allowPrimary" data-code="{{ $widgetContent->ui_card_number }}">
        <div class="accordion-header">
            <div class="section-name"> {{ $widgetContent->widget?->widget_name }}
                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="{{ route('admin.pages.widget.status.change', $widgetContent->id) }}"
                            {{ $widgetContent->status == 1 ? 'checked' : '' }} type="checkbox" role="switch"
                            id="{{ $widgetContent->id }}">
                        <label class="form-check-label" for="{{ $widgetContent->id }}"> </label>
                    </div>

                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="{{ $widgetContent->id }}">
                        <i class="bi bi-trash"></i>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse">
            <div class="accordion-body">
                @php
                    $widgetContents = $widgetContent->getTranslation('widget_content', $lang);
                @endphp
                <form enctype="multipart/form-data" data-action="{{ route('admin.pages.widget.save') }}" class="form"
                    method="POST">
                    @csrf

                    <input type="hidden" name="ui_card_number" value="{{ $widgetContent->ui_card_number }}">
                    <input type="hidden" name="page_id" value="{{ $widgetContent->page_id }}">
                    <input type="hidden" name="widget_slug" class="widget-slug"
                        value="{{ $widgetContent->widget_slug }}">
                    <div class="row">
                        <!-- Banner Tag -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Banner Tag') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Your Dream Job') }}" name="content[0][banner_tag]"
                                    value="{{ isset($widgetContents['banner_tag']) ? $widgetContents['banner_tag'] : 'Your Dream Job' }}">
                            </div>
                        </div>

                        <!-- Title -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Title') }}</label>
                                <textarea class="username-input" rows="2"
                                    placeholder="{{ translate('Discover Work <strong>That <span>Works for You</span></strong>') }}"
                                    name="content[0][title]">{{ isset($widgetContents['title']) ? $widgetContents['title'] : 'Discover Work <strong>That <span>Works for You</span></strong>' }}</textarea>
                                <small class="text-muted">{{ translate('HTML tags allowed for title') }}</small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Description') }}</label>
                                <textarea class="username-input" rows="3" placeholder="{{ translate('Browse, apply, and get hired faster...') }}"
                                    name="content[0][description]">{{ isset($widgetContents['description']) ? $widgetContents['description'] : 'Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!' }}</textarea>
                            </div>
                        </div>



                        <!-- Keywords Section -->
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Keyword One (Live Jobs)') }}</label>
                                <input type="text" class="username-input" placeholder="{{ translate('Live Jobs') }}"
                                    name="content[0][keyword_one]"
                                    value="{{ isset($widgetContents['keyword_one']) ? $widgetContents['keyword_one'] : 'Live Jobs' }}">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Two (Companies)') }}</label>
                                <input type="text" class="username-input" placeholder="{{ translate('Companies') }}"
                                    name="content[0][keyword_two]"
                                    value="{{ isset($widgetContents['keyword_two']) ? $widgetContents['keyword_two'] : 'Companies' }}">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Three (Candidates)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Candidates') }}" name="content[0][keyword_three]"
                                    value="{{ isset($widgetContents['keyword_three']) ? $widgetContents['keyword_three'] : 'Candidates' }}">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Four (New Jobs)') }}</label>
                                <input type="text" class="username-input" placeholder="{{ translate('New Jobs') }}"
                                    name="content[0][keyword_four]"
                                    value="{{ isset($widgetContents['keyword_four']) ? $widgetContents['keyword_four'] : 'New Jobs' }}">
                            </div>
                        </div>


                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow">{{ translate('Update') }}</button>
                    </div>
                </form>
            </div>
        </div>

    </div>
@else
    <div class="sortable-item accordion-item allowPrimary" data-code="{{ $randomId }}">
        <div class="accordion-header" id="herosection">
            <div class="section-name"> {{ $widgetName }}
                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="{{ route('admin.pages.widget.status.change', $randomId) }}" checked
                            type="checkbox" role="switch" id="{{ $randomId }}">
                        <label class="form-check-label" for="{{ $randomId }}"> </label>
                    </div>
                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="{{ $randomId }}">
                        <i class="bi bi-trash"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse show">
            <div class="accordion-body">
                <form enctype="multipart/form-data" data-action="{{ route('admin.pages.widget.save') }}"
                    class="form" method="POST">
                    @csrf

                    <input type="hidden" name="ui_card_number" value="{{ $randomId }}">
                    <input type="hidden" name="page_id" value="{{ $pageId }}">
                    <input type="hidden" name="widget_slug" class="widget-slug" value="{{ $slug }}">

                    <div class="row">
                        <!-- Banner Tag -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Banner Tag') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Your Dream Job') }}" name="content[0][banner_tag]"
                                    value="Your Dream Job">
                            </div>
                        </div>

                        <!-- Title (HTML Allowed) -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Title (HTML Allowed)') }}</label>
                                <textarea class="username-input" rows="2"
                                    placeholder="{{ translate('Discover Work <strong>That <span>Works for You</span></strong>') }}"
                                    name="content[0][title]">Discover Work <strong>That <span>Works for You</span></strong></textarea>
                                <small class="text-muted">{{ translate('HTML tags allowed for styling') }}</small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Description') }}</label>
                                <textarea class="username-input" rows="3"
                                    placeholder="{{ translate('Browse, apply, and get hired faster...') }}" name="content[0][description]">Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!</textarea>
                            </div>
                        </div>



                        <!-- Keywords Section -->
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Keyword One (Live Jobs)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Live Jobs') }}" name="content[0][keyword_one]"
                                    value="Live Jobs">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Two (Companies)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Companies') }}" name="content[0][keyword_two]"
                                    value="Companies">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Three (Candidates)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Candidates') }}" name="content[0][keyword_three]"
                                    value="Candidates">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label>{{ translate('Keyword Four (New Jobs)') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('New Jobs') }}" name="content[0][keyword_four]"
                                    value="New Jobs">
                            </div>
                        </div>
                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow">{{ translate('Save') }}</button>
                    </div>
                </form>
            </div>
        </div>

    </div>
@endif