@php
    if (!empty($singleWidgetDataShow)) {
        $singleWidgetDataShow = $singleWidgetDataShow->getTranslation('widget_content');
    }
@endphp

<!-- ========== Hero Section Start============= -->
<div class="banner-wrapper">
    <div class="container">
        <div class="row align-items-end gy-md-5 gy-4">
            <div class="col-xxl-7 col-xl-6">
                <div class="banner-content">
                    @if(!empty($singleWidgetDataShow['banner_tag']))
                        <span class="banner-tag">{{ $singleWidgetDataShow['banner_tag'] }}</span>
                    @endif
                    @if(!empty($singleWidgetDataShow['title']))
                        <h1>{!! $singleWidgetDataShow['title'] !!}</h1>
                    @endif
                    @if(!empty($singleWidgetDataShow['description']))
                        <p>{{ $singleWidgetDataShow['description'] }}</p>
                    @endif
                    <div class="job-search-area">
                        @php
                            $pageLink = getThemeOption('page_setting') ?? [];
                            $pageLink = isset($pageLink['job_list_url'])
                                ? $pageLink['job_list_url'] . '?featured=featured'
                                : '#';

                        @endphp
                        <form>
                            <div class="form-inner job-title">
                                <input type="text" placeholder="Service title or keywords" id="job_title"
                                    name="job_title">
                            </div>
                            <div class="form-inner category">
                                <select class="select" name="jobcategory" id="jobcategory">
                                    <option selected disabled>{{ translate('Select Category') }}</option>
                                    @foreach (jobCategory($limit = null) as $index => $category)
                                        <option value="{{ $category->category_slug }}">
                                            {{ $category->getTranslation('category_name') }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-inner">
                                <button type="submit" class="primary-btn-2"> {{ translate('Search') }}</button>
                            </div>
                        </form>
                    </div>
                    <div class="suggest-tag">
                        <ul>
                            <li><a href="#">Designer</a></li>
                            <li><a href="#">Web Developer</a></li>
                            <li><a href="#">Project Manager</a></li>
                            <li><a href="#">Digital Marketing</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xxl-5 col-xl-6">
                <div class="banner-img-wrap">
                    <div class="banner-img">
                        <img src="{{ asset('frontend/assets/images/banner-img.png') }}" alt="">
                    </div>
                    <ul class="counter-item">
                        <li class="single-counter">
                            <img src="{{ asset('frontend/assets/images/icon/live-jobs-icon.svg') }}" alt="">
                            <span>{{ !empty($singleWidgetDataShow['keyword_one']) ? $singleWidgetDataShow['keyword_one'] : 'Live Jobs' }}</span>
                            <h6><strong class="counter">{{ CountLiveJobs() }}</strong>+</h6>
                        </li>
                        <li class="single-counter">
                            <img src="{{ asset('frontend/assets/images/icon/candidates-icon.svg') }}" alt="">
                            <span>{{ !empty($singleWidgetDataShow['keyword_three']) ? $singleWidgetDataShow['keyword_three'] : 'Candidates' }}</span>
                            <h6><strong class="counter">{{ CountCandidate() }}</strong>+</h6>
                        </li>
                        <li class="single-counter">
                            <img src="{{ asset('frontend/assets/images/icon/new-jobs-icon.svg') }}" alt="">
                            <span>{{ !empty($singleWidgetDataShow['keyword_four']) ? $singleWidgetDataShow['keyword_four'] : 'New Jobs' }}</span>
                            <h6><strong class="counter">{{ CountNewJobs() }}</strong>+</h6>
                        </li>
                        <li class="single-counter">
                            <img src="{{ asset('frontend/assets/images/icon/companies-icon.svg') }}" alt="">
                            <span>{{ !empty($singleWidgetDataShow['keyword_two']) ? $singleWidgetDataShow['keyword_two'] : 'Companies' }}</span>
                            <h6><strong class="counter">{{ CountCompany() }}</strong>+</h6>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <img src="{{ asset('frontend/assets/images/banner-vector.png') }}" alt="" class="vector">
</div>
<!-- ========== Hero Section End============= -->
