@php
    if (!empty($singleWidgetDataShow)) {
        $singleWidgetDataShow = $singleWidgetDataShow->getTranslation('widget_content');
    }
    dd($singleWidgetDataShow);
@endphp

<!-- ========== Hero Section Start============= -->
<div class="banner-wrapper" @if(isset($singleWidgetDataShow['background_image']) && $singleWidgetDataShow['background_image']) style="background-image: url('{{ asset('storage/' . $singleWidgetDataShow['background_image']) }}'); background-size: cover; background-position: center;" @endif>
    <div class="container">
        <div class="row align-items-end gy-md-5 gy-4">
            <div class="col-xxl-7 col-xl-6">
                <div class="banner-content">
                    <span class="banner-tag">{{ $singleWidgetDataShow['banner_tag'] ?? 'Your Dream Job' }}</span>
                    <h1>{!! $singleWidgetDataShow['title'] ?? 'Discover Work <strong>That <span>Works for You</span></strong>' !!}</h1>
                    <p>{{ $singleWidgetDataShow['description'] ?? 'Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!' }}</p>
                    <div class="job-search-area">
                        <form>
                            <div class="form-inner job-title">
                                <input type="text" placeholder="Service title or keywords">
                            </div>
                            <div class="form-inner category">
                                <select>
                                    <option value="1">All Categories</option>
                                    <option value="2">Engineering</option>
                                    <option value="3">UI/UX Design</option>
                                    <option value="3">Programming</option>
                                    <option value="3">Closed</option>
                                </select>
                            </div>
                            <div class="form-inner">
                                <button type="submit" class="primary-btn-2"> Search</button>
                            </div>
                        </form>
                    </div>
                    <div class="suggest-tag">
                        <ul>
                            <li><a href="#">Designer</a></li>
                            <li><a href="#">Web Developer</a></li>
                            <li><a href="#">Project Manager</a></li>
                            <li><a href="#">Digital Marketing</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xxl-5 col-xl-6">
                <div class="banner-img-wrap">
                    <div class="banner-img">
                        <img src="{{ isset($singleWidgetDataShow['person_image']) && $singleWidgetDataShow['person_image'] ? asset('storage/' . $singleWidgetDataShow['person_image']) : asset('frontend/assets/images/banner-img.png') }}" alt="Hero Person">
                    </div>
                    <ul class="counter-item">
                        <li class="single-counter">
                            <img src="{{ asset('frontend/assets/images/icon/live-jobs-icon.svg') }}" alt="">
                            <span>{{ $singleWidgetDataShow['keyword_one'] ?? 'Live Jobs' }}</span>
                            <h6><strong class="counter">1000</strong>+</h6>
                        </li>
                        <li class="single-counter">
                            <img src="{{ asset('frontend/assets/images/icon/candidates-icon.svg') }}" alt="">
                            <span>{{ $singleWidgetDataShow['keyword_three'] ?? 'Candidates' }}</span>
                            <h6><strong class="counter">600</strong>+</h6>
                        </li>
                        <li class="single-counter">
                            <img src="{{ asset('frontend/assets/images/icon/new-jobs-icon.svg') }}" alt="">
                            <span>{{ $singleWidgetDataShow['keyword_four'] ?? 'New Jobs' }}</span>
                            <h6><strong class="counter">100</strong>+</h6>
                        </li>
                        <li class="single-counter">
                            <img src="{{ asset('frontend/assets/images/icon/companies-icon.svg') }}" alt="">
                            <span>{{ $singleWidgetDataShow['keyword_two'] ?? 'Companies' }}</span>
                            <h6><strong class="counter">50</strong>+</h6>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    @if(isset($singleWidgetDataShow['show_banner_vector']) && $singleWidgetDataShow['show_banner_vector'] == '1')
        <img src="{{ asset('frontend/assets/images/banner-vector.png') }}" alt="" class="vector">
    @endif
</div>
<!-- ========== Hero Section End============= -->
