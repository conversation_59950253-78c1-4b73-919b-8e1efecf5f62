<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class Pages extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public static function getSingleWidgets($slugName)
    {
        $singleWidgets = Widgets::where(['widget_slug' => $slugName])->first();

        return $singleWidgets;
    }

    protected $casts = [
        'meta_keyward' => 'array',
    ];

    public function getTranslation($field = '', $lang = false)
    {
        $lang = $lang == false ? App::getLocale() : $lang;
        $page = $this->pageTranslation->where('lang', $lang)->first();

        return $page != null ? $page->$field : $this->$field;
    }

    public function pageTranslation()
    {
        return $this->hasMany(PagesTranslation::class, 'page_id', 'id');
    }

    public function widgetContents()
    {
        return $this->hasMany(WidgetsContents::class, 'page_id', 'id')->orderBy('position', 'ASC');
    }
}
