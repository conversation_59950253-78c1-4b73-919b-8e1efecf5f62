<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\TestimonialRequest;
use App\Repositories\Admin\TestimonialRepository;
use Illuminate\Http\Request;

class TestimonialController extends Controller
{
    public function __construct(protected TestimonialRepository $testimonial) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {

        if ($request->ajax()) {
            return $this->testimonial->content();
        }

        return view('admin.pages.testimonial.index');
    }

    /** new Job Sub Category store
     * ========= store============
     *
     * @param App\Http\Requests\TestimonialRequest
     * @return Response
     */
    public function store(TestimonialRequest $request)
    {

        if (!$this->hasPermissions(['testimonial.add', 'testimonial.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->testimonial->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $testimonial = $this->testimonial->getById($id);
        $data['name'] = $testimonial->getTranslation('name', Request()->lang);
        $data['title'] = $testimonial->getTranslation('title', Request()->lang);
        $data['description'] = $testimonial->getTranslation('description', Request()->lang);
        $data['rating'] = $testimonial->getTranslation('rating', Request()->lang);
        $data['id'] = $testimonial->id;
        $data['lang'] = Request()->lang;
        $data['image'] = $testimonial->image;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['testimonial.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->testimonial->destroy($id);
        return $this->formatResponse($result);

    }

    /** sub category status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {

        if (!$this->hasPermissions(['testimonial.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->testimonial->statusChange($id);
        return $this->formatResponse($result);

    }
}
