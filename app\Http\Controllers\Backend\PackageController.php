<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Admin\Package;
use App\Models\Admin\PackageAddon;
use App\Models\Admin\PackageAddonItem;
use App\Models\Admin\PackageItem;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PackageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //
        $package = Package::with('packageItems')->first();
        $packageAddon['starting'] = PackageAddon::with('addonItems')->where('slug', 'starting')->first();
        $packageAddon['featuredJobs'] = PackageAddon::with('addonItems')->where('slug', 'featured-jobs')->first();
        $packageAddon['cvbank'] = PackageAddon::with('addonItems')->where('slug', 'cv-bank')->first();

        return view('admin.pages.package.index', compact('package', 'packageAddon'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {

            if (!$this->hasPermissions(['package.edit'])) {
                return $this->permissionDeniedResponse();
            }
            $package = Package::updateOrCreate(['id' => $request->id], [
                'name' => $request->package_name,
                'type' => $request->package_type,
                'price' => $request->package_price,
                'expired_date' => $request->expired_date,
                'status' => $request->package_status == 'on' ? 1 : 0,

                ]);

            if (! empty($request->packageItem) && count($request->packageItem) > 1) {
                PackageItem::truncate();
                foreach ($request->packageItem as $key => $itemName) {
                    if ($itemName !== null) {
                        $packageItem = new PackageItem;
                        $packageItem->package_id = $package->id;
                        $packageItem->name = $itemName;
                        $packageItem->save();
                    }
                }
            }

            $this->PackageAddonStoreUpdate($request->addon02_item, $request->addon02_id, $package->id, $item = null, $request->addon02_name, $request->addon02_price, $request->addon02_status);
            $this->PackageAddonStoreUpdate($request->addon03_item, $request->addon03_id, $package->id, $item = $request->addon03_item_qty, $request->addon03_name, $request->addon03_price, $request->addon03_status);

            if ($package) {
                return response()->json(['status' => true, 'message' => translate('Package Update successfully')]);
            } else {
                return response()->json(['status' => false, 'message' => translate('Somthing Error!')]);
            }
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Package  $package
     * @return \Illuminate\Http\Response
     */
    public function edit(Package $package)
    {
        return response()->json($package);
    }

    /**
     * statusChange
     *
     * @param  mixed  $request
     * @return void
     */
    public function statusChange(Request $request)
    {

        $package = Package::find($request->id);
        $package->status = $package->status ? false : true;
        $package->update();

        if ($package) {
            return response()->json(['success' => true]);
        }
    }

    /**
     * PackageAddonStoreUpdate
     *
     * @param  mixed  $addonItems
     * @param  int  $addonid
     * @param  int  $packageId
     * @param  mixed  $item
     * @param  mixed  $name
     * @param  mixed  $price
     * @param  mixed  $status
     * @return void
     */
    protected function PackageAddonStoreUpdate($addonItems, $addonid, $packageId, $item, $name, $price, $status)
    {

        if ($packageAddon = PackageAddon::where('id', $addonid)->first()) {
            $packageAddon->price = $price;
            $packageAddon->item_qty = $item ? $item : null;
            $packageAddon->status = $status == 'on' ? 1 : 0;
            $packageAddon->update();
            $this->packageAddonItem($addonItems, $packageAddon->id);
        } else {
            $packageAddon = new PackageAddon;
            $packageAddon->name = $name;
            $packageAddon->slug = Str::slug($name);
            $packageAddon->package_id = $packageId;
            $packageAddon->item_qty = $item ? $item : null;
            $packageAddon->price = $price;
            $packageAddon->status = $status == 'on' ? 1 : 0;
            $packageAddon->save();

            $this->packageAddonItem($addonItems, $packageAddon->id);
        }
    }

    /**
     * packageAddonItem
     *
     * @param  mixed  $addonItems
     * @param  int  $id
     * @return void
     */
    protected function packageAddonItem($addonItems, $id)
    {

        if (! empty($addonItems) && count($addonItems) > 1) {
            if (PackageAddonItem::where('package_addon_id', $id)->first()) {
                PackageAddonItem::where('package_addon_id', $id)->delete();
                foreach ($addonItems as $key => $item) {
                    if ($item !== null) {
                        $addonItem = new PackageAddonItem;
                        $addonItem->package_addon_id = $id;
                        $addonItem->name = $item;
                        $addonItem->save();
                    }
                }
            } else {
                foreach ($addonItems as $key => $item) {
                    if ($item !== null) {
                        $addonItem = new PackageAddonItem;
                        $addonItem->package_addon_id = $id;
                        $addonItem->name = $item;
                        $addonItem->save();
                    }
                }
            }
        }
    }
}
