<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\CityRequest;
use App\Repositories\Admin\CityRepository;
use Illuminate\Http\Request;

class CityController extends Controller
{
    //

    public function __construct(protected CityRepository $city) {}

    /** Display the resource
     *========= index========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->city->content();
        }

        return view('admin.pages.city.index');
    }

    /** new resource store
     * ============ store =======
     *
     * @param Request
     * @return Response
     */
    public function store(CityRequest $request)
    {
        if (!$this->hasPermissions(['city.add', 'city.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->city->create($request);

        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $city = $this->city->getById($id);

        return response()->json($city);
    }

    /** category status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['city.status'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->city->statusChange($id);

        return $this->formatResponse($result);
    }

    /**
     * ================ stateByCountry ============
     *
     * @return Response
     */
    public function stateByCountry($id)
    {

        $result = $this->city->stateByCountryId($id);
        if ($result['status'] !== true) {
            return response()->json([
                'status' => false,
                'message' => $result['message']
            ]);
        }

        return response()->json([
            'status' => true,
            'states' => $result['states']
        ]);
    }

    /**
     *=========== cityByState ============
     *
     * @return Response
     */
    public function cityByState($id)
    {

        $result = $this->city->cityByStateId($id);

        if ($result['status'] !== true) {
            return response()->json([
                'status' => false,
                'message' => $result['message']
            ]);
        }
        return response()->json([
            'status' => true,
            'cities' => $result['cities']
        ]);

    }
}
