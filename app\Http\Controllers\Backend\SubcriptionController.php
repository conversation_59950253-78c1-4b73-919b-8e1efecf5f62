<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Front\Payment;
use App\Models\Front\Subscription;
use App\Repositories\Companies\SubcriptionRepository;
use Illuminate\Http\Request;

class SubcriptionController extends Controller
{
    //

    public function __construct(protected SubcriptionRepository $subcription) {}

    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->subcription->content();
        }
        return view('admin.pages.subcription.index');
    }

    public function show($id)
    {
        $subscription = $this->subcription->getById($id);
        return view('admin.pages.subcription.show', compact('subscription'));
    }

    public function downloadInvoice($id)
    {

        try {

            $subscription = $this->subcription->getById($id);
            ini_set('max_execution_time', 120);
            $mpdf = new \Mpdf\Mpdf;
            $html = view('admin.pages.subcription.invoice', compact('subscription'))->render();

            $mpdf->WriteHTML($html);
            $fileName = str_replace(' ', '_', $subscription->company->company_name).'_ '.str_replace(' ', '_', $subscription->created_at);
            $pdfFilePath = $fileName.'.pdf';
            $mpdf->Output($pdfFilePath, 'I');
            return back();
        } catch (\Throwable $th) {
            return back();
        }
    }

    public function paymentStatus($status, Request $request){



       $subscription=  Subscription::where("id" , $request->subscription_id)->first();
        if($request->type=="status"){
            $subscription->status = $status;
        }
        else{
            $subscription->payment_status = $status;
        }
       $subscription->update();
      return response()->json(['status'=> true]);
    }
}
