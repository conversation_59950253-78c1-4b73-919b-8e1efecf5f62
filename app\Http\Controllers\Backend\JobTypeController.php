<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\JobTypeRequest;
use App\Repositories\Admin\JobTypeRepostitory;
use Illuminate\Http\Request;

class JobTypeController extends Controller
{
    public function __construct(protected JobTypeRepostitory $jobType) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->jobType->content();
        }

        return view('admin.pages.job_type.index');
    }

    /** new Job Sub Category store
     * ========= store============
     *
     * @param App\Http\Requests\JobTypeRequest
     * @return Response
     */
    public function store(JobTypeRequest $request)
    {
        if (!$this->hasPermissions(['jobtype.add', 'jobtype.edit'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jobType->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $jobType = $this->jobType->getById($id);

        $data['job_type_name'] = $jobType->getTranslation('job_type_name', Request()->lang);
        $data['id'] = $jobType->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['jobtype.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->jobType->destroy($id);
        return $this->formatResponse($result);

    }

    /** Job Type status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['jobtype.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jobType->statusChange($id);
        return $this->formatResponse($result);
    }
}
