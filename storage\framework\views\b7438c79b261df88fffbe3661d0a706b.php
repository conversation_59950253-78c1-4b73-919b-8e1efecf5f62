<script>
    (function($){
        "use strict";

      let save =" <?php echo e(translate('Save')); ?>";
      let close =" <?php echo e(translate('Close')); ?>";
      let addForm =" <?php echo e(translate('Create Job Type')); ?>";
      let editForm =" <?php echo e(translate('Update Job Type')); ?>";
      let update =" <?php echo e(translate('Update')); ?>";

      let table = $('#datatable').DataTable({
        processing: true,
        serverSide: true,
        destroy: true,
        order: [ [0, 'desc'] ],
        ajax: {
            url: "<?php echo e(route('admin.job.type')); ?>",
        },
        columns: [

            {
                data: 'Id',
                name: 'Id'
            },

            {
                data: 'Job_Type_Name',
                name: 'Job_Type_Name'
            },

            {
                data: 'Status',
                name: 'Status'
            },
            {
                data: '',
                name: ''
            },

            {
                data: 'action',
                name: 'action',
                orderable: false
            }
        ]
    });

    $('.add-btn').on('click',function () {
        $('.saveBtn').html(`${save}`);
        $('.title').html(`${addForm}`);
        $('.modal').modal('show');
        $("#id").val("")
        $("#lang").val("")
        $('.form')[0].reset();
    });
    $('.closeBtn').on("click",function () {
        $(this).closest('.modal').modal('hide');
    });
    //add Form

    $(document).on('submit', ".form", function (e){
        e.preventDefault();

        let form 		= $(this)[0];
	    let formData 	= new FormData(form);
	    let action 		= $(this).attr('action');

        $.ajax({
            url: action,
            method: 'post',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success:function (data){
                if(data.status === true){
                    toastr["success"](`${data.message}`);
                    form.reset();
                    $('.modal').modal('hide');
                    table.draw();
                }else if (data.status==false){
                    if(typeof data.errors !== 'undefined'){
                        printErrorMsg(data.errors);
                    }else{
                        toastr["error"](`${data.message}`);
                    }
                }
            },
        });
    });

    //edit Form

    $(document).on('click', '.edit', function () {

        let id = $(this).data('id');
        let action = $(this).data('action');

        $.ajax({
            method:"GET",
            dataType:"json",
            url:action,
            success:function(data){
                $('#id').val(id);
                $('#job_type_name').val(data.job_type_name);
                $('#lang').val(data.lang);
                $('.saveBtn').html(`${update}`);
                $('.modal').modal('show');
                $('.title').html(`${editForm}`);

            },
        })
    });


     //========== status

     $(document).on('change', '.status-change', function (e) {
        e.preventDefault();
        let action = $(this).data('action');
        $.ajax({
            url: action,
            type: "GET",
            dataType:"JSON",
            success: function (data) {
                if(data.status === true){
                    toastr["success"](`${data.message}`);
                    table.draw();
                }else if(data.status == false){
                    toastr["error"](`${data.message}`);
                }

            },
        });
    });


    ///delete subcategory

    $(document).on('click', '.delete', function (e) {
        e.preventDefault();

        let action = $(this).data('action');

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url:action,
                    type: "GET",
                    dataType:"JSON",
                    success: function (data) {
                        table.draw();
                        if (data.hasOwnProperty('message')) {
                                Swal.fire(
                                    'error!',
                                    `${data.message}`,
                                )
                            } else {
                                Swal.fire(
                                    'Deleted!',
                                    'Your file has been deleted.',
                                    'success'
                                )
                            }
                    },
                });

            }else if (
                /* Read more about handling dismissals below */
                result.dismiss === Swal.DismissReason.cancel
            ) {
                Swal.fire(
                    'Cancelled',
                    'Your file is safe :)',
                    'error'
                )
            }
        })
    });

    function printErrorMsg (msg) {
        $.each( msg, function( key, value ) {
            $('.'+key+'_err').text(value).fadeIn().delay(30000).fadeOut("slow");
        });
    }
    })(jQuery);

</script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/admin/jobtype.blade.php ENDPATH**/ ?>