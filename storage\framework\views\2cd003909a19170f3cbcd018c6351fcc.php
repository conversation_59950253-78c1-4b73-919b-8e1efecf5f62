<!-- header-area -->
<header class="header-area">
    <div class="sidebar-header">

        <a href="<?php echo e(route('admin.dashboard')); ?>" class="header-logo">
            <?php if(isset(getThemeOption('backend_logo')['logo'])): ?>
                <?php if(fileExists($folder = 'theme-options', $fileName = getThemeOption('backend_logo')['logo']) == true && getThemeOption('backend_logo')['logo'] !== ''): ?>
                    <img class="img-thumb" alt="logo" src="<?php echo e(asset('storage/theme-options/' .getThemeOption('backend_logo')['logo'])); ?>" />
                <?php else: ?>
                <img src="<?php echo e(asset('backend/assets/images/bg/title-loog.png')); ?>" alt="">
                <?php endif; ?>
            <?php endif; ?>
        </a>

    </div>
    <div class="main-conent-header">
        <?php echo $__env->yieldContent('breadcrumb'); ?>

        <div class="admin-area dropdown">
            <?php
                if (Session::has('locale')) {
                    $locale = Session::get('locale', Config::get('app.locale'));
                } else {
                    $locale = env('DEFAULT_LANGUAGE');
                }
            ?>
            <div id="lang-change">
                <a class="no-arrow dropdown-toggle d-flex jusify-content-start align-items-center gap-2 border-none"
                    href="javascript:void(0);" id="dropdownlanguage" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <span class="active-lang">
                        <img src="<?php echo e(asset('storage/lang/flags/' . $locale . '.png')); ?>"
                            alt="<?php echo e(translate('Language')); ?>" height="11">
                         <?php echo e(strtoupper($locale)); ?>

                    </span>
                </a>
                <ul class="dropdown-menu" id="change-lang" aria-labelledby="dropdownlanguage">

                    <?php $__currentLoopData = \App\Models\Admin\Language::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li>
                            <a class="dropdown-item languageTranslate"
                                href="javascript:void(0)"
                                data-action="<?php echo e(route('admin.language.change')); ?>"
                                id="<?php echo e($language->code); ?>"
                                data-code="<?php echo e($language->code); ?>"
                                data-flag="<?php echo e($language->code); ?>"
                                class="dropdown-item <?php if($locale == $language->code): ?> active  <?php endif; ?>">
                                <img class="mr-2" src="<?php echo e(asset('storage/lang/flags/' . $language->code . '.png')); ?>"> <span class="language"><?php echo e($language->name); ?></span>
                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                </ul>
            </div>
        </div>


        <div class="admin-area dropdown">
            <button class=" dropdown-toggle d-flex jusify-content-start align-items-center gap-2 border-none" id="dropdownMenuButton2" data-bs-toggle="dropdown" aria-expanded="false">
                <span class="admin-thumb">
                    <?php if(fileExists($folder = 'images', $fileName =Auth::user()->image ) == true && Auth::user()->image !== ''): ?>
                            <img  src="<?php echo e(asset('storage/images/'.Auth::user()->image)); ?>" />
                        <?php else: ?>
                        <img src="<?php echo e(asset('backend/assets/images/bg/admin.png')); ?>" alt="">
                    <?php endif; ?>

                </span>

                <span class="admin-desig">
                    <h6><?php echo e(Auth::user()->first_name); ?> <?php echo e(Auth::user()->last_name); ?></h6>
                    <p><?php echo e(translate('Super Admin')); ?></p>
                </span>
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                <li><a class="dropdown-item " href="<?php echo e(route('admin.profile')); ?>"><i class="bi bi-person"></i><?php echo e(translate('Profile')); ?></a></li>
                <li><a class="dropdown-item" href="<?php echo e(route('admin.lock')); ?>"><i class="bi bi-file-lock"></i><?php echo e(translate('Lock screen')); ?></a></li>
                <li>
                    <a class="dropdown-item" 
                    onclick="event.preventDefault();
                    document.getElementById('logout-form').submit();" >
                    <i class="bi bi-box-arrow-left"></i>
                    <?php echo e(translate('Logout')); ?>

                </a>
                
                  <form id="logout-form" action="<?php echo e(route('admin.logout')); ?>" method="POST"
                            class="d-none">
                      <?php echo csrf_field(); ?>
                  </form>
                </li>
            </ul>
        </div>
    </div>
</header>
<!-- main-container -->
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/admin/includes/header.blade.php ENDPATH**/ ?>