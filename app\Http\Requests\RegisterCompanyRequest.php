<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class RegisterCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {

        return [

            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,'.Request()->user_id,
            'password' => isset(Request()->id) ? '' : 'required|min:5|max:12|confirmed',
            'company_name' => 'required|string|unique:companies,company_name,'.Request()->id,
            'company_size' => 'required|int',
            'company_type' => 'required',
            'terms_condition' => isset(Request()->terms_condition) ? 'required' : '',

        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [

            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'Owner Email is required.',
            'email.unique' => 'A user with this email address already exists.',
            'password.required' => 'Password Field is required',
            'password.confirmed' => 'Password Confirmation does not match',
            'password.max.string' => 'Password Maximun length 12',
            'password.min.string' => 'Password Minimun length 5',
            'company_name.required' => 'Company Name is required',
            'company_size.required' => 'Comapny Size is required',
            'company_type.required' => 'Comapny type is required',
            'company_size.integer' => 'Comapny Size will be Integer',
            'terms_condition' => 'Please checked company terms & conditions',

        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => false,
            'errors' => $validator->errors(),
        ]));
    }
}
