{"__meta": {"id": "01JY65PQH8KFK9EZC90N3AHG4N", "datetime": "2025-06-20 08:01:27", "utime": **********.59349, "method": "GET", "uri": "/frontend/assets/image/featured-bg.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.298378, "end": **********.593506, "duration": 0.29512810707092285, "duration_str": "295ms", "measures": [{"label": "Booting", "start": **********.298378, "relative_start": 0, "end": **********.50818, "relative_end": **********.50818, "duration": 0.*****************, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.508189, "relative_start": 0.*****************, "end": **********.593508, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "85.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.51845, "relative_start": 0.*****************, "end": **********.52405, "relative_end": **********.52405, "duration": 0.****************, "duration_str": "5.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.547029, "relative_start": 0.*****************, "end": **********.591442, "relative_end": **********.591442, "duration": 0.044413089752197266, "duration_str": "44.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 18, "nb_templates": 18, "templates": [{"name": "1x front.pages.404", "param_count": null, "params": [], "start": **********.548975, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/404.blade.phpfront.pages.404", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2F404.blade.php&line=1", "ajax": false, "filename": "404.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.404"}, {"name": "1x front.layouts.master", "param_count": null, "params": [], "start": **********.553519, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/master.blade.phpfront.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.master"}, {"name": "1x front.layouts.includes.head", "param_count": null, "params": [], "start": **********.553797, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/head.blade.phpfront.layouts.includes.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.head"}, {"name": "1x css.theme-custom-css", "param_count": null, "params": [], "start": **********.554945, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/css/theme-custom-css.blade.phpcss.theme-custom-css", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fcss%2Ftheme-custom-css.blade.php&line=1", "ajax": false, "filename": "theme-custom-css.blade.php", "line": "?"}, "render_count": 1, "name_original": "css.theme-custom-css"}, {"name": "1x front.layouts.includes.topbar", "param_count": null, "params": [], "start": **********.555266, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/topbar.blade.phpfront.layouts.includes.topbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftopbar.blade.php&line=1", "ajax": false, "filename": "topbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.topbar"}, {"name": "3x front.layouts.includes.social-area", "param_count": null, "params": [], "start": **********.558956, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/social-area.blade.phpfront.layouts.includes.social-area", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fsocial-area.blade.php&line=1", "ajax": false, "filename": "social-area.blade.php", "line": "?"}, "render_count": 3, "name_original": "front.layouts.includes.social-area"}, {"name": "1x front.layouts.includes.header", "param_count": null, "params": [], "start": **********.559325, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/header.blade.phpfront.layouts.includes.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.header"}, {"name": "1x front.layouts.includes.logo", "param_count": null, "params": [], "start": **********.559616, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/logo.blade.phpfront.layouts.includes.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.logo"}, {"name": "1x front.layouts.includes.mobile-logo", "param_count": null, "params": [], "start": **********.567963, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/mobile-logo.blade.phpfront.layouts.includes.mobile-logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmobile-logo.blade.php&line=1", "ajax": false, "filename": "mobile-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.mobile-logo"}, {"name": "1x front.layouts.includes.menu", "param_count": null, "params": [], "start": **********.568292, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/menu.blade.phpfront.layouts.includes.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.menu"}, {"name": "1x front.layouts.includes.mobile-menu-bottom", "param_count": null, "params": [], "start": **********.572291, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/mobile-menu-bottom.blade.phpfront.layouts.includes.mobile-menu-bottom", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmobile-menu-bottom.blade.php&line=1", "ajax": false, "filename": "mobile-menu-bottom.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.mobile-menu-bottom"}, {"name": "1x front.layouts.includes.navbar-right", "param_count": null, "params": [], "start": **********.575182, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/navbar-right.blade.phpfront.layouts.includes.navbar-right", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fnavbar-right.blade.php&line=1", "ajax": false, "filename": "navbar-right.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.navbar-right"}, {"name": "1x front.layouts.includes.footer", "param_count": null, "params": [], "start": **********.577776, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/footer.blade.phpfront.layouts.includes.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.footer"}, {"name": "1x front.layouts.includes.script", "param_count": null, "params": [], "start": **********.580482, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/script.blade.phpfront.layouts.includes.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.script"}, {"name": "1x js.translate", "param_count": null, "params": [], "start": **********.580857, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/translate.blade.phpjs.translate", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftranslate.blade.php&line=1", "ajax": false, "filename": "translate.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.translate"}, {"name": "1x js.theme-custom-js", "param_count": null, "params": [], "start": **********.59102, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/theme-custom-js.blade.phpjs.theme-custom-js", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftheme-custom-js.blade.php&line=1", "ajax": false, "filename": "theme-custom-js.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.theme-custom-js"}]}, "route": {"uri": "GET {any}", "middleware": "web", "controller": "App\\Http\\Controllers\\Frontend\\PagesController@customPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PagesController.php:15-70</a>"}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00222, "accumulated_duration_str": "2.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `pages` where `page_route` = 'frontend/assets/image/featured-bg.png' limit 1", "type": "query", "params": [], "bindings": ["frontend/assets/image/featured-bg.png"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.545018, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:27", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=27", "ajax": false, "filename": "PagesController.php", "line": "27"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 30.631}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "front.layouts.includes.topbar", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/topbar.blade.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.556751, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.topbar:48", "source": {"index": 16, "namespace": "view", "name": "front.layouts.includes.topbar", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/topbar.blade.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftopbar.blade.php&line=48", "ajax": false, "filename": "topbar.blade.php", "line": "48"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 30.631, "width_percent": 16.667}, {"sql": "select * from `menu_items` where (`menu_id` = 2 and `parent_id` is null) order by `order` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.568795, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 47.297, "width_percent": 29.279}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (215, 216, 231, 232, 233, 235) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.570542, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 76.577, "width_percent": 23.423}]}, "models": {"data": {"App\\Models\\Admin\\MenuItem": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "kcRYzpxbdmqE2cd23fKHJZ70QG5G3vKWJptOhjYR", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/frontend/assets/js/boxicons.js.map\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/frontend/assets/image/featured-bg.png", "action_name": null, "controller_action": "App\\Http\\Controllers\\Frontend\\PagesController@customPage", "uri": "GET {any}", "controller": "App\\Http\\Controllers\\Frontend\\PagesController@customPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PagesController.php:15-70</a>", "middleware": "web", "duration": "297ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-785388732 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-785388732\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1907520086 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1907520086\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1730833707 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">http://127.0.0.1:8000/frontend/assets/css/style.css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6Ikl4RDVNQWlUUHI1d3NBZENkR2ZjSWc9PSIsInZhbHVlIjoiL0R2dllwTy92U0wxa1pmS25VMFZQREh3WkhsUDVVaTJKTGxwc3pxOEJGaUxLcFgrY2pHZFlsU1Z2di9MVjg5MWJuTXdiTWNWSnJrN3c4azhoejNrTXFOU3RCc0ZPemo3ejVUTjNoOGpxVVVHcnhYQ2xyWDYrdnFuM3BGK0VOU0QiLCJtYWMiOiJjMTJkMzM5NmY5MDU5Zjg2ZGY2OWRmYmYxY2E4ZTJjYjA0MjE5NTQyOTU2MGEyNDIzZTVlZWU1N2IyZTI3YWVkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImZYUURsOWZ1THBWUVgvdzNWNkl3d3c9PSIsInZhbHVlIjoieVRiWllUY29heFNPOHRXS3J6RGFudDQwMG4rc3FDK0owTEh0Z0lUK2M4aUJReS9WNUk3MlQ5bGF4K3M4di96WmEwUFlWa0NzTmZ5aFA3UVZTNFBaUy9tNXdwNzVERlNya09WYituZW9mOW9JTlJtcGJDK1I3MElGTElSSWhXbXMiLCJtYWMiOiJiMTIyMjQ4YWRiZDk2MTc1NzNkY2QwOTk1YjBhNDVmMjIxNzYyODVhNTVlZDY5MGExOWJhMmI2OGM2YmY5YjEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730833707\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1688763928 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kcRYzpxbdmqE2cd23fKHJZ70QG5G3vKWJptOhjYR</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NnM2wQP19ll75N2OOmwPl2L4N2lZigEBzsLddMwQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688763928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2070326646 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 08:01:27 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070326646\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-747479577 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kcRYzpxbdmqE2cd23fKHJZ70QG5G3vKWJptOhjYR</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/frontend/assets/js/boxicons.js.map</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747479577\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/frontend/assets/image/featured-bg.png", "controller_action": "App\\Http\\Controllers\\Frontend\\PagesController@customPage"}, "badge": null}}