<?php

namespace Database\Seeders;

use App\Models\Admin\WidgetsContents;
use Illuminate\Database\Seeder;

class WidgetContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {



        $data = [
            [
                'id' => 3,
                'page_id' => 1,
                'widget_slug' => 'hero-section',
                'ui_card_number' => '348ffac',
                'widget_content' => json_encode([
                    "banner_tag" => "Your Dream Job",
                    "title" => "Discover Work <strong>That <span>Works for You</span></strong>",
                    "description" => "Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!",
                    "banner_image" => "",
                    "keyword_one" => "Live Jobs",
                    "keyword_two" => "Companies",
                    "keyword_three" => "Candidates",
                    "keyword_four" => "New Jobs",
                    "banner_vector" => "",
                    "background_image" => ""
                ]),
                'created_by' => null,
                'updated_by' => null,
                'status' => 1,
                'created_at' => '2023-03-31 20:47:48',
                'updated_at' => '2024-08-24 01:33:42',
                'position' => 1
            ],
            [
                'id' => 5,
                'page_id' => 1,
                'widget_slug' => 'featured-jobs',
                'ui_card_number' => '4129902',
                'widget_content' => json_encode([
                    "span_color_title" => "Featured",
                    "secondary_title" => "Jobs",
                    "show_item" => "6",
                    "page_link" => "https://www.jobes.egenslab.com/find-job",
                    "description" => "Choose your trending dream job and brighten your future."
                ]),
                'created_by' => null,
                'updated_by' => null,
                'status' => 1,
                'created_at' => '2023-03-31 21:09:18',
                'updated_at' => '2024-10-25 04:12:38',
                'position' => 3
            ],
            [
                'id' => 8,
                'page_id' => 1,
                'widget_slug' => 'reviews',
                'ui_card_number' => '9b750be',
                'widget_content' => json_encode([
                    "title" => "Trusted",
                    "span_color_title" => "Users",
                    "show_item" => "10",
                    "description" => "Here are some valuable words from our users who get complete satisfaction."
                ]),
                'created_by' => null,
                'updated_by' => null,
                'status' => 1,
                'created_at' => '2023-03-31 21:13:30',
                'updated_at' => '2024-08-23 13:13:03',
                'position' => 6
            ],
            // Add other records here following the same format...
        ];


        if (WidgetsContents::count() == 0) {
            WidgetsContents::insert($data);
        }
    }
}
