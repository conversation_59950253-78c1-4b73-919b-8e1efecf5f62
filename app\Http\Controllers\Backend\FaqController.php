<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\FaqRequest;
use App\Repositories\Admin\FaqRepository;
use Illuminate\Http\Request;

class FaqController extends Controller
{
    public function __construct(protected FaqRepository $faq) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {

        if ($request->ajax()) {
            return $this->faq->content();
        }

        return view('admin.pages.Faq.index');
    }

    /** new post store
     * ========= store============
     *
     * @param App\Http\Requests\FaqRequest
     * @return Response
     */
    public function store(FaqRequest $request)
    {

        if (!$this->hasPermissions(['faq.add', 'faq.edit'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->faq->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $faq = $this->faq->getById($id);

        $data['name'] = $faq->getTranslation('question', Request()->lang);
        $data['answer'] = $faq->getTranslation('answer', Request()->lang);
        $data['id'] = $faq->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {

        if (!$this->hasPermissions(['faq.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->faq->destroy($id);
        return $this->formatResponse($result);

    }

    /** status change
     * ======= statusChange ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {

        if (!$this->hasPermissions(['faq.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->faq->statusChange($id);
        return $this->formatResponse($result);
    }
}
