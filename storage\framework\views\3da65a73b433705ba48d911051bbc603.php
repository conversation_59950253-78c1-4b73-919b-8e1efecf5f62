<?php
    if (!empty($singleWidgetDataShow)) {
        $singleWidgetDataShow = $singleWidgetDataShow->getTranslation('widget_content');
    }
?>

<!-- ========== Hero Section Start============= -->
<?php
    $backgroundImage = $singleWidgetDataShow['background_image'] ?? '';
    $backgroundStyle = '';

    if (!empty($backgroundImage) && !is_array($backgroundImage)) {
        // Check if it's a default asset or uploaded file
        if (str_starts_with($backgroundImage, 'frontend/')) {
            $bgUrl = asset($backgroundImage);
        } else {
            $bgUrl = asset('storage/' . $backgroundImage);
        }
        $backgroundStyle = "background-image: url('{$bgUrl}'); background-size: cover; background-position: center;";
    }
?>
<div class="banner-wrapper" <?php if($backgroundStyle): ?> style="<?php echo e($backgroundStyle); ?>" <?php endif; ?>>
    <div class="container">
        <div class="row align-items-end gy-md-5 gy-4">
            <div class="col-xxl-7 col-xl-6">
                <div class="banner-content">
                    <span class="banner-tag"><?php echo e(!empty($singleWidgetDataShow['banner_tag']) ? $singleWidgetDataShow['banner_tag'] : 'Your Dream Job'); ?></span>
                    <h1><?php echo !empty($singleWidgetDataShow['title']) ? $singleWidgetDataShow['title'] : 'Discover Work <strong>That <span>Works for You</span></strong>'; ?></h1>
                    <p><?php echo e(!empty($singleWidgetDataShow['description']) ? $singleWidgetDataShow['description'] : 'Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!'); ?></p>
                    <div class="job-search-area">
                        <form>
                            <div class="form-inner job-title">
                                <input type="text" placeholder="Service title or keywords">
                            </div>
                            <div class="form-inner category">
                                <select>
                                    <option value="1">All Categories</option>
                                    <option value="2">Engineering</option>
                                    <option value="3">UI/UX Design</option>
                                    <option value="3">Programming</option>
                                    <option value="3">Closed</option>
                                </select>
                            </div>
                            <div class="form-inner">
                                <button type="submit" class="primary-btn-2"> Search</button>
                            </div>
                        </form>
                    </div>
                    <div class="suggest-tag">
                        <ul>
                            <li><a href="#">Designer</a></li>
                            <li><a href="#">Web Developer</a></li>
                            <li><a href="#">Project Manager</a></li>
                            <li><a href="#">Digital Marketing</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xxl-5 col-xl-6">
                <div class="banner-img-wrap">
                    <div class="banner-img">
                        <img src="<?php echo e(asset('frontend/assets/images/banner-img.png')); ?>" alt="">
                    </div>
                    <ul class="counter-item">
                        <li class="single-counter">
                            <img src="<?php echo e(asset('frontend/assets/images/icon/live-jobs-icon.svg')); ?>" alt="">
                            <span><?php echo e(!empty($singleWidgetDataShow['keyword_one']) ? $singleWidgetDataShow['keyword_one'] : 'Live Jobs'); ?></span>
                            <h6><strong class="counter">1000</strong>+</h6>
                        </li>
                        <li class="single-counter">
                            <img src="<?php echo e(asset('frontend/assets/images/icon/candidates-icon.svg')); ?>" alt="">
                            <span><?php echo e(!empty($singleWidgetDataShow['keyword_three']) ? $singleWidgetDataShow['keyword_three'] : 'Candidates'); ?></span>
                            <h6><strong class="counter">600</strong>+</h6>
                        </li>
                        <li class="single-counter">
                            <img src="<?php echo e(asset('frontend/assets/images/icon/new-jobs-icon.svg')); ?>" alt="">
                            <span><?php echo e(!empty($singleWidgetDataShow['keyword_four']) ? $singleWidgetDataShow['keyword_four'] : 'New Jobs'); ?></span>
                            <h6><strong class="counter">100</strong>+</h6>
                        </li>
                        <li class="single-counter">
                            <img src="<?php echo e(asset('frontend/assets/images/icon/companies-icon.svg')); ?>" alt="">
                            <span><?php echo e(!empty($singleWidgetDataShow['keyword_two']) ? $singleWidgetDataShow['keyword_two'] : 'Companies'); ?></span>
                            <h6><strong class="counter">50</strong>+</h6>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-xxl-5 col-xl-6">
                <div class="banner-img-wrap">
                    <div class="banner-img">
                        <?php
                            $bannerImage = $singleWidgetDataShow['banner_image'] ?? '';
                            $defaultImage = asset('frontend/assets/images/banner-img.png');

                            if (!empty($bannerImage) && !is_array($bannerImage)) {
                                // Check if it's a default asset or uploaded file
                                if (str_starts_with($bannerImage, 'frontend/')) {
                                    $imageUrl = asset($bannerImage);
                                } else {
                                    $imageUrl = asset('storage/' . $bannerImage);
                                }
                            } else {
                                $imageUrl = $defaultImage;
                            }
                        ?>
                        <img src="<?php echo e($imageUrl); ?>" alt="Hero Person" onerror="this.src='<?php echo e($defaultImage); ?>'">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
        $bannerVector = $singleWidgetDataShow['banner_vector'] ?? '';
        $defaultVector = asset('frontend/assets/images/banner-vector.png');

        if (!empty($bannerVector) && !is_array($bannerVector)) {
            // Check if it's a default asset or uploaded file
            if (str_starts_with($bannerVector, 'frontend/')) {
                $vectorUrl = asset($bannerVector);
            } else {
                $vectorUrl = asset('storage/' . $bannerVector);
            }
        } else {
            $vectorUrl = $defaultVector;
        }
    ?>
    <img src="<?php echo e($vectorUrl); ?>" alt="" class="vector" onerror="this.src='<?php echo e($defaultVector); ?>'">
</div>
<!-- ========== Hero Section End============= -->
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/pages/widgets/hero-section.blade.php ENDPATH**/ ?>