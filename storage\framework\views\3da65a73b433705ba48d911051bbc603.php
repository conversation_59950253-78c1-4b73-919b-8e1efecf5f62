<?php
    if (!empty($singleWidgetDataShow)) {
        $singleWidgetDataShow = $singleWidgetDataShow->getTranslation('widget_content');
    }
?>

<!-- ========== Hero Section Start============= -->
<div class="banner-wrapper">
    <div class="container">
        <div class="row align-items-end gy-md-5 gy-4">
            <div class="col-xxl-7 col-xl-6">
                <div class="banner-content">
                    <?php if(isset($singleWidgetDataShow['banner_tag']) && $singleWidgetDataShow['banner_tag'] !== ''): ?>
                        <span class="banner-tag"><?php echo e($singleWidgetDataShow['banner_tag']); ?></span>
                    <?php elseif(!isset($singleWidgetDataShow['banner_tag']) || $singleWidgetDataShow === null): ?>
                        <span class="banner-tag">Your Dream Job</span>
                    <?php endif; ?>
                    <?php if(isset($singleWidgetDataShow['title']) && $singleWidgetDataShow['title'] !== ''): ?>
                        <h1><?php echo $singleWidgetDataShow['title']; ?></h1>
                    <?php elseif(!isset($singleWidgetDataShow['title']) || $singleWidgetDataShow === null): ?>
                        <h1>Discover Work <strong>That <span>Works for You</span></strong></h1>
                    <?php endif; ?>
                    <?php if(isset($singleWidgetDataShow['description']) && $singleWidgetDataShow['description'] !== ''): ?>
                        <p><?php echo e($singleWidgetDataShow['description']); ?></p>
                    <?php elseif(!isset($singleWidgetDataShow['description']) || $singleWidgetDataShow === null): ?>
                        <p>Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!</p>
                    <?php endif; ?>
                    <div class="job-search-area">
                        <?php
                            $pageLink = getThemeOption('page_setting') ?? [];
                            $pageLink = isset($pageLink['job_list_url'])
                                ? $pageLink['job_list_url'] . '?featured=featured'
                                : '#';

                        ?>
                        <form>
                            <div class="form-inner job-title">
                                <input type="text" placeholder="Service title or keywords" id="job_title"
                                    name="job_title">
                            </div>
                            <div class="form-inner category">
                                <select class="select" name="jobcategory" id="jobcategory">
                                    <option selected disabled><?php echo e(translate('Select Category')); ?></option>
                                    <?php $__currentLoopData = jobCategory($limit = null); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->category_slug); ?>">
                                            <?php echo e($category->getTranslation('category_name')); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="form-inner">
                                <button type="submit" class="primary-btn-2"> <?php echo e(translate('Search')); ?></button>
                            </div>
                        </form>
                    </div>
                    <div class="suggest-tag">
                        <ul>
                            <li><a href="#">Designer</a></li>
                            <li><a href="#">Web Developer</a></li>
                            <li><a href="#">Project Manager</a></li>
                            <li><a href="#">Digital Marketing</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xxl-5 col-xl-6">
                <div class="banner-img-wrap">
                    <div class="banner-img">
                        <img src="<?php echo e(asset('frontend/assets/images/banner-img.png')); ?>" alt="">
                    </div>
                    <ul class="counter-item">
                        <?php if(isset($singleWidgetDataShow['keyword_one']) && $singleWidgetDataShow['keyword_one'] !== ''): ?>
                            <li class="single-counter">
                                <img src="<?php echo e(asset('frontend/assets/images/icon/live-jobs-icon.svg')); ?>" alt="">
                                <span><?php echo e($singleWidgetDataShow['keyword_one']); ?></span>
                                <h6><strong class="counter"><?php echo e(CountLiveJobs()); ?></strong>+</h6>
                            </li>
                        <?php elseif(!isset($singleWidgetDataShow['keyword_one']) || $singleWidgetDataShow === null): ?>
                            <li class="single-counter">
                                <img src="<?php echo e(asset('frontend/assets/images/icon/live-jobs-icon.svg')); ?>" alt="">
                                <span>Live Jobs</span>
                                <h6><strong class="counter"><?php echo e(CountLiveJobs()); ?></strong>+</h6>
                            </li>
                        <?php endif; ?>
                        <?php if(isset($singleWidgetDataShow['keyword_three']) && $singleWidgetDataShow['keyword_three'] !== ''): ?>
                            <li class="single-counter">
                                <img src="<?php echo e(asset('frontend/assets/images/icon/candidates-icon.svg')); ?>" alt="">
                                <span><?php echo e($singleWidgetDataShow['keyword_three']); ?></span>
                                <h6><strong class="counter"><?php echo e(CountCandidate()); ?></strong>+</h6>
                            </li>
                        <?php elseif(!isset($singleWidgetDataShow['keyword_three']) || $singleWidgetDataShow === null): ?>
                            <li class="single-counter">
                                <img src="<?php echo e(asset('frontend/assets/images/icon/candidates-icon.svg')); ?>" alt="">
                                <span>Candidates</span>
                                <h6><strong class="counter"><?php echo e(CountCandidate()); ?></strong>+</h6>
                            </li>
                        <?php endif; ?>
                        <?php if(isset($singleWidgetDataShow['keyword_four']) && $singleWidgetDataShow['keyword_four'] !== ''): ?>
                            <li class="single-counter">
                                <img src="<?php echo e(asset('frontend/assets/images/icon/new-jobs-icon.svg')); ?>" alt="">
                                <span><?php echo e($singleWidgetDataShow['keyword_four']); ?></span>
                                <h6><strong class="counter"><?php echo e(CountNewJobs()); ?></strong>+</h6>
                            </li>
                        <?php elseif(!isset($singleWidgetDataShow['keyword_four']) || $singleWidgetDataShow === null): ?>
                            <li class="single-counter">
                                <img src="<?php echo e(asset('frontend/assets/images/icon/new-jobs-icon.svg')); ?>" alt="">
                                <span>New Jobs</span>
                                <h6><strong class="counter"><?php echo e(CountNewJobs()); ?></strong>+</h6>
                            </li>
                        <?php endif; ?>
                        <?php if(isset($singleWidgetDataShow['keyword_two']) && $singleWidgetDataShow['keyword_two'] !== ''): ?>
                            <li class="single-counter">
                                <img src="<?php echo e(asset('frontend/assets/images/icon/companies-icon.svg')); ?>" alt="">
                                <span><?php echo e($singleWidgetDataShow['keyword_two']); ?></span>
                                <h6><strong class="counter"><?php echo e(CountCompany()); ?></strong>+</h6>
                            </li>
                        <?php elseif(!isset($singleWidgetDataShow['keyword_two']) || $singleWidgetDataShow === null): ?>
                            <li class="single-counter">
                                <img src="<?php echo e(asset('frontend/assets/images/icon/companies-icon.svg')); ?>" alt="">
                                <span>Companies</span>
                                <h6><strong class="counter"><?php echo e(CountCompany()); ?></strong>+</h6>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <img src="<?php echo e(asset('frontend/assets/images/banner-vector.png')); ?>" alt="" class="vector">
</div>
<!-- ========== Hero Section End============= -->
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/pages/widgets/hero-section.blade.php ENDPATH**/ ?>