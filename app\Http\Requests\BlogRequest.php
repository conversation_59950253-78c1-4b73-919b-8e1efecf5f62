<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class BlogRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            //
            'blog_category_id' => 'required',
            'blog_thumbnail' => isset(Request()->id) ? (isset(Request()->blog_thumbnail) ? 'required|image' : '') : 'required|image',
            'blog_title' => 'required', Rule::unique('blogs', 'blog_title')->ignore(Request()->id),
            'blog_tags' => 'required',
            'blog_details' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'blog_category_id.required' => 'Category Name is required.',
            'blog_title.required' => 'Title is required.',
            'blog_title.unique' => 'Title Already exists.',
            'blog_thumbnail.required' => 'Thumbnail is required',
            'blog_thumbnail.image' => 'Thumbnail will be must image',
            'blog_tags' => 'Tags is required.',
            'blog_details' => 'Description is required.',
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => false,
            'errors' => $validator->errors(),
        ]));
    }
}
