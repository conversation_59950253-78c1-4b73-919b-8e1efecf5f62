{"__meta": {"id": "01JY6J3JF1107BV9DPJFCHTWR7", "datetime": "2025-06-20 11:38:11", "utime": **********.318075, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750419481.246029, "end": **********.318096, "duration": 10.072067022323608, "duration_str": "10.07s", "measures": [{"label": "Booting", "start": 1750419481.246029, "relative_start": 0, "end": **********.846034, "relative_end": **********.846034, "duration": 7.***************, "duration_str": "7.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.846074, "relative_start": 7.***************, "end": **********.318098, "relative_end": 2.1457672119140625e-06, "duration": 2.****************, "duration_str": "2.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.233278, "relative_start": 7.***************, "end": **********.405316, "relative_end": **********.405316, "duration": 0.*****************, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.890732, "relative_start": 8.***************, "end": **********.290319, "relative_end": **********.290319, "duration": 1.****************, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 25, "nb_templates": 25, "templates": [{"name": "1x front.pages.eg_job", "param_count": null, "params": [], "start": **********.937256, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/eg_job.blade.phpfront.pages.eg_job", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Feg_job.blade.php&line=1", "ajax": false, "filename": "eg_job.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.eg_job"}, {"name": "1x front.pages.widgets.hero-section", "param_count": null, "params": [], "start": **********.938774, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.phpfront.pages.widgets.hero-section", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Fhero-section.blade.php&line=1", "ajax": false, "filename": "hero-section.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.hero-section"}, {"name": "1x front.pages.widgets.latest-jobs-category", "param_count": null, "params": [], "start": 1750419490.460072, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.phpfront.pages.widgets.latest-jobs-category", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Flatest-jobs-category.blade.php&line=1", "ajax": false, "filename": "latest-jobs-category.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.latest-jobs-category"}, {"name": "1x front.pages.widgets.featured-jobs", "param_count": null, "params": [], "start": 1750419490.486792, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.phpfront.pages.widgets.featured-jobs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Ffeatured-jobs.blade.php&line=1", "ajax": false, "filename": "featured-jobs.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.featured-jobs"}, {"name": "1x front.pages.widgets.working-process", "param_count": null, "params": [], "start": 1750419490.718295, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/working-process.blade.phpfront.pages.widgets.working-process", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Fworking-process.blade.php&line=1", "ajax": false, "filename": "working-process.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.working-process"}, {"name": "1x front.pages.widgets.dream-location", "param_count": null, "params": [], "start": 1750419490.745723, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/dream-location.blade.phpfront.pages.widgets.dream-location", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Fdream-location.blade.php&line=1", "ajax": false, "filename": "dream-location.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.dream-location"}, {"name": "1x front.pages.widgets.trusted-company", "param_count": null, "params": [], "start": 1750419490.777835, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/trusted-company.blade.phpfront.pages.widgets.trusted-company", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Ftrusted-company.blade.php&line=1", "ajax": false, "filename": "trusted-company.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.trusted-company"}, {"name": "1x front.pages.widgets.top-recruiters", "param_count": null, "params": [], "start": 1750419490.781075, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/top-recruiters.blade.phpfront.pages.widgets.top-recruiters", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Ftop-recruiters.blade.php&line=1", "ajax": false, "filename": "top-recruiters.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.top-recruiters"}, {"name": "1x front.pages.widgets.latest-blog", "param_count": null, "params": [], "start": 1750419490.807543, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-blog.blade.phpfront.pages.widgets.latest-blog", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Flatest-blog.blade.php&line=1", "ajax": false, "filename": "latest-blog.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.latest-blog"}, {"name": "1x front.layouts.master", "param_count": null, "params": [], "start": 1750419490.811558, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/master.blade.phpfront.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.master"}, {"name": "1x front.layouts.includes.head", "param_count": null, "params": [], "start": 1750419490.813132, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/head.blade.phpfront.layouts.includes.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.head"}, {"name": "1x css.theme-custom-css", "param_count": null, "params": [], "start": 1750419490.815776, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/css/theme-custom-css.blade.phpcss.theme-custom-css", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fcss%2Ftheme-custom-css.blade.php&line=1", "ajax": false, "filename": "theme-custom-css.blade.php", "line": "?"}, "render_count": 1, "name_original": "css.theme-custom-css"}, {"name": "1x front.layouts.includes.topbar", "param_count": null, "params": [], "start": 1750419490.848426, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/topbar.blade.phpfront.layouts.includes.topbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftopbar.blade.php&line=1", "ajax": false, "filename": "topbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.topbar"}, {"name": "2x front.layouts.includes.social-area", "param_count": null, "params": [], "start": 1750419490.884456, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/social-area.blade.phpfront.layouts.includes.social-area", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fsocial-area.blade.php&line=1", "ajax": false, "filename": "social-area.blade.php", "line": "?"}, "render_count": 2, "name_original": "front.layouts.includes.social-area"}, {"name": "1x front.layouts.includes.header", "param_count": null, "params": [], "start": 1750419490.916219, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/header.blade.phpfront.layouts.includes.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.header"}, {"name": "1x front.layouts.includes.logo", "param_count": null, "params": [], "start": 1750419490.956106, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/logo.blade.phpfront.layouts.includes.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.logo"}, {"name": "1x front.layouts.includes.mobile-logo", "param_count": null, "params": [], "start": **********.149388, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/mobile-logo.blade.phpfront.layouts.includes.mobile-logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmobile-logo.blade.php&line=1", "ajax": false, "filename": "mobile-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.mobile-logo"}, {"name": "1x front.layouts.includes.menu", "param_count": null, "params": [], "start": **********.15055, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/menu.blade.phpfront.layouts.includes.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.menu"}, {"name": "1x front.layouts.includes.mobile-menu-bottom", "param_count": null, "params": [], "start": **********.154818, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/mobile-menu-bottom.blade.phpfront.layouts.includes.mobile-menu-bottom", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmobile-menu-bottom.blade.php&line=1", "ajax": false, "filename": "mobile-menu-bottom.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.mobile-menu-bottom"}, {"name": "1x front.layouts.includes.navbar-right", "param_count": null, "params": [], "start": **********.188573, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/navbar-right.blade.phpfront.layouts.includes.navbar-right", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fnavbar-right.blade.php&line=1", "ajax": false, "filename": "navbar-right.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.navbar-right"}, {"name": "1x front.layouts.includes.footer", "param_count": null, "params": [], "start": **********.217992, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/footer.blade.phpfront.layouts.includes.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.footer"}, {"name": "1x front.layouts.includes.script", "param_count": null, "params": [], "start": **********.219542, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/script.blade.phpfront.layouts.includes.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.script"}, {"name": "1x js.translate", "param_count": null, "params": [], "start": **********.263403, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/translate.blade.phpjs.translate", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftranslate.blade.php&line=1", "ajax": false, "filename": "translate.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.translate"}, {"name": "1x js.theme-custom-js", "param_count": null, "params": [], "start": **********.274135, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/theme-custom-js.blade.phpjs.theme-custom-js", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftheme-custom-js.blade.php&line=1", "ajax": false, "filename": "theme-custom-js.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.theme-custom-js"}]}, "route": {"uri": "GET /", "middleware": "web, xss", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "home", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:17-33</a>"}, "queries": {"count": 43, "nb_statements": 43, "nb_visible_statements": 43, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.027640000000000005, "accumulated_duration_str": "27.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `pages` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\HomeRepository.php", "line": 16}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8678741, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HomeRepository.php:16", "source": {"index": 16, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\HomeRepository.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FHomeRepository.php&line=16", "ajax": false, "filename": "HomeRepository.php", "line": "16"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 2.279}, {"sql": "select * from `widgets_contents` where `widgets_contents`.`page_id` in (1) and `status` = 1 order by `position` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\HomeRepository.php", "line": 16}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.887665, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HomeRepository.php:16", "source": {"index": 21, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\HomeRepository.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FHomeRepository.php&line=16", "ajax": false, "filename": "HomeRepository.php", "line": "16"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 2.279, "width_percent": 2.713}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 3 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 3}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.262407, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 4.993, "width_percent": 2.424}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 426}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.280058, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Helper.php:426", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=426", "ajax": false, "filename": "Helper.php", "line": "426"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 7.417, "width_percent": 2.062}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 1 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.294853, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 9.479, "width_percent": 2.533}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 2 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.296661, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 12.012, "width_percent": 1.954}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 5 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.298185, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 13.965, "width_percent": 1.881}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 6 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.29966, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 15.847, "width_percent": 1.918}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 7 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.3011491, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 17.764, "width_percent": 1.845}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 8 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.30263, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 19.609, "width_percent": 1.99}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 9 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.304125, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 21.599, "width_percent": 2.026}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 10 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.305612, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 23.625, "width_percent": 1.99}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 11 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.3072908, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 25.615, "width_percent": 2.171}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 12 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.3096921, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 27.786, "width_percent": 3.003}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 13 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.311573, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 30.789, "width_percent": 1.918}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 14 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.3130481, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 32.706, "width_percent": 1.881}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 15 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.314492, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 34.588, "width_percent": 1.809}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 16 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.315934, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 36.397, "width_percent": 1.809}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 17 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.317385, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 38.205, "width_percent": 1.809}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 18 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.318799, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 40.014, "width_percent": 1.773}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 19 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.320246, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 41.787, "width_percent": 2.026}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 20 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.32172, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 43.813, "width_percent": 1.954}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 34 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.323201, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 45.767, "width_percent": 1.918}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 35 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.3246398, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 47.685, "width_percent": 3.437}, {"sql": "select count(`id`) as aggregate from `jobs` where `job_deadline` >= '2025-06-20 11:38:10'", "type": "query", "params": [], "bindings": ["2025-06-20 11:38:10"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.353424, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1228", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1228}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1228", "ajax": false, "filename": "Helper.php", "line": "1228"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 51.122, "width_percent": 2.243}, {"sql": "select count(`id`) as aggregate from `candidates`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1242}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.420115, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1242", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1242}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1242", "ajax": false, "filename": "Helper.php", "line": "1242"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 53.365, "width_percent": 1.881}, {"sql": "select count(`id`) as aggregate from `jobs` where `job_deadline` >= '2025-06-13 11:38:10'", "type": "query", "params": [], "bindings": ["2025-06-13 11:38:10"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1249}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.421556, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1249", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1249", "ajax": false, "filename": "Helper.php", "line": "1249"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 55.246, "width_percent": 1.773}, {"sql": "select count(`id`) as aggregate from `companies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1235}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.4577231, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Helper.php:1235", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1235}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1235", "ajax": false, "filename": "Helper.php", "line": "1235"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 57.019, "width_percent": 4.92}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 111 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.485014, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 61.939, "width_percent": 2.533}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 5 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.514347, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 64.472, "width_percent": 2.388}, {"sql": "select `jobs`.*, (select `name` from `countries` where `jobs`.`country_id` = `countries`.`id`) as `country_name`, (select `name` from `cities` where `jobs`.`city_id` = `cities`.`id`) as `city_name` from `jobs` where exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `status` = 1 and `job_deadline` >= '2025-06-20 11:38:10' and `job_featured` = 1 order by `id` desc limit 6", "type": "query", "params": [], "bindings": [1, 1, 1, 1, "2025-06-20 11:38:10", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.711067, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Helper.php:650", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=650", "ajax": false, "filename": "Helper.php", "line": "650"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.86, "width_percent": 4.088}, {"sql": "select `id`, `company_name`, `slug`, `status` from `companies` where `companies`.`id` in (26, 46, 63, 65) and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.713506, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Helper.php:650", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=650", "ajax": false, "filename": "Helper.php", "line": "650"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 70.948, "width_percent": 2.135}, {"sql": "select * from `job_job_types` where `job_job_types`.`job_id` in (21, 24, 25, 27, 29, 32)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.716569, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Helper.php:650", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=650", "ajax": false, "filename": "Helper.php", "line": "650"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 73.082, "width_percent": 2.207}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 96 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.working-process", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/working-process.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.7439651, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 75.289, "width_percent": 2.388}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 105 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [105], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.dream-location", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/dream-location.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.769085, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.677, "width_percent": 2.424}, {"sql": "select `name`, `location_image`, (select `name` from `countries` where `cities`.`country_id` = `countries`.`id`) as `country_name`, (select count(*) from `jobs` where `cities`.`id` = `jobs`.`city_id`) as `jobs_count` from `cities` where exists (select `id`, `city_id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `cities`.`id` = `jobs`.`city_id` and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `job_deadline` >= '2025-06-20 11:38:10' and `status` = 1) and `status` = 1 limit 6", "type": "query", "params": [], "bindings": [1, 1, 1, "2025-06-20 11:38:10", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 742}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.77267, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Helper.php:742", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=742", "ajax": false, "filename": "Helper.php", "line": "742"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.101, "width_percent": 3.654}, {"sql": "select `id`, `city_id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `jobs`.`city_id` in (0) and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `job_deadline` >= '2025-06-20 11:38:10' and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, 1, "2025-06-20 11:38:10", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 742}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.775722, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Helper.php:742", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=742", "ajax": false, "filename": "Helper.php", "line": "742"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 83.755, "width_percent": 3.365}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 106 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.trusted-company", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/trusted-company.blade.php", "line": 4}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.7793782, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 87.12, "width_percent": 2.352}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 10 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.top-recruiters", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/top-recruiters.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.8054302, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 89.472, "width_percent": 2.279}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 136 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [136], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-blog", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-blog.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1750419490.809688, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 91.751, "width_percent": 2.424}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "front.layouts.includes.topbar", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/topbar.blade.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1750419490.852221, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.topbar:48", "source": {"index": 16, "namespace": "view", "name": "front.layouts.includes.topbar", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/topbar.blade.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftopbar.blade.php&line=48", "ajax": false, "filename": "topbar.blade.php", "line": "48"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 94.175, "width_percent": 2.098}, {"sql": "select * from `menu_items` where (`menu_id` = 2 and `parent_id` is null) order by `order` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.151701, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 96.274, "width_percent": 2.315}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (215, 216, 231, 232, 233, 235) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.153385, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 98.589, "width_percent": 1.411}]}, "models": {"data": {"App\\Models\\Admin\\CategoryTranslation": {"value": 88, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategoryTranslation.php&line=1", "ajax": false, "filename": "CategoryTranslation.php", "line": "?"}}, "App\\Models\\Admin\\WidgetContentTranslation": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetContentTranslation.php&line=1", "ajax": false, "filename": "WidgetContentTranslation.php", "line": "?"}}, "App\\Models\\Admin\\Category": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Admin\\WidgetsContents": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=1", "ajax": false, "filename": "WidgetsContents.php", "line": "?"}}, "App\\Models\\Admin\\JobJobType": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJobJobType.php&line=1", "ajax": false, "filename": "JobJobType.php", "line": "?"}}, "App\\Models\\Admin\\Job": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Admin\\City": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCity.php&line=1", "ajax": false, "filename": "City.php", "line": "?"}}, "App\\Models\\Admin\\MenuItem": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin\\Company": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Admin\\Pages": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FPages.php&line=1", "ajax": false, "filename": "Pages.php", "line": "?"}}}, "count": 182, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/pages/edit/1?lang=en\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/assets/image/icon/apple-icon.svg\"\n]", "locale": "en", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:17-33</a>", "middleware": "web, xss", "duration": "10.16s", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-320491469 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-320491469\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-822212266 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-822212266\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2045424046 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6IklUaHJMMXpKUTRsQVlTSXlSYTNPL3c9PSIsInZhbHVlIjoiYURpUW1iaCsxUGF0Y2FaNXliY3FsQVc3dVdMNVlqcml3YldUTkZyQVlOR1RwY2liUHZlTmZ2c2NxV2hlSVZQWnVQOW1FNGxDOHZ4NEFNd0l0L2FSL1k0UzIyeG5OOTFXcjZuVXBsMTFjSXNSUmdLMG0rN0llTDZaMWFrVTJNUXUiLCJtYWMiOiJjZDk1NjU4YWIwZmMzYjM2NzYwZTI4NTVmMWU0MTM3MjFiOGI5MzY2MjA1MDI1MTMyYzRjYTkwOGVlZDE1MmEzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImQ1T2FReEhLa0I3SWE5Qm1Cd0U2Q2c9PSIsInZhbHVlIjoiMkZ0T1cyaEpjUUMyZCtiSXRuS1c4WDA1NUkwcExtN0x6K0hBR1VydGRROE5FR2pJRTZMR1dZOWkrT3F4ODBxN0ZxU25jU2RzOTl6NXNVdGc4aUlwRnQyRTdiY1U0T09yejZjZXhNUWtjSGcvaFJnbmM1YjR0c0ttY0xJb3NOMFgiLCJtYWMiOiI2YjkyNWUyYjZmNjZjN2I3N2UzMjU2ZjRmM2ViYWNkOGE3ZWQ1NDc2OWNiYTJjYjg1ZmYwOWRhZjE2M2E0YWMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045424046\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1681113041 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">73NVybykaJRblf91BMgO3Gbz1VJmhdzXGQr1QbOg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681113041\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-26330711 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 11:38:09 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26330711\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1036390346 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/admin/pages/edit/1?lang=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">http://127.0.0.1:8000/assets/image/icon/apple-icon.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036390346\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index"}, "badge": null}}