<?php

namespace App\Classes;

use App\Enums\EmailFormatStatus;
use App\Models\Admin\EmailTemplate;
use App\Notifications\JobApply;
use App\Notifications\Notify;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class EmailFormat
{
    public function __construct(protected EmailTemplate $model) {}

    /**
     * interview
     *
     * @param  mixed  $data
     * @param  mixed  $array
     * @return response
     */
    public function interview($data, $array = [])
    {

        $name = $data->user->first_name.' '.$data->user->last_name;
        $emailContent = $this->model->where('caption', EmailFormatStatus::INTERVIEW)->first();
        $email_content = str_replace(['{_candidateName_}', '{_companyName_}', '{_jobTitle_}', '{_date_}', '{_time_}'], [$name, $data->company_name, $data->job_title, $array['date'], $array['time']], $emailContent->email_content);
        $details = [
            'company' => $data->company_name,
            'email' => $data->user->email,
            'email_subject' => $emailContent->email_subject,
            'email_body' => $email_content,
        ];
        $this->sendMail($details);
    }

    public function joningLetter($data, $array = [])
    {

        $name = $data->user->first_name.' '.$data->user->last_name;
        $emailContent = $this->model->where('caption', EmailFormatStatus::JOINING_LETTER)->first();
        $email_content = str_replace(['{_candidateName_}', '{_companyName_}', '{_jobTitle_}', '{_date_}'], [$name, $data->company_name, $data->job_title, $array['date']], $emailContent->email_content);
        $details = [
            'company' => $data->company_name,
            'email' => $data->user->email,
            'email_subject' => $emailContent->email_subject,
            'email_body' => $email_content,
        ];
        $this->sendMail($details);
    }

    /** account verify confirmation mail
     * ======= confirmationMailSend ==========
     */
    public function companyVerifyMail($data, $array = null)
    {

        try {

            $emailContent = $this->model->where('caption', EmailFormatStatus::COMPANY_VERIFY_MAIL)->first();
            $email_content = str_replace('{_companyName_}', $array['company_name'], $emailContent->email_content);
            $confirmMailLink = "<a href='".asset('account/verify/'.$array['token'])."'>Confirm Your Account</a>";
            $email_content = str_replace('{_link_}', $confirmMailLink, $email_content);

            $details = [
                'email' => $data->email,
                'name' => $data->username,
                'email_subject' => $emailContent->email_subject,
                'email_body' => $email_content,

            ];
            $this->sendMail($details);

        } catch (\Throwable $th) {
            //throw $th;
        }

    }

    /** account verify confirmation mail
     * ======= confirmationMailSend ==========
     */
    public function condidateVerifyMail($data, $array = null)
    {

        try {

            $emailContent = $this->model->where('caption', EmailFormatStatus::CANDIDATE_VERIFY_MAIL)->first();
            $email_content = str_replace(['{_companyName_}'], [$array['company_name']], $emailContent->email_content);
            $confirmMailLink = "<a href='".asset('account/verify/'.$array['token'])."'>Confirm Your Account</a>";
            $email_content = str_replace('{_link_}', $confirmMailLink, $email_content);

            $details = [
                'email' => $data->email,
                'name' => $data->username,
                'email_subject' => $emailContent->email_subject,
                'email_body' => $email_content,

            ];
            $this->sendMail($details);

        } catch (\Throwable $th) {
            //throw $th;
        }

    }

    /**
     * condidateConfirmationMail
     *
     * @param  mixed  $data
     * @param  mixed  $array
     * @return void
     */
    public function condidateConfirmationMail($data, $array = null)
    {

        try {

            $emailContent = $this->model->where('caption', EmailFormatStatus::CANDIDATE_CONFIRMATION_EMAIL)->first();
            $email_content = str_replace(['{_companyName_}'], [$array['company_name']], $emailContent->email_content);
            $details = [
                'email' => $data->email,
                'name' => $data->username,
                'email_subject' => $emailContent->email_subject,
                'email_body' => $email_content,

            ];
            $this->sendMail($details);

        } catch (\Throwable $th) {
            //throw $th;
        }

    }

    /**
     * companyConfirmationMail
     *
     * @param  mixed  $data
     * @param  mixed  $array
     * @return void
     */
    public function companyConfirmationMail($data, $array = null)
    {

        try {

            $emailContent = $this->model->where('caption', EmailFormatStatus::COMPANY_CONFIRMATION_EMAIL)->first();
            $email_content = str_replace(['{_companyName_}'], [$array['company_name']], $emailContent->email_content);
            $details = [
                'email' => $data->email,
                'name' => $data->username,
                'email_subject' => $emailContent->email_subject,
                'email_body' => $email_content,

            ];
            $this->sendMail($details);

        } catch (\Throwable $th) {
            //throw $th;
        }
    }

    /**
     * subscriptionMail
     *
     * @param  mixed  $data
     * @param  mixed  $array
     * @return void
     */
    public function subscriptionMail($data, $array = null)
    {

        try {

            $emailContent = $this->model->where('caption', EmailFormatStatus::COMPANY_SUBSCRIPTION)->first();
            $email_content = str_replace(['{_companyName_}'], [$array['company_name']], $emailContent->email_content);
            $details = [
                'email' => $data->email,
                'name' => $data->username,
                'email_subject' => $emailContent->email_subject,
                'email_body' => $email_content,
            ];
            $this->sendMail($details);
        } catch (\Throwable $th) {
            //throw $th;
        }

    }

    public function applyJob($data)
    {

        $emailContent = $this->model->where('caption', EmailFormatStatus::APPLIED_JOB)->first();
        $email_content = str_replace(['{_companyName_}', '{_candidat﻿eName_}', '{_job_title_}'], [$data['company_name'], $data['user_name'], $data['job_title']], $emailContent->email_content);
        $details = [
            'email' => $data['email'],
            'name' => $data['user_name'],
            'email_subject' => $emailContent->email_subject,
            'email_body' => $email_content,
        ];
        $this->sendMail($details);
    }

    /** account verify confirmation mail
     * ======= confirmationMailSend ==========
     */
    public function mailSend($data, $mailType, $type)
    {

        try {

            $emailContent = EmailTemplate::where('id', $mailType)->first();

            $email_content = str_replace('{_companyName_}', $data['company_name'], $emailContent->email_content);

            if ($type == 'forgot') {
                $resetLink = "<a href='".asset('reset-password/'.$data['email'].'/'.$data['token'])."'>Link</a>";
            } elseif ($type == 'reset') {
                $resetLink = "<a href='".asset('login/')."'>Link</a>";
            }

            $email_content = str_replace('{_link_}', $resetLink, $email_content);

            $details = [
                'company' => $data['company_name'],
                'email' => $data['email'],
                'email_subject' => $emailContent->email_subject,
                'email_body' => $email_content,
            ];

            Mail::send([], $details, function ($message) use ($details, $data) {
                $message->to($data['email']);
                $message->subject($details['email_subject']);
                $message->html($details['email_body']);
            });

        } catch (\Throwable $th) {
            //throw $th;
            return false;
        }
    }

    /**
     * sendMail
     *
     * @param  array  $details
     * @return void
     */
    public function sendMail($details)
    {

        Mail::send([], $details, function ($message) use ($details) {
            $message->to($details['email']);
            $message->subject($details['email_subject']);
            $message->html($details['email_body']);
        });

    }

    /**
     * jobApplyNotify
     *
     * @param  array  $data
     * @return void
     */
    public function jobApplyNotify($senderTo, $data)
    {

        $emailContent = $this->model->where('caption', EmailFormatStatus::JOB_APPLY_NOTIFY)->first();
        $email_content = str_replace(['{_user_name_}', '{_job_title_}'], [$data['user_name'], $data['job_title']], $emailContent->email_content);
        $details = [
            'title' => $emailContent->email_subject,
            'message' => $email_content,
        ];
        Notification::send($senderTo, new JobApply($details));
    }

    /**
     * interview
     *
     * @param  mixed  $data
     * @param  mixed  $array
     * @return response
     */
    public function interviewNotify($senderTo, $data)
    {

        $emailContent = $this->model->where('caption', EmailFormatStatus::INTERVIEW)->first();
        $email_content = str_replace(['{_candidateName_}', '{_companyName_}', '{_jobTitle_}', '{_date_}', '{_time_}'], [$data['name'], $data['company_name'], $data['job_title'], $data['date'], $data['time']], $emailContent->email_content);
        $details = [
            'title' => $emailContent->email_subject,
            'message' => $email_content,
        ];
        Notification::send($senderTo, new Notify($details));
    }

    /**
     * interview
     *
     * @param  mixed  $data
     * @param  mixed  $array
     * @return response
     */
    public function jobRejectNotify($senderTo, $data)
    {

        $emailContent = $this->model->where('caption', EmailFormatStatus::JOB_REJECT_NOTIFY)->first();
        $email_content = str_replace(['{_candidateName_}', '{_companyName_}', '{_jobTitle_}'], [$data['name'], $data['company_name'], $data['job_title']], $emailContent->email_content);
        $details = [
            'title' => $emailContent->email_subject,
            'message' => $email_content,
        ];
        Notification::send($senderTo, new Notify($details));
    }

    /**
     * joningNotify
     *
     * @param  mixed  $data
     * @param  mixed  $array
     * @return response
     */
    public function joningNotify($senderTo, $data)
    {

        $emailContent = $this->model->where('caption', EmailFormatStatus::JOINING_LETTER)->first();
        $email_content = str_replace(['{_candidateName_}', '{_companyName_}', '{_jobTitle_}', '{_date_}'], [$data['name'], $data['company_name'], $data['job_title'], $data['date']], $emailContent->email_content);
        $details = [
            'title' => $emailContent->email_subject,
            'message' => $email_content,
        ];
        Notification::send($senderTo, new Notify($details));

    }
}
