<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\JobExperienceRequest;
use App\Repositories\Admin\JobExperienceRepostitory;
use Illuminate\Http\Request;

class JobExperienceController extends Controller
{
    //

    public function __construct(protected JobExperienceRepostitory $jonExperience) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->jonExperience->content();
        }

        return view('admin.pages.experience.index');
    }

    /** new Job Sub Category store
     * ========= store============
     *
     * @param JobExperienceRequest
     * @return Response
     */
    public function store(JobExperienceRequest $request)
    {
        if (!$this->hasPermissions(['jobexperience.add', 'jobexperience.edit'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jonExperience->create($request);
        return  $this->formatResponse($result);


    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $jonExperience = $this->jonExperience->getById($id);
        $data['name'] = $jonExperience->getTranslation('name', Request()->lang);
        $data['id'] = $jonExperience->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['jobexperience.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jonExperience->destroy($id);
        return response()->json(['status' => $result['status']]);

    }

    /** Job Type status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['jobexperience.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jonExperience->statusChange($id);
        return $this->formatResponse($result);

    }
}
