<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\PermissionRequest;
use App\Repositories\Admin\PermissionRepository;
use Spatie\Permission\Models\Permission;

class PermissionController extends Controller
{
    public function __construct(protected PermissionRepository $permission) {}

    /**index
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = Permission::all();
        return view('admin.pages.permission.index', compact('data'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(PermissionRequest $request)
    {
        if (!$this->hasPermissions(['permission.add'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->permission->create($request);
        return response()->json([
            'status'  => $result['status'],
            'message' => $result['message'],
            'url'     => route('admin.permission.index'),
        ]);


    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $permission = $this->permission->getById($id);
        return response()->json($permission);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(PermissionRequest $request)
    {
        if (!$this->hasPermissions(['permission.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->permission->update($request->id, $request);

        return response()->json([
            'status'  => $result['status'],
            'message' => $result['message'],
            'url'     => route('admin.permission.index'),
        ]);

    }
}
