<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionController extends Controller
{
    /**
     * index
     *
     * @return View
     */
    public function index()
    {
        $data = Role::where('guard_name', 'admin')->get();
        return view('admin.pages.roles.index', compact('data'));
    }

    /**
     * create
     *
     * @return View
     */
    public function create()
    {
        $groupbyname = Admin::groupByName();
        $permission = Permission::latest()->get();
        return view('admin.pages.roles.create', compact('permission', 'groupbyname'));
    }

    /**
     * store
     *
     * @param  mixed  $request
     * @return void
     */
    public function store(Request $request)
    {

        if (!$this->hasPermissions(['role.add'])) {
            toastr()->error(translate('You have no Permission'));
            return redirect()->back();
        }

        $permission = $request->input('permissions');
        if (!empty($permission)) {
            $role = Role::create(['name' => $request->name]);
            $role->syncPermissions($permission);
        }
        toastr()->success(translate('Role created successfully'));
        return redirect()->route('admin.role.list');

    }

    /**
     * edit
     *
     * @param  mixed  $id
     * @return void
     */
    public function edit($id)
    {
        $role = Role::where('id', $id)->first();
        $groupbyname = Admin::groupByName();
        $permissions = Permission::orderBy('group_name')->get()->groupBy('group_name');

        return view('admin.pages.roles.edit', compact('permissions', 'groupbyname', 'role'));
    }

    /**
     * update
     *
     * @param  mixed  $request
     * @param  mixed  $id
     * @return void
     */
    public function update(Request $request, $id)
    {
        if (!$this->hasPermissions(['role.edit'])) {

            toastr()->error(translate('You have no Permission'), 'warning');
            return redirect()->back();
        }
        $role = Role::findById($id);
        $permission = $request->input('permissions');

        if (!empty($permission)) {
            $role->syncPermissions($permission);
        }

        toastr()->success(translate('Role updated successfully'));
        return redirect()->route('admin.role.list');

    }

    /**
     * delete
     *
     * @param  mixed  $id
     * @return void
     */
    public function delete($id, Request $request)
    {
        if (!$this->hasPermissions(['role.delete'])) {
            toastr()->error(translate('You have no Permission'), 'warning');
           return redirect()->back();
        }

        $delete = Role::where('id', $id)->delete();
        if ($delete) {
            toastr()->success(translate('Role deleted successfully'));
        } else {
            toastr()->warning(translate('Something went wrong'));
        }

        return redirect()->back();

    }
}
