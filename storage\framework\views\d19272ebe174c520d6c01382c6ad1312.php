<script>
    (function($) {
        "use strict";
        let save = " <?php echo e(translate('Save')); ?>";
        let close = " <?php echo e(translate('Close')); ?>";
        let addForm = " <?php echo e(translate('Add Form')); ?>";
        let editForm = " <?php echo e(translate('Edit Form')); ?>";
        let update = " <?php echo e(translate('Update')); ?>";
        let table = $('#datatable').DataTable({
            processing: true,
            serverSide: true,
            destroy: true,
            order: [
                [0, 'desc']
            ],
            ajax: {
                url: "<?php echo e(route('admin.job')); ?>",
            },
            columns: [{
                    data: 'Id',
                    name: 'Id'
                },
                {
                    data: 'Title',
                    name: 'Title'
                },

                {
                    data: 'Salary',
                    name: 'Salary'
                },
                {
                    data: 'Vacancy',
                    name: 'Vacancy'
                },

                {
                    data: 'Deadline',
                    name: 'Deadline'
                },
                {
                    data: 'Job featured',
                    name: 'Job featured'
                },

                {
                    data: 'Status',
                    name: 'Status'
                },

                {
                    data: '',
                    name: ''
                },


                {
                    data: 'action',
                    name: 'action',
                    orderable: false
                }
            ]
        });
        // modal show

        $(document).on('click', '.deleteJob', function(e) {
            e.preventDefault();
            var id = $(this).data('id');

            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '/admin/job/delete/' + id,
                        type: "GET",
                        dataType: "JSON",
                        success: function(data) {
                            table.draw();
                            if (data.hasOwnProperty('message')) {
                                Swal.fire(
                                    'error!',
                                    `${data.message}`,
                                )
                            } else {
                                Swal.fire(
                                    'Deleted!',
                                    'Your file has been deleted.',
                                    'success'
                                )
                            }
                        },
                    });

                } else if (
                    result.dismiss === Swal.DismissReason.cancel
                ) {
                    Swal.fire(
                        'Cancelled',
                        'Your file is safe :)',
                        'error'
                    )
                }
            })
        });
        //========== status

        $(document).on('change', '.status-change', function(e) {
            e.preventDefault();
            let action = $(this).data('action');
            $.ajax({
                url: action,
                type: "GET",
                dataType: "JSON",
                success: function(data) {
                    if (data.status === true) {
                        toastr["success"](`${data.message}`);
                        table.draw();
                    } else if (data.status == false) {
                        toastr["error"](`${data.message}`);
                    }

                },
            });
        });
    })(jQuery);
</script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/admin/job.blade.php ENDPATH**/ ?>