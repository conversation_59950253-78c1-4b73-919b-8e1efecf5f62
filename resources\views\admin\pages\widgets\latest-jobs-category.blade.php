@if (@isset($widgetContent))
    <div class="sortable-item accordion-item allowPrimary" data-code="{{ $widgetContent->ui_card_number }}">
        <div class="accordion-header">
            <div class="section-name"> {{ $widgetContent->widget?->widget_name }}
                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="{{ route('admin.pages.widget.status.change', $widgetContent->id) }}"
                            {{ $widgetContent->status == 1 ? 'checked' : '' }} type="checkbox" role="switch"
                            id="{{ $widgetContent->id }}">
                        <label class="form-check-label" for="{{ $widgetContent->id }}"> </label>
                    </div>

                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="{{ $widgetContent->id }}">
                        <i class="bi bi-trash"></i>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse">
            <div class="accordion-body">

                @php
                 $widgetContents= $widgetContent->getTranslation("widget_content",$lang);
               @endphp

                <form enctype="multipart/form-data" class="form" data-action="{{ route('admin.pages.widget.save') }}"
                    method="POST">
                    @csrf

                    <input type="hidden" name="ui_card_number" value="{{ $widgetContent->ui_card_number }}">
                    <input type="hidden" name="page_id" value="{{ $widgetContent->page_id }}">
                    <input type="hidden" name="widget_slug" class="widget-slug" value="{{ $widgetContent->widget_slug }}">

                    <div class="row">
                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Title') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Enter Title') }}" name="content[0][title]"
                                    value="{{ isset($widgetContents['title']) ? $widgetContents['title'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Show Number of Item') }}</label>
                                <input type="number" class="username-input"
                                    placeholder="{{ translate('Show Number of item') }}" min="1"
                                    name="content[0][show_item]"
                                    value="{{ isset($widgetContents['show_item']) ? $widgetContents['show_item'] : '' }}">
                            </div>
                        </div>
                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('All View Page Link') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Page Link') }}"
                                    name="content[0][page_link]"
                                    value="{{ isset($widgetContents['page_link']) ? $widgetContents['page_link'] : '' }}">
                            </div>
                        </div>
                    </div>
                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow">{{ translate('Update') }}</button>
                    </div>
                </form>
            </div>
        </div>

    </div>
@else
    <div class="sortable-item accordion-item allowPrimary" data-code="{{ $randomId }}">
        <div class="accordion-header">
            <div class="section-name"> {{ $widgetName }}
                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change" checked
                            data-action="{{ route('admin.pages.widget.status.change', $randomId) }}" type="checkbox"
                            role="switch" id="{{ $randomId }}">
                        <label class="form-check-label" for="{{ $randomId }}"> </label>
                    </div>
                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="{{ $randomId }}">
                        <i class="bi bi-trash"></i>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse show">
            <div class="accordion-body">
                <form enctype="multipart/form-data" class="form" data-action="{{ route('admin.pages.widget.save') }}"
                    method="POST">
                    @csrf

                    <input type="hidden" name="ui_card_number" value="{{ $randomId }}">
                    <input type="hidden" name="page_id" value="{{ $pageId }}">
                    <input type="hidden" name="widget_slug" class="widget-slug" value="{{ $slug }}">

                    <div class="row">
                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Title') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Enter Title') }}" name="content[0][title]">
                            </div>
                        </div>
                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('Show Number of Item') }}</label>
                                <input type="number" class="username-input"
                                    placeholder="{{ translate('Show Number of item') }}" min="1"
                                    name="content[0][show_item]">
                            </div>
                        </div>
                        <div class="col-sm-4 mb-2">
                            <div class="form-inner">
                                <label>{{ translate('All View Page Link') }}</label>
                                <input type="text" class="username-input"
                                    placeholder="{{ translate('Page Link') }}"
                                    name="content[0][page_link]">
                            </div>
                        </div>
                    </div>
                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow">{{ translate('Save') }}</button>
                    </div>
                </form>
            </div>
        </div>

    </div>
@endif
