<?php

namespace App\Http\Controllers\Company;

use App\Classes\EmailFormat;
use App\Enums\HiringStatus;
use App\Exports\AppliedUserListExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterCompanyRequest;
use App\Models\Admin\Candidate;
use App\Models\Admin\EducationLavel;
use App\Models\Admin\Job;
use App\Models\Front\ApplyJob;
use App\Models\User;
use App\Repositories\Admin\CityRepository;
use App\Repositories\CandidateRepository;
use App\Repositories\Companies\DeshboardRepository;
use App\Repositories\CompanyRepository;
use App\Repositories\JobRepository;
use App\Repositories\UserRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class CompanyDashboardController extends Controller
{
    public function __construct(
        protected DeshboardRepository $company,
        protected CompanyRepository $companyUser,
        protected JobRepository $job,
        protected UserRepository $user,
        protected CityRepository $city,
        protected CandidateRepository $candidate,
        protected EmailFormat $emailFormat
    ) {}

    /**
     * Display a listing of the resource.
     *=========  companyDashboard ===============

     *
     * @param Request
     * @return \Illuminate\Http\Response
     */
    public function dashboardContent(Request $request)
    {


        $data = $this->company->dashboardInfo();
        $appliedJobLists = $this->company->applyJobs($request);
        return view('front.pages.company-dashboard.dashboard', compact('appliedJobLists', 'data'));
    }

    /**
     *===companyProfile===
     *
     * @return \Illuminate\Http\Response
     */
    public function companyProfile()
    {
        $company = $this->companyUser->getById(Auth::user()->id);

        return view('front.pages.company-dashboard.profile', compact('company'));
    }

    /** company update
     * ==== companyUpdate =====
     *
     * @param RegisterCompanyRequest
     * @return message;
     */
    public function companyUpdate(RegisterCompanyRequest $request)
    {

        $status = $this->companyUser->update($request);
        if ($status['status'] == true) {
            return response()->json([
                'status' => true,
                'message' => 'Update Successfully',
                'url' => route('company.profile')
            ]);
        }

        return response()->json(['status' => false, 'message' => $status['message']]);
    }

    public function companySingleJobDetails($slug)
    {

        $request = Request();
        $options = [];
        $experience = '';
        $jobType = '';
        $showItem = 7;

        if (isset($request->status)) {
            $options['status'] = $request->status;
        }
        if (isset($request->showItem)) {
            $showItem = $request->showItem;
        }
        if (isset($request->experience)) {
            preg_match_all('!\d+!', $request->experience, $experiences);
            $experience = $experiences[0];
        }

        if (isset($request->jobType)) {
            $jobtype = $request->jobType;
        }

        try {
            $singleJob = Job::with('company', 'applieduser', 'category', 'jobtype', 'jobLevels', 'experiences', 'jobJobTypes')
                ->where('company_id', authCheck()->company->id)
                ->where('slug', $slug)
                ->first();

            $appliedJob = ApplyJob::query();

            if (! empty($experience)) {
                $appliedJob->withWhereHas('candidate', function ($query) use ($experience) {
                    return $query->whereIn('candidate_experience', $experience);
                });
            }

            if (! empty($jobtype)) {
                $appliedJob->withWhereHas('candidate', function ($query) use ($jobtype) {
                    return $query->where('candidate_available_time', $jobtype);
                });
            }

            $appliedUsers = $appliedJob->with('candidate.user')
                ->where(['job_id' => $singleJob->id, 'company_id' => authCheck()->company->id])
                ->where($options)
                ->paginate($showItem ?? 7);

            if ($request->ajax()) {
                $appliedContent = view('front.pages.company-dashboard.apply-job-applicant', compact('appliedUsers'))->render();

                return response()->json(['status' => true, 'applicant' => $appliedContent]);
            }

            return view('front.pages.company-dashboard.apply-job-details', compact('singleJob', 'appliedUsers', 'slug'));
        } catch (\Throwable $th) {
            return view('front.pages.404');
        }
    }

    /** company profile setting
     *=========== companySettings============
     *
     * @return \Illuminate\Http\Response
     */
    public function companySettings()
    {
        $authEmail = Auth::user()->email;
        $user = User::with('company')->where('email', $authEmail)->first();

        return view('front.pages.company-dashboard.settings', compact('user'));
    }

    /** company change Password
     *=========== changeCompanyPassword============

     *
     * @return response
     */
    public function changeCompanyPassword(Request $request)
    {
        $result = $this->user->changePassword($request);
        toastr()->{$result['status'] ? 'success' : 'warning'}('', $result['message'], ['positionClass' => 'toast-top-right']);
        return $result['status'] ? redirect()->route('auth.user.login') : redirect()->back();
    }

    /** company phone email change
     *============= companyPhoneEmailChange =============
     *
     * @param Request
     * @return response
     */
    public function companyPhoneEmailChange(Request $request)
    {
        $result = $this->companyUser->emailChange($request);
        toastr()->{$result['status'] ? 'success' : 'error'}('', $result['message'], ['positionClass' => 'toast-top-right']);
        return redirect()->back();
    }

    /**
     * jobViewed
     *
     * @param  mixed  $request
     * @return Response
     */
    public function jobViewed(Request $request)
    {
        try {
            $oldStatus = ApplyJob::find($request->id);
            if ($oldStatus->status == 1) {
                $status = ApplyJob::where('id', $request->id)->update(['status' => 2]);
            }

            return response()->json([
                'status' => true,
                'msg' => 'successfully cv viewed',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'msg' => $e->getMessage(),
            ]);
        }
    }

    /**
     * appliedUserListExport
     *
     * @param  mixed  $id
     * @return void
     */
    public function appliedUserListExport($id)
    {
        return Excel::download(new AppliedUserListExport($id), 'appliedListExcel.xlsx');
    }

    /**
     * applicantDownload
     *
     * @return void
     */
    public function applicantDownload()
    {

        $request = Request();
        $options = [];
        $experience = '';
        $jobType = '';
        $showItem = 7;

        if (isset($request->status)) {
            $options['status'] = $request->status;
        }
        if (isset($request->showItem)) {
            $showItem = $request->page;
        }
        if (isset($request->experience)) {
            preg_match_all('!\d+!', $request->experience, $experiences);
            $experience = $experiences[0];
        }

        if (isset($request->jobType)) {
            $jobType = $request->jobType;
        }

        $singleJob = Job::where('company_id', authCheck()->company->id)
            ->where('slug', $request->slug)
            ->first();

        $appliedJob = ApplyJob::query();

        if (! empty($experience)) {
            $appliedJob->withWhereHas('candidate', function ($query) use ($experience) {
                return $query->whereIn('candidate_experience', $experience);
            });
        }

        if (! empty($jobtype)) {
            $appliedJob->withWhereHas('candidate', function ($query) use ($jobtype) {
                return $query->where('candidate_available_time', $jobtype);
            });
        }
        $appliedUsers = $appliedJob->with('candidate.user', 'job')
            ->where(['job_id' => $singleJob->id, 'company_id' => authCheck()->company->id])
            ->where($options)
            ->paginate($showItem);

        $fileName = date('d-m-Y') . '_candidate-list.csv';
        $headers = [
            'Content-type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=$fileName",
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];

        $columns = [
            'SL',
            'Job Title',
            'Name',
            'Email',
            'Phone',
            'Experience',
            'Applied On',
            'Address',
            'Salary Type',
            'Salary Range',
            "Extra Info",
            'Status',
        ];

        $callback = function () use ($appliedUsers, $columns) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $columns);
            $i = 0;
            foreach ($appliedUsers as $appliedUser) {
                $job = $appliedUser->job ?? '';
                  $extraInfo =  "";
                if ($appliedUser->extra_information) {
                    $extraInfo= $this->extraInfo($appliedUser->extra_information);
                }

                $i++;
                $row['SL'] = $i;
                $row['Job Title'] = $job->job_title;
                $row['Name'] = $appliedUser->user->first_name . '' . $appliedUser->user->last_name;
                $row['Email'] = $appliedUser->user->email;
                $row['Phone'] = $appliedUser->phone;
                $row['Experience'] = $appliedUser?->experience;
                $row['Applied On'] = customDateFormate($appliedUser?->created_at, $format = 'd M, Y');
                $row['Address'] = $appliedUser?->address;
                $row['Salary Type'] = ucfirst($job?->salary_type);
                $row['Salary Range'] = $job?->salary_mode == 'range' ? ($job?->min_salary . '-' . $job?->max_salary) : $job?->salary_mode;
                $row['Extra Info'] =   $extraInfo ?? '';
                $row['Status'] = $this->applicantStatus($appliedUser->status);
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }



    public function extraInfo($jsonData){
        $dataArray = $jsonData ?? [];
        $formattedData = [];
        foreach ($dataArray as $item) {
            foreach ($item as $key => $value) {
                // Format key by replacing "_" with space and capitalizing words
                $formattedKey = ucwords(str_replace('_', ' ', $key));
                // Append formatted key-value pair as a string
                $formattedData[] = "$formattedKey: $value";
            }
        }
        $finalString = implode(', ', $formattedData);
        return $finalString;
    }



    /**
     * applyJobStatus
     *
     * @param  mixed  $id
     * @return void
     */
    public function applyJobStatus($id)
    {

        try {

            $applyJob = ApplyJob::jobInfo()->with('user')->where('id', $id)->first();
            $applyJob->status = Request()->status_id;
            switch ($applyJob->status) {
                case HiringStatus::SHORTLIST:
                    $applyJob->update();
                    $message = translate('ShortList has been Successfully');
                    break;

                case HiringStatus::INTERVIEW:
                    if (Request()->time == '' && Request()->date == '') {
                        return response()->json(['status' => false, 'message' => translate('Time and date set')]);
                    }
                    $data = [
                        'time' => Request()->time,
                        'date' => Request()->date,
                        'name' => $applyJob?->user?->first_name . ' ' . $applyJob?->user?->last_name,
                        'company_name' => $applyJob?->company_name,
                        'job_title' => $applyJob?->job_title,
                    ];
                    $applyJob->update();
                    $this->emailFormat->interviewNotify($applyJob->user, $data);
                    $this->emailFormat->interview($applyJob, $data);
                    $message = translate('Interview has been Successfully finished');
                    break;

                case HiringStatus::HIRED:

                    if (Request()->time == '' && Request()->date == '') {
                        return response()->json(['status' => false, 'message' => translate('Time and date set')]);
                    }
                    $data = [
                        'time' => Request()->time,
                        'date' => Request()->date,
                        'name' => $applyJob?->user?->first_name . ' ' . $applyJob?->user?->last_name,
                        'company_name' => $applyJob?->company_name,
                        'job_title' => $applyJob?->job_title,
                    ];
                    $applyJob->update();
                    $this->emailFormat->joningNotify($applyJob->user, $data);
                    $this->emailFormat->joningLetter($applyJob, $data);
                    $message = translate('Hired has been Successfully');
                    break;
                case HiringStatus::REJECTED:

                    $applyJob->update();
                    $message = translate('Rejected has been Successfully finished');
                    $data = [
                        'name' => $applyJob?->user?->first_name . ' ' . $applyJob?->user?->last_name,
                        'company_name' => $applyJob?->company_name,
                        'job_title' => $applyJob?->job_title,
                    ];

                    $this->emailFormat->jobRejectNotify($applyJob->user, $data);
                    break;
            }

            toastr()->success('', "$message", ['positionClass' => 'toast-top-right']);

            return response()->json(['status' => true]);
        } catch (\Throwable $th) {
            //throw $th;

            return response()->json(['status' => 'error', 'message' => $th->getMessage()]);
        }
    }

    /**
     * cvshow
     *
     * @param  int  $id
     * @return view
     */
    public function cvshow($id)
    {

        $candidate = Candidate::with('user', 'profession', 'candidateSkills')->where('id', $id)->first();
        $applyId = Request()->apply_id;
        $applyStatus = Request()->apply_status;
        $cvcontent = view('front.pages.company-dashboard.cv', compact('candidate', 'applyId', 'applyStatus'))->render();

        return response()->json(['status' => true, 'content' => $cvcontent]);
    }

    /**
     * ====areaDestroy======
     *
     * @param  int  $id
     * @return message;
     */
    public function areaDestroy($id)
    {
        $status = $this->companyUser->areaDelete($id);

        if ($status['status'] == true) {
            return response()->json(['status' => true, 'message' => translate('Delete Successfully')]);
        }

        return response()->json(['status' => false, 'message' => $status['message']]);
    }

    /**
     * ================ stateByCountry ============
     *
     * @return Response
     */
    public function stateByCountry($id)
    {

        $result = $this->city->stateByCountryId($id);
        if ($result['status'] == true) {
            return response()->json(['status' => true, 'states' => $result['states']]);
        }

        return response()->json(['status' => false, 'message' => $result['message']]);
    }

    /**
     *=========== cityByState ============
     *
     * @return Response
     */
    public function cityByState($id)
    {

        $result = $this->city->cityByStateId($id);

        if ($result['status'] == true) {
            return response()->json(['status' => true, 'cities' => $result['cities']]);
        }

        return response()->json(['status' => false, 'message' => $result['message']]);
    }

    /**
     * downloadCv
     *
     * @param  int  $id  candidate_id
     * @return Response
     */
    public function downloadCv($id)
    {

        try {
            return $this->candidate->resumePdfDownload($id);
        } catch (\Throwable $th) {
            //throw $th;

            return $th->getMessage();
        }
    }

    /**
     * applicantStatus
     *
     * @param  int  $id
     */
    public function applicantStatus($id)
    {

        switch ($id) {
            case HiringStatus::SHORTLIST:

                $status = 'ShortList';
                break;

            case HiringStatus::INTERVIEW:

                $status = 'Interviwed';
                break;

            case HiringStatus::HIRED:

                $status = 'Hired';

                break;

            case HiringStatus::REJECTED:
                $status = 'Rejected';
                break;

            default:
                $status = 'Process';
        }

        return $status;
    }

    /**
     * qaualification
     *
     * @param  mixed  $name
     * @return Response
     */
    public function qaualification($name)
    {

        $qaualifications = EducationLavel::where('status', 1)->where('name', 'like', '%' . $name . '%')->get();

        return response()->json($qaualifications);
    }

    /**
     * accountStatus
     *
     * @param  int  $id
     */
    public function accountStatus($id)
    {
        $company = $this->companyUser->statusChange($id);
        if ($company['status'] == true) {
            return ['status' => true, 'message' => translate('Account Status Change Successfully')];
        }

        return $company;
    }

    /**
     * notifications
     */
    public function notifications(Request $request)
    {

        $notifications = $request?->user()?->company->notifications()->latest()->paginate(10);

        return view('front.pages.company-dashboard.notification', compact('notifications'));
    }

    /**
     * readAllNotificaiton
     */
    public function readAllNotificaiton()
    {
        auth()->user()?->company?->unreadNotifications->markAsRead();
        toastr()->success(translate('Notification Read All Successfully'));

        return redirect()->back();
    }

    /**
     * notificationReadUnreadById
     *
     * @param  int  $id
     */
    public function notificationReadUnreadById($id, Request $request)
    {

        $notification = $request->user()?->company?->notifications()->find($id);
        $notification->read_at = Carbon::now();
        $notification->update();
        if ($request->ajax()) {
            return response()->json(['status' => true, 'message' => 'succees']);
        }
        toastr()->success(translate('Notification Read Successfully'));

        return redirect()->route('company.all.notification');
    }

    /**
     * notificationDelete
     *
     * @param  int  $id
     */
    public function notificationDelete($id, Request $request)
    {
        $request->user()->company?->notifications()->where('id', $id)->delete();
        toastr()->success(translate('Notification Delete Successfully'));
        return redirect()->back();
    }
}
