{"__meta": {"id": "01JY1B1WDZJPXES5HEZ1QGZCKJ", "datetime": "2025-06-18 10:58:43", "utime": **********.775671, "method": "GET", "uri": "/company/profile", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.301085, "end": **********.775687, "duration": 0.47460198402404785, "duration_str": "475ms", "measures": [{"label": "Booting", "start": **********.301085, "relative_start": 0, "end": **********.536019, "relative_end": **********.536019, "duration": 0.*****************, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.536029, "relative_start": 0.*****************, "end": **********.775689, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "240ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.545646, "relative_start": 0.*****************, "end": **********.551049, "relative_end": **********.551049, "duration": 0.005403041839599609, "duration_str": "5.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.609191, "relative_start": 0.****************, "end": **********.773659, "relative_end": **********.773659, "duration": 0.*****************, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 17, "nb_templates": 17, "templates": [{"name": "1x front.pages.company-dashboard.profile", "param_count": null, "params": [], "start": **********.61173, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.phpfront.pages.company-dashboard.profile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fcompany-dashboard%2Fprofile.blade.php&line=1", "ajax": false, "filename": "profile.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.company-dashboard.profile"}, {"name": "1x js.admin.company", "param_count": null, "params": [], "start": **********.728172, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/admin/company.blade.phpjs.admin.company", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Fadmin%2Fcompany.blade.php&line=1", "ajax": false, "filename": "company.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.admin.company"}, {"name": "1x front.pages.company-dashboard.main", "param_count": null, "params": [], "start": **********.728898, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/main.blade.phpfront.pages.company-dashboard.main", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fcompany-dashboard%2Fmain.blade.php&line=1", "ajax": false, "filename": "main.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.company-dashboard.main"}, {"name": "1x front.layouts.master", "param_count": null, "params": [], "start": **********.734952, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/master.blade.phpfront.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.master"}, {"name": "1x front.layouts.includes.head", "param_count": null, "params": [], "start": **********.735287, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/head.blade.phpfront.layouts.includes.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.head"}, {"name": "1x css.theme-custom-css", "param_count": null, "params": [], "start": **********.736643, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/css/theme-custom-css.blade.phpcss.theme-custom-css", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fcss%2Ftheme-custom-css.blade.php&line=1", "ajax": false, "filename": "theme-custom-css.blade.php", "line": "?"}, "render_count": 1, "name_original": "css.theme-custom-css"}, {"name": "1x front.layouts.includes.header", "param_count": null, "params": [], "start": **********.737182, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/header.blade.phpfront.layouts.includes.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.header"}, {"name": "1x front.layouts.includes.top", "param_count": null, "params": [], "start": **********.737792, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/top.blade.phpfront.layouts.includes.top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.top"}, {"name": "1x front.layouts.includes.logo", "param_count": null, "params": [], "start": **********.741038, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/logo.blade.phpfront.layouts.includes.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.logo"}, {"name": "1x front.layouts.includes.menu", "param_count": null, "params": [], "start": **********.741516, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/menu.blade.phpfront.layouts.includes.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.menu"}, {"name": "1x front.layouts.includes.mobile", "param_count": null, "params": [], "start": **********.744809, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/mobile.blade.phpfront.layouts.includes.mobile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.mobile"}, {"name": "1x front.layouts.includes.right-menu", "param_count": null, "params": [], "start": **********.747513, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.phpfront.layouts.includes.right-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=1", "ajax": false, "filename": "right-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.right-menu"}, {"name": "1x front.layouts.includes.company-menu", "param_count": null, "params": [], "start": **********.755961, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/company-menu.blade.phpfront.layouts.includes.company-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fcompany-menu.blade.php&line=1", "ajax": false, "filename": "company-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.company-menu"}, {"name": "1x front.layouts.includes.footer", "param_count": null, "params": [], "start": **********.760268, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/footer.blade.phpfront.layouts.includes.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.footer"}, {"name": "1x front.layouts.includes.script", "param_count": null, "params": [], "start": **********.762465, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/script.blade.phpfront.layouts.includes.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.script"}, {"name": "1x js.translate", "param_count": null, "params": [], "start": **********.762863, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/translate.blade.phpjs.translate", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftranslate.blade.php&line=1", "ajax": false, "filename": "translate.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.translate"}, {"name": "1x js.theme-custom-js", "param_count": null, "params": [], "start": **********.772759, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/theme-custom-js.blade.phpjs.theme-custom-js", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftheme-custom-js.blade.php&line=1", "ajax": false, "filename": "theme-custom-js.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.theme-custom-js"}]}, "route": {"uri": "GET company/profile", "middleware": "web, role:company|admin, auth, prevent-back-history, is_verify_email", "controller": "App\\Http\\Controllers\\Company\\CompanyDashboardController@companyProfile<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FCompany%2FCompanyDashboardController.php&line=61\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "company.profile", "prefix": "/company", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FCompany%2FCompanyDashboardController.php&line=61\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Company/CompanyDashboardController.php:61-66</a>"}, "queries": {"count": 43, "nb_statements": 43, "nb_visible_statements": 43, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.024000000000000014, "accumulated_duration_str": "24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 216 limit 1", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.5768, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 1.958}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (216) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5845811, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 1.958, "width_percent": 2.75}, {"sql": "select `companies`.*, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id` and `job_deadline` >= '2025-06-18 10:58:43' and `status` = 1) as `pending_jobs_count`, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id`) as `jobs_count`, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id` and `job_deadline` >= '2025-06-18 10:58:43' and `status` = 1) as `pending_jobs_count`, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id` and `job_deadline` >= '2025-06-18 10:58:43' and `status` = 1) as `active_jobs_count`, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id` and `status` = 0) as `declined_jobs_count` from `companies` where `user_id` = 216 or `id` = 216 limit 1", "type": "query", "params": [], "bindings": ["2025-06-18 10:58:43", 1, "2025-06-18 10:58:43", 1, "2025-06-18 10:58:43", 1, 0, 216, 216], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.588775, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 17, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 4.708, "width_percent": 5.333}, {"sql": "select * from `users` where `users`.`id` in (216)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.592107, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 10.042, "width_percent": 2.125}, {"sql": "select * from `user_login_logs` where `user_login_logs`.`user_id` in (216)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5939, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 27, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 12.167, "width_percent": 2.333}, {"sql": "select * from `work_areas` where `work_areas`.`company_id` in (47)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5960321, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 14.5, "width_percent": 2.708}, {"sql": "select `jobs`.*, (select count(*) from `apply_jobs` where `jobs`.`id` = `apply_jobs`.`job_id`) as `applieduser_count` from `jobs` where `jobs`.`company_id` in (47)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.597883, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 17.208, "width_percent": 2.875}, {"sql": "select * from `categories` where `categories`.`id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.599518, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 27, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 20.083, "width_percent": 1.958}, {"sql": "select `job_types`.*, `job_job_type`.`job_id` as `pivot_job_id`, `job_job_type`.`job_type_id` as `pivot_job_type_id` from `job_types` inner join `job_job_type` on `job_types`.`id` = `job_job_type`.`job_type_id` where `job_job_type`.`job_id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.601345, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 26, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 22.042, "width_percent": 2.125}, {"sql": "select subscriptions.company_id,SUM(subscriptions.total_price) as total from `subscriptions` where `payment_status` = 'Paid' and `subscriptions`.`company_id` in (47) group by `subscriptions`.`company_id`", "type": "query", "params": [], "bindings": ["Paid"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6034489, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 24.167, "width_percent": 2.333}, {"sql": "select * from `subscriptions` where `subscriptions`.`company_id` in (47)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.604944, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 26.5, "width_percent": 2.125}, {"sql": "select * from `user_login_logs` where `user_login_logs`.`user_id` in (47)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6063302, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 28.625, "width_percent": 1.917}, {"sql": "select * from `company_types` where `company_types`.`id` in (9)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Company/CompanyDashboardController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Company\\CompanyDashboardController.php", "line": 63}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.607675, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:131", "source": {"index": 22, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 131}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=131", "ajax": false, "filename": "CompanyRepository.php", "line": "131"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 30.542, "width_percent": 1.958}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 47 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 91}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.626142, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 32.5, "width_percent": 1.917}, {"sql": "select * from `company_types` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1051}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.631186, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1051", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1051}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1051", "ajax": false, "filename": "Helper.php", "line": "1051"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 34.417, "width_percent": 2.083}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 1 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.633115, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 36.5, "width_percent": 2.25}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 2 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.634737, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 38.75, "width_percent": 2.167}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 3 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.636225, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 40.917, "width_percent": 2.125}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 4 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6377041, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 43.042, "width_percent": 2.083}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 5 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.63916, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 45.125, "width_percent": 2.083}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 6 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.640613, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 47.208, "width_percent": 2.083}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 7 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.642067, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 49.292, "width_percent": 2.125}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 8 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6435351, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 51.417, "width_percent": 2.083}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 9 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6456199, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 53.5, "width_percent": 3.083}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 10 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.647469, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 56.583, "width_percent": 1.833}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 11 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.648948, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 58.417, "width_percent": 2}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 12 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6509042, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 60.417, "width_percent": 1.875}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 13 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.652713, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 62.292, "width_percent": 2.125}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 14 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.654182, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 64.417, "width_percent": 2}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 15 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.655612, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.417, "width_percent": 1.958}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 16 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.65703, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 68.375, "width_percent": 2}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 17 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6584752, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 70.375, "width_percent": 2.042}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 18 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6599162, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 72.417, "width_percent": 2.042}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 19 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.661951, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 74.458, "width_percent": 3.5}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 20 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6639469, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.958, "width_percent": 2.333}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 29 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": "view", "name": "front.pages.company-dashboard.profile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/company-dashboard/profile.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.665479, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.292, "width_percent": 2}, {"sql": "select * from `countries`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 383}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.670424, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Helper.php:383", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 383}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=383", "ajax": false, "filename": "Helper.php", "line": "383"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 82.292, "width_percent": 5.917}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.73949, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.top:19", "source": {"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=19", "ajax": false, "filename": "top.blade.php", "line": "19"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 88.208, "width_percent": 1.625}, {"sql": "select * from `menu_items` where (`menu_id` = 2 and `parent_id` is null) order by `order` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.7419329, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 89.833, "width_percent": 1.792}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (215, 216, 231, 232, 233, 235) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.74335, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 91.625, "width_percent": 1.292}, {"sql": "select * from `companies` where `companies`.`user_id` = 216 and `companies`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.749096, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.right-menu:2", "source": {"index": 21, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 2}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=2", "ajax": false, "filename": "right-menu.blade.php", "line": "2"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 92.917, "width_percent": 2.375}, {"sql": "select * from `candidates` where `candidates`.`user_id` = 216 and `candidates`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.751439, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.right-menu:3", "source": {"index": 22, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 3}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=3", "ajax": false, "filename": "right-menu.blade.php", "line": "3"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 95.292, "width_percent": 1.917}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\Admin\\\\Company' and `notifications`.`notifiable_id` = 47 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\Admin\\Company", 47], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 7}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.752702, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.right-menu:7", "source": {"index": 16, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=7", "ajax": false, "filename": "right-menu.blade.php", "line": "7"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 97.208, "width_percent": 2.792}]}, "models": {"data": {"App\\Models\\Admin\\CompanyTypeTranslation": {"value": 82, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyTypeTranslation.php&line=1", "ajax": false, "filename": "CompanyTypeTranslation.php", "line": "?"}}, "App\\Models\\Admin\\CompanyType": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=1", "ajax": false, "filename": "CompanyType.php", "line": "?"}}, "App\\Models\\Admin\\MenuItem": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin\\Country": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\Company": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Front\\WorkArea": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FFront%2FWorkArea.php&line=1", "ajax": false, "filename": "WorkArea.php", "line": "?"}}, "App\\Models\\Admin\\Job": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Admin\\Category": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}}, "count": 127, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/lang/flags/in.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "216"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/company/profile", "action_name": "company.profile", "controller_action": "App\\Http\\Controllers\\Company\\CompanyDashboardController@companyProfile", "uri": "GET company/profile", "controller": "App\\Http\\Controllers\\Company\\CompanyDashboardController@companyProfile<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FCompany%2FCompanyDashboardController.php&line=61\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/company", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FCompany%2FCompanyDashboardController.php&line=61\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Company/CompanyDashboardController.php:61-66</a>", "middleware": "web, role:company|admin, auth, prevent-back-history, is_verify_email", "duration": "481ms", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2018420185 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2018420185\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1457614083 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1457614083\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1305569010 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/company/all-job</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6IlNJN1gzYzhqWjRQdEtNR3ZxeTVIc3c9PSIsInZhbHVlIjoia3o4bVBYbHp1SElBeXBCYmRLV3BvSGF6TElDaUNmUmlmRGg3b3MvdFBQck4raFdPOGFkZ3ZKL1plUHQ5S0gxLzJsSnF6VHczMzJUQlFENGNaNUkycUt0eDhSUDI5YkltaVJTck9hbE1GOWRxeUl6OGlsY0VtKzVTV0FiajNoYUMiLCJtYWMiOiI1YmI0ZDUwNzM4NTQ4MzZlYWNkYTVmNDc0MTNjZDgxZjM2ZDA3ZmRlYjE1OWMwODdmMmE5Y2ViZWU0NWIxMTRjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImRRR1dZTUNxUmdzejIyV3JpdklwSlE9PSIsInZhbHVlIjoiVzcwUmlyWWYzZFhCNC9jV2hlR0RSQ2tmNmV5RUJGaDBrdTVaRkpyUnV3bHVsYWl6ZWZ6MGZCRXg1b204ZG52c3FsNEhMYU9LeVhKdTdkSDR4dWQ0M3dtWlRvSVBwd05yWi9xWGhLZmo3YkFJdW0ySFp4ek5OREs5Ykd3OVNaTTkiLCJtYWMiOiI1NDQ1ZDcyZGUzYzg1YTc2MzUwMDBiZTAwMmE5YzA3MDIyN2RhNmNlZmYwMzU5NWM4ZTMwMDc3NDdjNWFiYmE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305569010\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-855215917 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tQf33pMyNyWOY8sdEZw5gl6WCpMho5WMM5teqzOL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855215917\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-410297075 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 10:58:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410297075\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1591410704 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/storage/lang/flags/in.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>216</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591410704\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/company/profile", "action_name": "company.profile", "controller_action": "App\\Http\\Controllers\\Company\\CompanyDashboardController@companyProfile"}, "badge": null}}