<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class AdminRoleController extends Controller
{
    /**
     * index
     *
     * @return view
     */
    public function index()
    {
        $data = Admin::with('roles')->orderBy('id', 'DESC')->get();
        return view('admin.pages.user.index', compact('data'));
    }

    /**
     * create
     *
     * @return view
     */
    public function create()
    {
        $role = Role::where('guard_name', 'admin')->get();

        return view('admin.pages.user.create', compact('role'));
    }

    /**
     * store
     *
     * @param  mixed  $request
     * @return Response
     */
    public function store(Request $request)

    {


        if (!$this->hasPermissions(['user.add'])) {
            toastr()->error(translate('You have no Permission'), 'warning');
            return redirect()->back();
        }
        $request->validate([
            'fname' => 'required',
            'lname' => 'required',
            'roles' => 'required',
            'email' => 'required|email|unique:admins',
            'confirm_password' => 'required|min:5|max:12',
            'password' => 'required|min:5|max:12|same:confirm_password',
        ]);

        $admin = Admin::create([
            'first_name' => $request->fname,
            'last_name'  => $request->lname,
            'email'      => $request->email,
            'password'   => Hash::make($request->password),
        ]);

        if ($admin && !empty($request->roles)) {
            $admin->assignRole((array) $request->roles);
        }


        toastr()->success(translate('User save successfully'));
        return redirect()->route('admin.list');
    }

    public function edit($id)
    {
        $role = Role::all();
        $edit = Admin::where('id', $id)->first();
        return view('admin.pages.user.edit', compact('role', 'edit'));
    }

    /**
     * update
     *
     * @param  mixed  $request
     */
    public function update(Request $request)
    {
        if (!$this->hasPermissions(['user.edit'])) {
            toastr()->error(translate('You have no Permission'), 'warning');
            return redirect()->back();
        }
        $this->validate($request, [
            'fname' => 'required',
            'lname' => 'required',
            'email' => 'required|email|unique:admins,email,' . $request->id,
            'roles' => 'required',
        ]);

        if ($request->password) {
            $this->validate($request, [
                'password' => 'required|min:5|max:12|same:confirm_password',
            ]);
        }

        $admin = Admin::find($request->id);
        if ($admin) {
            $admin->update([
                'first_name' => $request->fname,
                'last_name'  => $request->lname,
                'email'      => $request->email,
                'password'   => $request->password ? Hash::make($request->password): $admin->password,
            ]);
            $admin->roles()->sync($request->roles ?? []);
        }
        toastr()->success(translate('User updated successfully'));
        return redirect()->route('admin.list');
    }

    /**
     * delete
     *
     * @param  int  $id
     */
    public function delete($id, Request $request)
    {

        if (!$this->hasPermissions(['user.delete'])) {
            toastr()->error(translate('You do not have permission to delete users.'));
            return redirect()->back();
        }
        // Attempt to delete the user
        if (Admin::where('id', $id)->delete()) {
            toastr()->success(translate('User deleted successfully.'));
        } else {
            toastr()->error(translate('User could not be deleted.'));
        }

        return redirect()->back();
    }
}
