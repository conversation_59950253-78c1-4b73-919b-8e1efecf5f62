{"__meta": {"id": "01JXYP65MBBRP8XW9TR0MAKA7W", "datetime": "2025-06-17 10:15:35", "utime": **********.308128, "method": "GET", "uri": "/.well-known/appspecific/com.chrome.devtools.json", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750155334.607825, "end": **********.308143, "duration": 0.7003178596496582, "duration_str": "700ms", "measures": [{"label": "Booting", "start": 1750155334.607825, "relative_start": 0, "end": **********.177117, "relative_end": **********.177117, "duration": 0.****************, "duration_str": "569ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.177139, "relative_start": 0.****************, "end": **********.308145, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.206503, "relative_start": 0.****************, "end": **********.2186, "relative_end": **********.2186, "duration": 0.*****************, "duration_str": "12.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.252492, "relative_start": 0.****************, "end": **********.305885, "relative_end": **********.305885, "duration": 0.*****************, "duration_str": "53.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 14, "nb_templates": 14, "templates": [{"name": "1x front.pages.404", "param_count": null, "params": [], "start": **********.254809, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/404.blade.phpfront.pages.404", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2F404.blade.php&line=1", "ajax": false, "filename": "404.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.404"}, {"name": "1x front.layouts.master", "param_count": null, "params": [], "start": **********.259866, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/master.blade.phpfront.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.master"}, {"name": "1x front.layouts.includes.head", "param_count": null, "params": [], "start": **********.260311, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/head.blade.phpfront.layouts.includes.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.head"}, {"name": "1x css.theme-custom-css", "param_count": null, "params": [], "start": **********.261055, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/css/theme-custom-css.blade.phpcss.theme-custom-css", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fcss%2Ftheme-custom-css.blade.php&line=1", "ajax": false, "filename": "theme-custom-css.blade.php", "line": "?"}, "render_count": 1, "name_original": "css.theme-custom-css"}, {"name": "1x front.layouts.includes.header", "param_count": null, "params": [], "start": **********.26233, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/header.blade.phpfront.layouts.includes.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.header"}, {"name": "1x front.layouts.includes.top", "param_count": null, "params": [], "start": **********.273108, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/top.blade.phpfront.layouts.includes.top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.top"}, {"name": "1x front.layouts.includes.logo", "param_count": null, "params": [], "start": **********.27764, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/logo.blade.phpfront.layouts.includes.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.logo"}, {"name": "1x front.layouts.includes.menu", "param_count": null, "params": [], "start": **********.278571, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/menu.blade.phpfront.layouts.includes.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.menu"}, {"name": "1x front.layouts.includes.mobile", "param_count": null, "params": [], "start": **********.282682, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/mobile.blade.phpfront.layouts.includes.mobile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.mobile"}, {"name": "1x front.layouts.includes.right-menu", "param_count": null, "params": [], "start": **********.28869, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.phpfront.layouts.includes.right-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=1", "ajax": false, "filename": "right-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.right-menu"}, {"name": "1x front.layouts.includes.footer", "param_count": null, "params": [], "start": **********.291056, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/footer.blade.phpfront.layouts.includes.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.footer"}, {"name": "1x front.layouts.includes.script", "param_count": null, "params": [], "start": **********.293361, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/script.blade.phpfront.layouts.includes.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.script"}, {"name": "1x js.translate", "param_count": null, "params": [], "start": **********.293961, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/translate.blade.phpjs.translate", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftranslate.blade.php&line=1", "ajax": false, "filename": "translate.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.translate"}, {"name": "1x js.theme-custom-js", "param_count": null, "params": [], "start": **********.304784, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/theme-custom-js.blade.phpjs.theme-custom-js", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftheme-custom-js.blade.php&line=1", "ajax": false, "filename": "theme-custom-js.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.theme-custom-js"}]}, "route": {"uri": "GET {any}", "middleware": "web", "controller": "App\\Http\\Controllers\\Frontend\\PagesController@customPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PagesController.php:15-70</a>"}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00151, "accumulated_duration_str": "1.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `pages` where `page_route` = '.well-known/appspecific/com.chrome.devtools.json' limit 1", "type": "query", "params": [], "bindings": [".well-known/appspecific/com.chrome.devtools.json"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2497919, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:27", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=27", "ajax": false, "filename": "PagesController.php", "line": "27"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 28.477}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.275124, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.top:19", "source": {"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=19", "ajax": false, "filename": "top.blade.php", "line": "19"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 28.477, "width_percent": 21.854}, {"sql": "select * from `menu_items` where (`menu_id` = 2 and `parent_id` is null) order by `order` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.279747, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 50.331, "width_percent": 33.113}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (215, 216, 231, 232, 233, 235) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.2813468, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 83.444, "width_percent": 16.556}]}, "models": {"data": {"App\\Models\\Admin\\MenuItem": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/frontendassets/images/icon/apply-ellipse.svg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/theme-option\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/.well-known/appspecific/com.chrome.devtools.json", "action_name": null, "controller_action": "App\\Http\\Controllers\\Frontend\\PagesController@customPage", "uri": "GET {any}", "controller": "App\\Http\\Controllers\\Frontend\\PagesController@customPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PagesController.php:15-70</a>", "middleware": "web", "duration": "703ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-896001773 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-896001773\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-872297528 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-872297528\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2125130674 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6InQxU1VqQ3U1N0NyaXJpRWh5U2JQTXc9PSIsInZhbHVlIjoiZ0dZZGtPd1hpYzJWMGxqM3NySCs0dWNBT2E2bEdKekZZeVFOSG1oZ2t5VlZYUHZoUXdoYm82V2JxNlRnaXI0bG1MSVVnTHFWTVZsNmJIaDBlWHpjT1hmYVBLS0ljYm5LR0VCSDZGR3pUV2xpTnlvemF4aE83dFdhbGhqK2ExNmEiLCJtYWMiOiI0MmQ0YjQwYjkxZmFlODQ5OTUzMjRiZmM0ZThhMzIxNmQzNjAxMzE1MWU2ODJmNTgwYWMyODIzODYzNGYzYzYxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImhzS0JZeGI1OEdQSi9rMFAzb3k2OHc9PSIsInZhbHVlIjoiMHVDNHhDdmFON3NiT2t2RDZvRUhhMXdIb2l4ejVJYVZybUV0dGlOOG9VR3EwaGIvY0J2UitBS2tuTGxHMjdMVC94NTdLWTNPeUpObmFKcXVXZEJsMUw5NGtEbGlNWmxvWitYWU9UMlBoOWluakc4QjcwV3dscHlZZHRQcWR6V3giLCJtYWMiOiIwNDc0N2E5YzE0MmU0MzdmNzlkN2I1MDFiYzdlMGVlN2ZmMGFkNTAwMmI4ZjJhZTI1YjcyZjNhMTEyNWZhOTUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125130674\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-590003468 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DuRe3UYDshvVdLe1lxmS8WfvwPktcikZxAETD6MP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590003468\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1738606723 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 10:15:35 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1738606723\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2112136916 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">http://127.0.0.1:8000/frontendassets/images/icon/apply-ellipse.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/theme-option</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112136916\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/.well-known/appspecific/com.chrome.devtools.json", "controller_action": "App\\Http\\Controllers\\Frontend\\PagesController@customPage"}, "badge": null}}