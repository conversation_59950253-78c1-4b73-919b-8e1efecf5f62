<div class="sidebar-wrapper">
    <div class="sidebar">
        <div class="sidebar-menu-wrapper pb-110">
            <ul class="side-menu-list gap-3">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('dashoard.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/dashboard') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.dashboard')); ?>">
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M14.0449 0.140627C13.8574 0.216799 13.6055 0.351564 13.4883 0.433596C13.3652 0.515627 10.4062 3.45117 6.90233 6.95508C1.76952 12.0996 0.509752 13.3887 0.386705 13.6348C-0.175796 14.7715 0.0117046 16.0137 0.873033 16.8809C1.4121 17.4141 2.09764 17.6953 2.8828 17.6953H3.28124V22.3828C3.28124 25.5527 3.30467 27.1641 3.34569 27.375C3.56835 28.4414 4.42967 29.4082 5.49608 29.7773C5.8828 29.9121 5.9121 29.9121 8.74218 29.9121C11.5312 29.9121 11.5957 29.9121 11.7539 29.7891C11.8418 29.7246 11.9707 29.5957 12.0351 29.5078C12.1582 29.3496 12.1582 29.2734 12.1875 25.4355L12.2168 21.5215L12.3926 21.2344C12.6152 20.877 13.0664 20.5898 13.5 20.5371C13.6641 20.5137 14.4844 20.5078 15.3223 20.5195L16.8457 20.5371L17.1562 20.707C17.4492 20.8711 17.6367 21.0645 17.8183 21.3867C17.8828 21.5098 17.9062 22.2305 17.9297 25.4414C17.959 29.2734 17.959 29.3496 18.082 29.5078C18.1465 29.5957 18.2754 29.7246 18.3633 29.7891C18.5215 29.9121 18.5859 29.9121 21.375 29.9121C24.2051 29.9121 24.2344 29.9121 24.6211 29.7773C25.6875 29.4082 26.5488 28.4414 26.7715 27.375C26.8125 27.1641 26.8359 25.5527 26.8359 22.3828V17.6953H27.2344C28.3359 17.6953 29.2969 17.1094 29.7715 16.1426C29.9707 15.7383 29.9707 15.7266 29.9707 14.9414C29.9707 13.6055 30.6445 14.3965 23.1211 6.87891C15.6445 -0.603514 16.3945 0.0410175 15.1172 0.0117207C14.4551 1.90735e-06 14.3555 0.0117207 14.0449 0.140627ZM15.4922 1.81641C15.8379 1.98047 28.084 14.2441 28.2129 14.543C28.3359 14.8418 28.3242 15.0938 28.1777 15.3867C27.9609 15.8379 27.7793 15.9023 26.6484 15.9375C25.5879 15.9668 25.5117 15.9902 25.2305 16.3711C25.1074 16.5293 25.1074 16.5938 25.0781 21.8438L25.0488 27.1582L24.8848 27.4336C24.7969 27.5859 24.6387 27.7793 24.5332 27.8555C24.123 28.1719 24 28.1836 21.7617 28.1836H19.6875V24.9023C19.6875 22.752 19.6641 21.5215 19.623 21.3164C19.3945 20.2148 18.5098 19.2656 17.373 18.9082C16.9863 18.7852 16.8574 18.7793 15.0586 18.7793C12.9609 18.7793 12.8555 18.791 12.1055 19.1719C11.3613 19.5469 10.6699 20.4727 10.4941 21.3164C10.4531 21.5215 10.4297 22.752 10.4297 24.9023V28.1836H8.35546C6.11718 28.1836 5.99413 28.1719 5.58397 27.8555C5.4785 27.7793 5.3203 27.5859 5.23241 27.4336L5.06835 27.1582L5.03905 21.8438C5.00975 16.5938 5.00975 16.5293 4.88671 16.3711C4.60546 15.9902 4.52928 15.9668 3.46874 15.9375C2.33788 15.9023 2.15624 15.8379 1.93944 15.3867C1.79296 15.0938 1.78124 14.8418 1.90428 14.543C2.02733 14.25 14.2793 1.98633 14.6191 1.81641C14.918 1.66992 15.1933 1.66992 15.4922 1.81641Z" />
                            </svg>
                            <h6><?php echo e(translate('Dashboard')); ?></h6>

                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subscription.menu')): ?>
                    <li>
                        <a href="<?php echo e(url('/')); ?>" target="__blank">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-globe" viewBox="0 0 16 16">
                                <path
                                    d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm7.5-6.923c-.67.204-1.335.82-1.887 1.855A7.97 7.97 0 0 0 5.145 4H7.5V1.077zM4.09 4a9.267 9.267 0 0 1 .64-1.539 6.7 6.7 0 0 1 .597-.933A7.025 7.025 0 0 0 2.255 4H4.09zm-.582 3.5c.03-.877.138-1.718.312-2.5H1.674a6.958 6.958 0 0 0-.656 2.5h2.49zM4.847 5a12.5 12.5 0 0 0-.338 2.5H7.5V5H4.847zM8.5 5v2.5h2.99a12.495 12.495 0 0 0-.337-2.5H8.5zM4.51 8.5a12.5 12.5 0 0 0 .337 2.5H7.5V8.5H4.51zm3.99 0V11h2.653c.187-.765.306-1.608.338-2.5H8.5zM5.145 12c.138.386.295.744.468 1.068.552 1.035 1.218 1.65 1.887 1.855V12H5.145zm.182 2.472a6.696 6.696 0 0 1-.597-.933A9.268 9.268 0 0 1 4.09 12H2.255a7.024 7.024 0 0 0 3.072 2.472zM3.82 11a13.652 13.652 0 0 1-.312-2.5h-2.49c.062.89.291 1.733.656 2.5H3.82zm6.853 3.472A7.024 7.024 0 0 0 13.745 12H11.91a9.27 9.27 0 0 1-.64 1.539 6.688 6.688 0 0 1-.597.933zM8.5 12v2.923c.67-.204 1.335-.82 1.887-1.855.173-.324.33-.682.468-1.068H8.5zm3.68-1h2.146c.365-.767.594-1.61.656-2.5h-2.49a13.65 13.65 0 0 1-.312 2.5zm2.802-3.5a6.959 6.959 0 0 0-.656-2.5H12.18c.174.782.282 1.623.312 2.5h2.49zM11.27 2.461c.247.464.462.98.64 1.539h1.835a7.024 7.024 0 0 0-3.072-2.472c.218.284.418.598.597.933zM10.855 4a7.966 7.966 0 0 0-.468-1.068C9.835 1.897 9.17 1.282 8.5 1.077V4h2.355z" />
                            </svg>
                            <h6><?php echo e(translate('Visit Website')); ?></h6>

                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subscription.menu')): ?>
                    <span class="menu-panel-label"> <?php echo e(translate('MANAGE Subcription')); ?> </span>

                    <li
                        class="<?php echo e(request()->is('admin/subscription') || request()->is('admin/subscription/*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.subscription')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-credit-card" viewBox="0 0 16 16">
                                <path
                                    d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v1h14V4a1 1 0 0 0-1-1H2zm13 4H1v5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V7z" />
                                <path d="M2 10a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-1z" />
                            </svg>
                            <h6><?php echo e(translate('Subscription')); ?></h6>

                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('companytype.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/company-type') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.company.type')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-buildings" viewBox="0 0 16 16">
                                <path
                                    d="M14.763.075A.5.5 0 0 1 15 .5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V14h-1v1.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V10a.5.5 0 0 1 .342-.474L6 7.64V4.5a.5.5 0 0 1 .276-.447l8-4a.5.5 0 0 1 .487.022ZM6 8.694 1 10.36V15h5V8.694ZM7 15h2v-1.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 .5.5V15h2V1.309l-7 3.5V15Z" />
                                <path
                                    d="M2 11h1v1H2v-1Zm2 0h1v1H4v-1Zm-2 2h1v1H2v-1Zm2 0h1v1H4v-1Zm4-4h1v1H8V9Zm2 0h1v1h-1V9Zm-2 2h1v1H8v-1Zm2 0h1v1h-1v-1Zm2-2h1v1h-1V9Zm0 2h1v1h-1v-1ZM8 7h1v1H8V7Zm2 0h1v1h-1V7Zm2 0h1v1h-1V7ZM8 5h1v1H8V5Zm2 0h1v1h-1V5Zm2 0h1v1h-1V5Zm0-2h1v1h-1V3Z" />
                            </svg>
                            <h6><?php echo e(translate('Company Type')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('company.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/company') || request()->is('admin/company/*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.company')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 2048 2048"><path fill="#D1D1D1" d="M1920 0v2048H256v-254H128v-128h128v-257H128v-128h128V769H128V641h128V385H128V257h128V0zm-128 128H384v1792h1408zm-128 384h-640V384h640zm0 256h-640V640h640zm-960 892q-39 0-73-14t-60-40t-40-60t-15-74q0-39 14-73t40-59t60-41t74-15q39 0 73 15t59 40t41 60t15 73q0 39-15 73t-40 60t-60 40t-73 15m0-256q-29 0-48 19t-20 49q0 29 19 48t49 20q29 0 48-19t20-49q0-29-19-48t-49-20m0-640q-39 0-73-14t-60-40t-40-60t-15-74q0-39 14-73t40-59t60-41t74-15q39 0 73 15t59 40t41 60t15 73q0 39-15 73t-40 60t-60 40t-73 15m0-256q-29 0-48 19t-20 49q0 29 19 48t49 20q29 0 48-19t20-49q0-29-19-48t-49-20m960 900h-640v-128h640zm0 256h-640v-128h640z"/></svg>
                            <h6><?php echo e(translate('Company')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('candidate.menu')): ?>
                    <li
                        class="<?php echo e(request()->is('admin/candidate') || request()->is('admin/candidate/*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.candidate')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-people" viewBox="0 0 16 16">
                                <path
                                    d="M15 14s1 0 1-1-1-4-5-4-5 3-5 4 1 1 1 1h8Zm-7.978-1A.261.261 0 0 1 7 12.996c.001-.264.167-1.03.76-1.72C8.312 10.629 9.282 10 11 10c1.717 0 2.687.63 3.24 1.276.593.69.758 1.457.76 1.72l-.008.002a.274.274 0 0 1-.014.002H7.022ZM11 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm3-2a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM6.936 9.28a5.88 5.88 0 0 0-1.23-.247A7.35 7.35 0 0 0 5 9c-4 0-5 3-5 4 0 .667.333 1 1 1h4.216A2.238 2.238 0 0 1 5 13c0-1.01.377-2.042 1.09-2.904.243-.294.526-.569.846-.816ZM4.92 10A5.493 5.493 0 0 0 4 13H1c0-.26.164-1.03.76-1.724.545-.636 1.492-1.256 3.16-1.275ZM1.5 5.5a3 3 0 1 1 6 0 3 3 0 0 1-6 0Zm3-2a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z" />
                            </svg>
                            <h6><?php echo e(translate('Candidate')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>
                <span class="menu-panel-label"> <?php echo e(translate('MANAGE JOB')); ?> </span>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('jobcategory.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/job') || request()->is('admin/job/*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.job')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24"><g fill="none" stroke="#D1D1D1" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="#D1D1D1"><path d="M11.007 21.5H9.605c-3.585 0-5.377 0-6.491-1.135S2 17.403 2 13.75s0-5.48 1.114-6.615S6.02 6 9.605 6h3.803c3.585 0 5.378 0 6.492 1.135c.857.873 1.054 2.156 1.1 4.365V13"/><path d="M19 18.5h-3m0 3a3 3 0 1 1 0-6m3 6a3 3 0 1 0 0-6M16 6l-.1-.31c-.495-1.54-.742-2.31-1.331-2.75c-.59-.44-1.372-.44-2.938-.44h-.263c-1.565 0-2.348 0-2.937.44c-.59.44-.837 1.21-1.332 2.75L7 6"/></g></svg>
                            <h6><?php echo e(translate('Job')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('jobcategory.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/category') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.category')); ?>">

                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 48 48"><defs><mask id="ipSCategoryManagement0"><g fill="none"><rect width="36" height="14" x="6" y="28" stroke="#fff" stroke-width="4" rx="4"/><path stroke="#fff" stroke-linecap="round" stroke-width="4" d="M20 7H10a4 4 0 0 0-4 4v6a4 4 0 0 0 4 4h10"/><circle cx="34" cy="14" r="8" fill="#fff" stroke="#fff" stroke-width="4"/><circle cx="34" cy="14" r="3" fill="#000"/></g></mask></defs><path fill="#D1D1D1" d="M0 0h48v48H0z" mask="url(#ipSCategoryManagement0)"/></svg>
                            <h6><?php echo e(translate('Job Category')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('jobsubcategory.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/subcategory') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.subcategory')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 32 32"><path fill="#D1D1D1" d="M29 10h-5v2h5v6h-7v2h3v2.142a4 4 0 1 0 2 0V20h2a2.003 2.003 0 0 0 2-2v-6a2 2 0 0 0-2-2m-1 16a2 2 0 1 1-2-2a2.003 2.003 0 0 1 2 2M19 6h-5v2h5v6h-7v2h3v6.142a4 4 0 1 0 2 0V16h2a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2m-1 20a2 2 0 1 1-2-2a2.003 2.003 0 0 1 2 2M9 2H3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2v10.142a4 4 0 1 0 2 0V12h2a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2M8 26a2 2 0 1 1-2-2a2 2 0 0 1 2 2M3 10V4h6l.002 6z"/></svg>
                            <h6><?php echo e(translate('Job Subcategory')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('jobtype.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/job-type') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.job.type')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24"><g fill="none" stroke="#D1D1D1" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="#D1D1D1"><path d="M11.007 21H9.605c-3.585 0-5.377 0-6.491-1.135S2 16.903 2 13.25s0-5.48 1.114-6.615S6.02 5.5 9.605 5.5h3.803c3.585 0 5.378 0 6.492 1.135c.857.873 1.054 2.156 1.1 4.365"/><path d="M20.017 20.023L22 22m-.947-4.474a3.527 3.527 0 1 0-7.053 0a3.527 3.527 0 0 0 7.053 0M16 5.5l-.1-.31c-.495-1.54-.742-2.31-1.331-2.75C13.979 2 13.197 2 11.63 2h-.263c-1.565 0-2.348 0-2.937.44c-.59.44-.837 1.21-1.332 2.75L7 5.5"/></g></svg>
                            <h6><?php echo e(translate('Job Type')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('jobskill.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/job-skill') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.job.skill')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 32 32"><path fill="#D1D1D1" d="M30 30h-8V4h8zm-6-2h4V6h-4zm-4 2h-8V12h8zm-6-2h4V14h-4zm-4 2H2V18h8zm-6-2h4v-8H4z"/></svg>
                            <h6><?php echo e(translate('Job Skill')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('jobrole.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/job-role') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.job.role')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 32 32"><path fill="#D1D1D1" d="M19.307 3.21a2.91 2.91 0 1 0-.223 1.94a11.64 11.64 0 0 1 8.232 7.049l1.775-.698a13.58 13.58 0 0 0-9.784-8.291m-2.822 1.638a.97.97 0 1 1 0-1.939a.97.97 0 0 1 0 1.94m-4.267.805l-.717-1.774a13.58 13.58 0 0 0-8.291 9.784a2.91 2.91 0 1 0 1.94.223a11.64 11.64 0 0 1 7.068-8.233m-8.34 11.802a.97.97 0 1 1 0-1.94a.97.97 0 0 1 0 1.94m12.607 8.727a2.91 2.91 0 0 0-2.599 1.62a11.64 11.64 0 0 1-8.233-7.05l-1.774.717a13.58 13.58 0 0 0 9.813 8.291a2.91 2.91 0 1 0 2.793-3.578m0 3.879a.97.97 0 1 1 0-1.94a.97.97 0 0 1 0 1.94M32 16.485a2.91 2.91 0 1 0-4.199 2.599a11.64 11.64 0 0 1-7.05 8.232l.718 1.775a13.58 13.58 0 0 0 8.291-9.813A2.91 2.91 0 0 0 32 16.485m-2.91.97a.97.97 0 1 1 0-1.94a.97.97 0 0 1 0 1.94"/><path fill="#D1D1D1" d="M19.19 16.35a3.879 3.879 0 1 0-5.42 0a4.85 4.85 0 0 0-2.134 4.014v1.939h9.697v-1.94a4.85 4.85 0 0 0-2.143-4.014m-4.645-2.774a1.94 1.94 0 1 1 3.88 0a1.94 1.94 0 0 1-3.88 0m-.97 6.788a2.91 2.91 0 1 1 5.819 0z" class="ouiIcon__fillSecondary"/></svg>
                            <h6><?php echo e(translate('Job Role')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('jobexperience.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/job-experience') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.job.experience')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24"><g fill="none" stroke="#D1D1D1" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="#D1D1D1"><path d="M11.007 21H9.605c-3.585 0-5.377 0-6.491-1.135S2 16.903 2 13.25s0-5.48 1.114-6.615S6.02 5.5 9.605 5.5h3.803c3.585 0 5.378 0 6.492 1.135c.857.873 1.054 2.156 1.1 4.365"/><path d="M17.111 13.255c.185-.17.277-.255.389-.255s.204.085.389.255l.713.657c.086.079.129.119.182.138c.054.02.112.018.23.013l.962-.038c.248-.01.372-.014.457.057s.102.194.135.44l.132.986c.016.114.023.17.051.22c.028.048.073.083.163.154l.776.61c.192.152.288.227.307.335s-.046.212-.174.42l-.526.847c-.06.097-.09.146-.1.2s.002.111.026.223l.209.978c.05.24.076.36.021.456s-.172.134-.405.21l-.926.301c-.11.036-.166.054-.209.09c-.043.037-.07.089-.123.192l-.452.871c-.115.223-.173.334-.278.372s-.22-.01-.452-.106l-.888-.368c-.109-.045-.163-.068-.22-.068s-.111.023-.22.068l-.888.368c-.232.096-.347.144-.452.106s-.163-.15-.278-.372l-.452-.871c-.054-.103-.08-.155-.123-.191s-.099-.055-.209-.09l-.926-.302c-.233-.076-.35-.114-.405-.21s-.03-.215.021-.456l.21-.978c.023-.112.035-.168.025-.222a.6.6 0 0 0-.1-.2l-.525-.848c-.13-.208-.194-.312-.175-.42s.115-.183.307-.334l.776-.61c.09-.072.135-.107.163-.156s.035-.105.05-.22l.133-.985c.033-.245.05-.369.135-.44s.209-.067.457-.057l.963.038c.117.005.175.007.229-.013c.053-.02.096-.059.182-.138zM16 5.5l-.1-.31c-.495-1.54-.742-2.31-1.331-2.75C13.979 2 13.197 2 11.63 2h-.263c-1.565 0-2.348 0-2.937.44c-.59.44-.837 1.21-1.332 2.75L7 5.5"/></g></svg>
                            <h6><?php echo e(translate('Job Experience')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('languagelevel.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/language-level') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.language.level')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-briefcase" viewBox="0 0 16 16">
                                <path
                                    d="M6.5 1A1.5 1.5 0 0 0 5 2.5V3H1.5A1.5 1.5 0 0 0 0 4.5v8A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-8A1.5 1.5 0 0 0 14.5 3H11v-.5A1.5 1.5 0 0 0 9.5 1h-3zm0 1h3a.5.5 0 0 1 .5.5V3H6v-.5a.5.5 0 0 1 .5-.5zm1.886 6.914L15 7.151V12.5a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5V7.15l6.614 1.764a1.5 1.5 0 0 0 .772 0zM1.5 4h13a.5.5 0 0 1 .5.5v1.616L8.129 7.948a.5.5 0 0 1-.258 0L1 6.116V4.5a.5.5 0 0 1 .5-.5z" />
                            </svg>
                            <h6><?php echo e(translate('Language Level')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('careerlevel.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/career-level') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.career.level')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24"><path fill="#D1D1D1" d="M2.494 18.913h3.52v-3.52c0-.43.35-.78.781-.78h3.52v-3.52c0-.432.35-.783.781-.783h3.52V6.791c0-.432.35-.782.782-.782h3.519V2.49c0-.432.35-.782.782-.782h3.52c.43 0 .781.35.781.782v20.724c0 .432-.35.782-.782.782H2.494a.78.78 0 0 1-.782-.782v-3.52c0-.43.35-.78.782-.78ZM.172 15.928a.587.587 0 0 1 0-.83L15.102.168a.587.587 0 0 1 .83.83l-14.93 14.93c-.23.23-.6.23-.83 0"/></svg>
                            <h6><?php echo e(translate('Career Level')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('profession.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/profession') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.profession')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-award" viewBox="0 0 16 16">
                                <path
                                    d="M9.669.864 8 0 6.331.864l-1.858.282-.842 1.68-1.337 1.32L2.6 6l-.306 1.854 1.337 1.32.842 1.68 1.858.282L8 12l1.669-.864 1.858-.282.842-1.68 1.337-1.32L13.4 6l.306-1.854-1.337-1.32-.842-1.68L9.669.864zm1.196 1.193.684 1.365 1.086 1.072L12.387 6l.248 1.506-1.086 1.072-.684 1.365-1.51.229L8 10.874l-1.355-.702-1.51-.229-.684-1.365-1.086-1.072L3.614 6l-.25-1.506 1.087-1.072.684-1.365 1.51-.229L8 1.126l1.356.702 1.509.229z" />
                                <path d="M4 11.794V16l4-1 4 1v-4.206l-2.018.306L8 13.126 6.018 12.1 4 11.794z" />
                            </svg>
                            <h6><?php echo e(translate('Profession')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                


                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('education.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/education') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.education')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-book" viewBox="0 0 16 16">
                                <path
                                    d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z" />
                            </svg>
                            <h6><?php echo e(translate('Education Lavel')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('package.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/package') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.package.index')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-tags" viewBox="0 0 16 16">
                                <path
                                    d="M3 2v4.586l7 7L14.586 9l-7-7H3zM2 2a1 1 0 0 1 1-1h4.586a1 1 0 0 1 .707.293l7 7a1 1 0 0 1 0 1.414l-4.586 4.586a1 1 0 0 1-1.414 0l-7-7A1 1 0 0 1 2 6.586V2z" />
                                <path
                                    d="M5.5 5a.5.5 0 1 1 0-1 .5.5 0 0 1 0 1zm0 1a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3zM1 7.086a1 1 0 0 0 .293.707L8.75 15.25l-.043.043a1 1 0 0 1-1.414 0l-7-7A1 1 0 0 1 0 7.586V3a1 1 0 0 1 1-1v5.086z" />
                            </svg>
                            <h6><?php echo e(translate('Package Plan')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>


                <span class="menu-panel-label"> <?php echo e(translate('Manage Staff')); ?> </span>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/admins-list') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.list')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-person" viewBox="0 0 16 16">
                                <path
                                    d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4Zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10Z" />
                            </svg>
                            <h6><?php echo e(translate('Users')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permission.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/permission') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.permission.index')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-person" viewBox="0 0 16 16">
                                <path
                                    d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4Zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10Z" />
                            </svg>
                            <h6><?php echo e(translate('Permission')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('role.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/roles') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.role.list')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-person" viewBox="0 0 16 16">
                                <path
                                    d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4Zm-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h10Z" />
                            </svg>
                            <h6><?php echo e(translate('User Role')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <span class="menu-panel-label"> <?php echo e(translate('Appearance')); ?> </span>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('themeoption.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/theme-option') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.theme.option')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-palette" viewBox="0 0 16 16">
                                <path
                                    d="M8 5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3zm4 3a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3zM5.5 7a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm.5 6a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z" />
                                <path
                                    d="M16 8c0 3.15-1.866 2.585-3.567 2.07C11.42 9.763 10.465 9.473 10 10c-.603.683-.475 1.819-.351 2.92C9.826 14.495 9.996 16 8 16a8 8 0 1 1 8-8zm-8 7c.611 0 .654-.171.655-.176.078-.146.124-.464.07-1.119-.014-.168-.037-.37-.061-.591-.052-.464-.112-1.005-.118-1.462-.01-.707.083-1.61.704-2.314.369-.417.845-.578 1.272-.618.404-.038.812.026 1.16.104.343.077.702.186 1.025.284l.028.008c.346.105.658.199.953.266.653.148.904.083.991.024C14.717 9.38 15 9.161 15 8a7 7 0 1 0-7 7z" />
                            </svg>
                            <h6><?php echo e(translate('Theme Option')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>


                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('emailtemplate.menu')): ?>
                    <li
                        class="<?php echo e(request()->is('admin/email-template') || request()->is('admin/email-template/*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.email.template')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-envelope-plus" viewBox="0 0 16 16">
                                <path
                                    d="M2 2a2 2 0 0 0-2 2v8.01A2 2 0 0 0 2 14h5.5a.5.5 0 0 0 0-1H2a1 1 0 0 1-.966-.741l5.64-3.471L8 9.583l7-4.2V8.5a.5.5 0 0 0 1 0V4a2 2 0 0 0-2-2H2Zm3.708 6.208L1 11.105V5.383l4.708 2.825ZM1 4.217V4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v.217l-7 4.2-7-4.2Z" />
                                <path
                                    d="M16 12.5a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Zm-3.5-2a.5.5 0 0 0-.5.5v1h-1a.5.5 0 0 0 0 1h1v1a.5.5 0 0 0 1 0v-1h1a.5.5 0 0 0 0-1h-1v-1a.5.5 0 0 0-.5-.5Z" />
                            </svg>
                            <h6><?php echo e(translate('Email Template')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('menu.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/menu/manage') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.menu')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-menu-button" viewBox="0 0 16 16">
                                <path
                                    d="M0 1.5A1.5 1.5 0 0 1 1.5 0h8A1.5 1.5 0 0 1 11 1.5v2A1.5 1.5 0 0 1 9.5 5h-8A1.5 1.5 0 0 1 0 3.5v-2zM1.5 1a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5h8a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 0-.5-.5h-8z" />
                                <path
                                    d="m7.823 2.823-.396-.396A.25.25 0 0 1 7.604 2h.792a.25.25 0 0 1 .177.427l-.396.396a.25.25 0 0 1-.354 0zM0 8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V8zm1 3v2a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2H1zm14-1V8a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2h14zM2 8.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5z" />
                            </svg>
                            <h6><?php echo e(translate('Menu')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>


                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('page.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/pages') || request()->is('admin/pages/*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.pages')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-file-earmark" viewBox="0 0 16 16">
                                <path
                                    d="M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h5.5L14 4.5zm-3 0A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4.5h-2z" />
                            </svg>
                            <h6><?php echo e(translate('Pages')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>
                <span class="menu-panel-label"> <?php echo e(translate('Others')); ?> </span>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('blogcategory.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/blog-category') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.blog.category')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-bookshelf" viewBox="0 0 16 16">
                                <path
                                    d="M2.5 0a.5.5 0 0 1 .5.5V2h10V.5a.5.5 0 0 1 1 0v15a.5.5 0 0 1-1 0V15H3v.5a.5.5 0 0 1-1 0V.5a.5.5 0 0 1 .5-.5zM3 14h10v-3H3v3zm0-4h10V7H3v3zm0-4h10V3H3v3z" />
                            </svg>
                            <h6><?php echo e(translate('Blog Category')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('blog.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/blog') || request()->is('admin/blog/* ') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.blog')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-bookshelf" viewBox="0 0 16 16">
                                <path
                                    d="M2.5 0a.5.5 0 0 1 .5.5V2h10V.5a.5.5 0 0 1 1 0v15a.5.5 0 0 1-1 0V15H3v.5a.5.5 0 0 1-1 0V.5a.5.5 0 0 1 .5-.5zM3 14h10v-3H3v3zm0-4h10V7H3v3zm0-4h10V3H3v3z" />
                            </svg>
                            <h6><?php echo e(translate('Blog')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('testimonial.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/testimonial') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.testimonial')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-blockquote-left" viewBox="0 0 16 16">
                                <path
                                    d="M2.5 3a.5.5 0 0 0 0 1h11a.5.5 0 0 0 0-1h-11zm5 3a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1h-6zm0 3a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1h-6zm-5 3a.5.5 0 0 0 0 1h11a.5.5 0 0 0 0-1h-11zm.79-5.373c.112-.078.26-.17.444-.275L3.524 6c-.122.074-.272.17-.452.287-.18.117-.35.26-.51.428a2.425 2.425 0 0 0-.398.562c-.11.207-.164.438-.164.692 0 .36.072.65.217.873.144.219.385.328.72.328.215 0 .383-.07.504-.211a.697.697 0 0 0 .188-.463c0-.23-.07-.404-.211-.521-.137-.121-.326-.182-.568-.182h-.282c.024-.203.065-.37.123-.498a1.38 1.38 0 0 1 .252-.37 1.94 1.94 0 0 1 .346-.298zm2.167 0c.113-.078.262-.17.445-.275L5.692 6c-.122.074-.272.17-.452.287-.18.117-.35.26-.51.428a2.425 2.425 0 0 0-.398.562c-.11.207-.164.438-.164.692 0 .36.072.65.217.873.144.219.385.328.72.328.215 0 .383-.07.504-.211a.697.697 0 0 0 .188-.463c0-.23-.07-.404-.211-.521-.137-.121-.326-.182-.568-.182h-.282a1.75 1.75 0 0 1 .118-.492c.058-.13.144-.254.257-.375a1.94 1.94 0 0 1 .346-.3z" />
                            </svg>
                            <h6><?php echo e(translate('Testimonial')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('trustedcompany.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/trusted-company') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.trusted.company')); ?>">
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M14.0449 0.140627C13.8574 0.216799 13.6055 0.351564 13.4883 0.433596C13.3652 0.515627 10.4062 3.45117 6.90233 6.95508C1.76952 12.0996 0.509752 13.3887 0.386705 13.6348C-0.175796 14.7715 0.0117046 16.0137 0.873033 16.8809C1.4121 17.4141 2.09764 17.6953 2.8828 17.6953H3.28124V22.3828C3.28124 25.5527 3.30467 27.1641 3.34569 27.375C3.56835 28.4414 4.42967 29.4082 5.49608 29.7773C5.8828 29.9121 5.9121 29.9121 8.74218 29.9121C11.5312 29.9121 11.5957 29.9121 11.7539 29.7891C11.8418 29.7246 11.9707 29.5957 12.0351 29.5078C12.1582 29.3496 12.1582 29.2734 12.1875 25.4355L12.2168 21.5215L12.3926 21.2344C12.6152 20.877 13.0664 20.5898 13.5 20.5371C13.6641 20.5137 14.4844 20.5078 15.3223 20.5195L16.8457 20.5371L17.1562 20.707C17.4492 20.8711 17.6367 21.0645 17.8183 21.3867C17.8828 21.5098 17.9062 22.2305 17.9297 25.4414C17.959 29.2734 17.959 29.3496 18.082 29.5078C18.1465 29.5957 18.2754 29.7246 18.3633 29.7891C18.5215 29.9121 18.5859 29.9121 21.375 29.9121C24.2051 29.9121 24.2344 29.9121 24.6211 29.7773C25.6875 29.4082 26.5488 28.4414 26.7715 27.375C26.8125 27.1641 26.8359 25.5527 26.8359 22.3828V17.6953H27.2344C28.3359 17.6953 29.2969 17.1094 29.7715 16.1426C29.9707 15.7383 29.9707 15.7266 29.9707 14.9414C29.9707 13.6055 30.6445 14.3965 23.1211 6.87891C15.6445 -0.603514 16.3945 0.0410175 15.1172 0.0117207C14.4551 1.90735e-06 14.3555 0.0117207 14.0449 0.140627ZM15.4922 1.81641C15.8379 1.98047 28.084 14.2441 28.2129 14.543C28.3359 14.8418 28.3242 15.0938 28.1777 15.3867C27.9609 15.8379 27.7793 15.9023 26.6484 15.9375C25.5879 15.9668 25.5117 15.9902 25.2305 16.3711C25.1074 16.5293 25.1074 16.5938 25.0781 21.8438L25.0488 27.1582L24.8848 27.4336C24.7969 27.5859 24.6387 27.7793 24.5332 27.8555C24.123 28.1719 24 28.1836 21.7617 28.1836H19.6875V24.9023C19.6875 22.752 19.6641 21.5215 19.623 21.3164C19.3945 20.2148 18.5098 19.2656 17.373 18.9082C16.9863 18.7852 16.8574 18.7793 15.0586 18.7793C12.9609 18.7793 12.8555 18.791 12.1055 19.1719C11.3613 19.5469 10.6699 20.4727 10.4941 21.3164C10.4531 21.5215 10.4297 22.752 10.4297 24.9023V28.1836H8.35546C6.11718 28.1836 5.99413 28.1719 5.58397 27.8555C5.4785 27.7793 5.3203 27.5859 5.23241 27.4336L5.06835 27.1582L5.03905 21.8438C5.00975 16.5938 5.00975 16.5293 4.88671 16.3711C4.60546 15.9902 4.52928 15.9668 3.46874 15.9375C2.33788 15.9023 2.15624 15.8379 1.93944 15.3867C1.79296 15.0938 1.78124 14.8418 1.90428 14.543C2.02733 14.25 14.2793 1.98633 14.6191 1.81641C14.918 1.66992 15.1933 1.66992 15.4922 1.81641Z" />
                            </svg>
                            <h6><?php echo e(translate('Trusted Company')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>



                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('faq.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/faq') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.faq')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-patch-question" viewBox="0 0 16 16">
                                <path
                                    d="M8.05 9.6c.336 0 .504-.24.554-.627.04-.534.198-.815.847-1.26.673-.475 1.049-1.09 1.049-1.986 0-1.325-.92-2.227-2.262-2.227-1.02 0-1.792.492-2.1 1.29A1.71 1.71 0 0 0 6 5.48c0 .393.203.64.545.64.272 0 .455-.147.564-.51.158-.592.525-.915 1.074-.915.61 0 1.03.446 1.03 1.084 0 .563-.208.885-.822 1.325-.619.433-.926.914-.926 1.64v.111c0 .428.208.745.585.745z" />
                                <path
                                    d="m10.273 2.513-.921-.944.715-.698.622.637.89-.011a2.89 2.89 0 0 1 2.924 2.924l-.01.89.636.622a2.89 2.89 0 0 1 0 4.134l-.637.622.011.89a2.89 2.89 0 0 1-2.924 2.924l-.89-.01-.622.636a2.89 2.89 0 0 1-4.134 0l-.622-.637-.89.011a2.89 2.89 0 0 1-2.924-2.924l.01-.89-.636-.622a2.89 2.89 0 0 1 0-4.134l.637-.622-.011-.89a2.89 2.89 0 0 1 2.924-2.924l.89.01.622-.636a2.89 2.89 0 0 1 4.134 0l-.715.698a1.89 1.89 0 0 0-2.704 0l-.92.944-1.32-.016a1.89 1.89 0 0 0-1.911 1.912l.016 1.318-.944.921a1.89 1.89 0 0 0 0 2.704l.944.92-.016 1.32a1.89 1.89 0 0 0 1.912 1.911l1.318-.016.921.944a1.89 1.89 0 0 0 2.704 0l.92-.944 1.32.016a1.89 1.89 0 0 0 1.911-1.912l-.016-1.318.944-.921a1.89 1.89 0 0 0 0-2.704l-.944-.92.016-1.32a1.89 1.89 0 0 0-1.912-1.911l-1.318.016z" />
                                <path d="M7.001 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0z" />
                            </svg>
                            <h6><?php echo e(translate('FAQ')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('language.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/language') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.language.list')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-translate" viewBox="0 0 16 16">
                                <path
                                    d="M4.545 6.714 4.11 8H3l1.862-5h1.284L8 8H6.833l-.435-1.286H4.545zm1.634-.736L5.5 3.956h-.049l-.679 2.022H6.18z" />
                                <path
                                    d="M0 2a2 2 0 0 1 2-2h7a2 2 0 0 1 2 2v3h3a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3H2a2 2 0 0 1-2-2V2zm2-1a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H2zm7.138 9.995c.193.301.402.583.63.846-.748.575-1.673 1.001-2.768 1.292.178.217.451.635.555.867 1.125-.359 2.08-.844 2.886-1.494.777.665 1.739 1.165 2.93 1.472.133-.254.414-.673.629-.89-1.125-.253-2.057-.694-2.82-1.284.681-.747 1.222-1.651 1.621-2.757H14V8h-3v1.047h.765c-.318.844-.74 1.546-1.272 2.13a6.066 6.066 0 0 1-.415-.492 1.988 1.988 0 0 1-.94.31z" />
                            </svg>
                            <h6><?php echo e(translate('Language')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>


                


                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('country.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/country') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.country')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-geo-alt" viewBox="0 0 16 16">
                                <path
                                    d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z" />
                                <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z" />
                            </svg>
                            <h6><?php echo e(translate('Country')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>



                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('state.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/state') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.state')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-map" viewBox="0 0 16 16">
                                <path fill-rule="evenodd"
                                    d="M15.817.113A.5.5 0 0 1 16 .5v14a.5.5 0 0 1-.402.49l-5 1a.502.502 0 0 1-.196 0L5.5 15.01l-4.902.98A.5.5 0 0 1 0 15.5v-14a.5.5 0 0 1 .402-.49l5-1a.5.5 0 0 1 .196 0L10.5.99l4.902-.98a.5.5 0 0 1 .415.103zM10 1.91l-4-.8v12.98l4 .8V1.91zm1 12.98 4-.8V1.11l-4 .8v12.98zm-6-.8V1.11l-4 .8v12.98l4-.8z" />
                            </svg>
                            <h6><?php echo e(translate('State')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>


                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('city.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/city') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.city')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-menu-button" viewBox="0 0 16 16">
                                <path
                                    d="M0 1.5A1.5 1.5 0 0 1 1.5 0h8A1.5 1.5 0 0 1 11 1.5v2A1.5 1.5 0 0 1 9.5 5h-8A1.5 1.5 0 0 1 0 3.5v-2zM1.5 1a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5h8a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 0-.5-.5h-8z" />
                                <path
                                    d="m7.823 2.823-.396-.396A.25.25 0 0 1 7.604 2h.792a.25.25 0 0 1 .177.427l-.396.396a.25.25 0 0 1-.354 0zM0 8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V8zm1 3v2a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2H1zm14-1V8a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2h14zM2 8.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5z" />
                            </svg>
                            <h6><?php echo e(translate('City')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('backendsetting.menu')): ?>
                    <li class="<?php echo e(request()->is('admin/backend-setting') ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('admin.backend.setting')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-menu-button" viewBox="0 0 16 16">
                                <path
                                    d="M0 1.5A1.5 1.5 0 0 1 1.5 0h8A1.5 1.5 0 0 1 11 1.5v2A1.5 1.5 0 0 1 9.5 5h-8A1.5 1.5 0 0 1 0 3.5v-2zM1.5 1a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5h8a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 0-.5-.5h-8z" />
                                <path
                                    d="m7.823 2.823-.396-.396A.25.25 0 0 1 7.604 2h.792a.25.25 0 0 1 .177.427l-.396.396a.25.25 0 0 1-.354 0zM0 8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V8zm1 3v2a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2H1zm14-1V8a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2h14zM2 8.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5z" />
                            </svg>
                            <h6><?php echo e(translate('Backend Setting')); ?></h6>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</div>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/admin/includes/sidebar.blade.php ENDPATH**/ ?>