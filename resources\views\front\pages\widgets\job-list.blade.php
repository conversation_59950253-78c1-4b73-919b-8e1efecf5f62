@php

    $singleWidgetData = [];
    if (!empty($singleWidgetDataShow)) {
        $singleWidgetData = $singleWidgetDataShow->getTranslation('widget_content');
    }

    $jobs = $datas ? $datas : allJobs($singleWidgetData['show_item'] ?? 15);
@endphp
<!-- ========== Job Listing Start============= -->
<div class="sidebar-section mb-120">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 mb-30">
                <div class="product-card-top-area">
                    <div class="left-content">
                        <h6>Showing <span>09</span> of <span>12</span> results</h6>
                    </div>
                    <div class="right-content">
                        <ul class="size-icon grid-view d-lg-flex d-none">
                            <li class="column-2 active">
                                <svg width="7" height="14" viewBox="0 0 7 14" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0.75 13.1875L0.749999 0.8125M5.8125 13.1875L5.8125 0.8125"
                                        stroke="#A0A0A0" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </li>
                            <li class="column-1">
                                <svg width="14" height="10" viewBox="0 0 14 10" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0.8125 1.0625H13.1875M0.8125 5H13.1875M0.8125 8.9375H13.1875"
                                        stroke="#A0A0A0" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </li>
                        </ul>
                        <div class="category-area">
                            <select>
                                <option value="1">Default sorting</option>
                                <option value="2">Sort by latest </option>
                                <option value="2">Sort by Best selling </option>
                                <option value="2">Sort by Low to high </option>
                                <option value="2">Sort by high to low </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row gy-5">
            <div class="col-xl-4 order-xl-1 order-2">
                <div class="left-sidebar">
                    <div class="left-sidebar-wrapper">
                        <div class="single-widgets mb-25">
                            <div class="widget-title">
                                <h5>Category</h5>
                            </div>
                            <div class="checkbox-container">
                                <ul>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Product manager</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>UIUX Designer</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Web developer</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Academic Counselling</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Human Resources (HR)</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Animation & Motion Graphics</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Social Media Management</span>
                                        </label>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="single-widgets mb-25">
                            <div class="widget-title">
                                <h5>Location</h5>
                            </div>
                            <div class="checkbox-container">
                                <ul>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Dhaka, Bangladesh</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>New York, USA</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>London, UK</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Sydney, Australia</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Toronto, Canada</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Berlin, Germany</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Rome, Italy</span>
                                        </label>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="single-widgets mb-25">
                            <div class="widget-title">
                                <h5>Type</h5>
                            </div>
                            <div class="checkbox-container">
                                <ul>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Full-Time</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Part-Time</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Temporary</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>On-Site</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Remote</span>
                                        </label>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="single-widgets mb-25">
                            <div class="widget-title">
                                <h5>Level</h5>
                            </div>
                            <div class="checkbox-container">
                                <ul>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Internship</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Junior- Level </span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Mid-Level</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Senior-Level</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Expert-Level</span>
                                        </label>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="single-widgets mb-25">
                            <div class="widget-title">
                                <h5>Experience</h5>
                            </div>
                            <div class="checkbox-container">
                                <ul>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>0-1 Years</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>2-3 Years</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>4-5 Years</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>6-7 Years</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>8+ Years</span>
                                        </label>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="single-widgets mb-25">
                            <div class="widget-title">
                                <h5>Date Posted</h5>
                            </div>
                            <div class="checkbox-container">
                                <ul>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Today(02)</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Last week ago(08)</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>Last month ago(10)</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>6 month ago(04)</span>
                                        </label>
                                    </li>
                                    <li>
                                        <label class="containerss">
                                            <input type="checkbox">
                                            <span class="checkmark"></span>
                                            <span>1 year ago(00)</span>
                                        </label>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="single-widgets">
                            <div class="widget-title">
                                <h5>Salary Range</h5>
                            </div>
                            <div class="range-wrap">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <form>
                                            <input type="hidden" name="min-value" value="">
                                            <input type="hidden" name="max-value" value="">
                                        </form>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div id="slider-range"></div>
                                    </div>
                                </div>
                                <div class="slider-labels">
                                    <div class="caption">
                                        <span id="slider-range-value1"></span>
                                    </div>
                                    <div class="caption">
                                        <span id="slider-range-value2"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-8 order-xl-2 order-1">
                <div class="list-grid-product-wrap">
                    <div class="row gy-4 mb-70">
                        <div class="col-xl-6 col-lg-6 col-md-6 item">
                            <div class="feature-card two">
                                <div class="feature-wrape">
                                    <div class="company-area">
                                        <div class="logo">
                                            <img src="assets/image/company-logo.png" alt="">
                                        </div>
                                        <div class="company-details">
                                            <div class="name-location">
                                                <h5>Frontend Developer</h5>
                                                <p>By <span>TechNova Solutions</span> in Web Development</p>
                                            </div>
                                            <div class="bookmark">
                                                <i class="bi bi-heart"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-discription">
                                        <ul>
                                            <li>
                                                On-Site
                                            </li>
                                            <li class="style-2">
                                                <svg width="10" height="10" viewBox="0 0 10 10"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.60375 5.5875C7.27625 6.25125 6.8325 6.9125 6.37875 7.50625C5.94784 8.06563 5.48762 8.6018 5 9.1125C4.51238 8.6018 4.05216 8.06563 3.62125 7.50625C3.1675 6.9125 2.72375 6.25125 2.39625 5.5875C2.065 4.91687 1.875 4.28875 1.875 3.75C1.875 2.9212 2.20424 2.12634 2.79029 1.54029C3.37634 0.95424 4.1712 0.625 5 0.625C5.8288 0.625 6.62366 0.95424 7.20971 1.54029C7.79576 2.12634 8.125 2.9212 8.125 3.75C8.125 4.28875 7.93438 4.91687 7.60375 5.5875ZM5 10C5 10 8.75 6.44625 8.75 3.75C8.75 2.75544 8.35491 1.80161 7.65165 1.09835C6.94839 0.395088 5.99456 0 5 0C4.00544 0 3.05161 0.395088 2.34835 1.09835C1.64509 1.80161 1.25 2.75544 1.25 3.75C1.25 6.44625 5 10 5 10Z" />
                                                    <path
                                                        d="M5 5C4.66848 5 4.35054 4.8683 4.11612 4.63388C3.8817 4.39946 3.75 4.08152 3.75 3.75C3.75 3.41848 3.8817 3.10054 4.11612 2.86612C4.35054 2.6317 4.66848 2.5 5 2.5C5.33152 2.5 5.64946 2.6317 5.88388 2.86612C6.1183 3.10054 6.25 3.41848 6.25 3.75C6.25 4.08152 6.1183 4.39946 5.88388 4.63388C5.64946 4.8683 5.33152 5 5 5ZM5 5.625C5.49728 5.625 5.97419 5.42746 6.32583 5.07583C6.67746 4.72419 6.875 4.24728 6.875 3.75C6.875 3.25272 6.67746 2.77581 6.32583 2.42417C5.97419 2.07254 5.49728 1.875 5 1.875C4.50272 1.875 4.02581 2.07254 3.67417 2.42417C3.32254 2.77581 3.125 3.25272 3.125 3.75C3.125 4.24728 3.32254 4.72419 3.67417 5.07583C4.02581 5.42746 4.50272 5.625 5 5.625Z" />
                                                </svg>
                                                New York City
                                            </li>
                                            <li class="style-3">
                                                $25k-30k
                                            </li>
                                            <li class="style-4">
                                                Full-Time
                                            </li>
                                            <li class="style-5">
                                                Vacancy: 01
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="date">
                                        <p>Deadline: 30 Apr 2025</p>
                                    </div>
                                </div>
                                <div class="button-area">
                                    <a href="find-jobs-details.html" class="primary-btn-4">Apply Now
                                        <svg width="12" height="12" viewBox="0 0 12 12"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.8125 0.750301C10.9285 0.750301 11.0398 0.796394 11.1219 0.878442C11.2039 0.960489 11.25 1.07177 11.25 1.1878L11.25 6.4378C11.25 6.55383 11.2039 6.66511 11.1219 6.74716C11.0398 6.82921 10.9285 6.8753 10.8125 6.8753C10.6965 6.8753 10.5852 6.82921 10.5031 6.74716C10.4211 6.66511 10.375 6.55383 10.375 6.4378L10.375 2.24393L1.49725 11.1226C1.45657 11.1632 1.40828 11.1955 1.35514 11.2175C1.30199 11.2395 1.24503 11.2509 1.1875 11.2509C1.12997 11.2509 1.07301 11.2395 1.01986 11.2175C0.966718 11.1955 0.918427 11.1632 0.87775 11.1226C0.837073 11.0819 0.804806 11.0336 0.782792 10.9804C0.760777 10.9273 0.749448 10.8703 0.749448 10.8128C0.749448 10.7553 0.760777 10.6983 0.782792 10.6452C0.804806 10.592 0.837073 10.5437 0.87775 10.5031L9.75637 1.6253L5.5625 1.6253C5.44647 1.6253 5.33519 1.57921 5.25314 1.49716C5.17109 1.41511 5.125 1.30383 5.125 1.1878C5.125 1.07177 5.17109 0.96049 5.25314 0.878442C5.33519 0.796395 5.44647 0.750302 5.5625 0.750302L10.8125 0.750301Z" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 item">
                            <div class="feature-card two">
                                <div class="feature-wrape">
                                    <div class="company-area">
                                        <div class="logo">
                                            <img src="assets/image/company-logo2.png" alt="">
                                        </div>
                                        <div class="company-details">
                                            <div class="name-location">
                                                <h5>Financial Analyst</h5>
                                                <p>By <span>Finpeak Group</span> in Finance & Investment</p>
                                            </div>
                                            <div class="bookmark">
                                                <i class="bi bi-heart"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-discription">
                                        <ul>
                                            <li>
                                                On-Site
                                            </li>
                                            <li class="style-2">
                                                <svg width="10" height="10" viewBox="0 0 10 10"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.60375 5.5875C7.27625 6.25125 6.8325 6.9125 6.37875 7.50625C5.94784 8.06563 5.48762 8.6018 5 9.1125C4.51238 8.6018 4.05216 8.06563 3.62125 7.50625C3.1675 6.9125 2.72375 6.25125 2.39625 5.5875C2.065 4.91687 1.875 4.28875 1.875 3.75C1.875 2.9212 2.20424 2.12634 2.79029 1.54029C3.37634 0.95424 4.1712 0.625 5 0.625C5.8288 0.625 6.62366 0.95424 7.20971 1.54029C7.79576 2.12634 8.125 2.9212 8.125 3.75C8.125 4.28875 7.93438 4.91687 7.60375 5.5875ZM5 10C5 10 8.75 6.44625 8.75 3.75C8.75 2.75544 8.35491 1.80161 7.65165 1.09835C6.94839 0.395088 5.99456 0 5 0C4.00544 0 3.05161 0.395088 2.34835 1.09835C1.64509 1.80161 1.25 2.75544 1.25 3.75C1.25 6.44625 5 10 5 10Z" />
                                                    <path
                                                        d="M5 5C4.66848 5 4.35054 4.8683 4.11612 4.63388C3.8817 4.39946 3.75 4.08152 3.75 3.75C3.75 3.41848 3.8817 3.10054 4.11612 2.86612C4.35054 2.6317 4.66848 2.5 5 2.5C5.33152 2.5 5.64946 2.6317 5.88388 2.86612C6.1183 3.10054 6.25 3.41848 6.25 3.75C6.25 4.08152 6.1183 4.39946 5.88388 4.63388C5.64946 4.8683 5.33152 5 5 5ZM5 5.625C5.49728 5.625 5.97419 5.42746 6.32583 5.07583C6.67746 4.72419 6.875 4.24728 6.875 3.75C6.875 3.25272 6.67746 2.77581 6.32583 2.42417C5.97419 2.07254 5.49728 1.875 5 1.875C4.50272 1.875 4.02581 2.07254 3.67417 2.42417C3.32254 2.77581 3.125 3.25272 3.125 3.75C3.125 4.24728 3.32254 4.72419 3.67417 5.07583C4.02581 5.42746 4.50272 5.625 5 5.625Z" />
                                                </svg>
                                                New York City
                                            </li>
                                            <li class="style-3">
                                                $25k-30k
                                            </li>
                                            <li class="style-4">
                                                Full-Time
                                            </li>
                                            <li class="style-5">
                                                Vacancy: 01
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="date">
                                        <p>Deadline: 30 Apr 2025</p>
                                    </div>
                                </div>
                                <div class="button-area">
                                    <a href="find-jobs-details.html" class="primary-btn-4">Apply Now
                                        <svg width="12" height="12" viewBox="0 0 12 12"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.8125 0.750301C10.9285 0.750301 11.0398 0.796394 11.1219 0.878442C11.2039 0.960489 11.25 1.07177 11.25 1.1878L11.25 6.4378C11.25 6.55383 11.2039 6.66511 11.1219 6.74716C11.0398 6.82921 10.9285 6.8753 10.8125 6.8753C10.6965 6.8753 10.5852 6.82921 10.5031 6.74716C10.4211 6.66511 10.375 6.55383 10.375 6.4378L10.375 2.24393L1.49725 11.1226C1.45657 11.1632 1.40828 11.1955 1.35514 11.2175C1.30199 11.2395 1.24503 11.2509 1.1875 11.2509C1.12997 11.2509 1.07301 11.2395 1.01986 11.2175C0.966718 11.1955 0.918427 11.1632 0.87775 11.1226C0.837073 11.0819 0.804806 11.0336 0.782792 10.9804C0.760777 10.9273 0.749448 10.8703 0.749448 10.8128C0.749448 10.7553 0.760777 10.6983 0.782792 10.6452C0.804806 10.592 0.837073 10.5437 0.87775 10.5031L9.75637 1.6253L5.5625 1.6253C5.44647 1.6253 5.33519 1.57921 5.25314 1.49716C5.17109 1.41511 5.125 1.30383 5.125 1.1878C5.125 1.07177 5.17109 0.96049 5.25314 0.878442C5.33519 0.796395 5.44647 0.750302 5.5625 0.750302L10.8125 0.750301Z" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 item">
                            <div class="feature-card two">
                                <div class="feature-wrape">
                                    <div class="company-area">
                                        <div class="logo">
                                            <img src="assets/image/company-logo3.png" alt="">
                                        </div>
                                        <div class="company-details">
                                            <div class="name-location">
                                                <h5>Brand Manager</h5>
                                                <p>By <span>Kingowl Organics</span> in Marketing</p>
                                            </div>
                                            <div class="bookmark">
                                                <i class="bi bi-heart"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-discription">
                                        <ul>
                                            <li>
                                                On-Site
                                            </li>
                                            <li class="style-2">
                                                <svg width="10" height="10" viewBox="0 0 10 10"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.60375 5.5875C7.27625 6.25125 6.8325 6.9125 6.37875 7.50625C5.94784 8.06563 5.48762 8.6018 5 9.1125C4.51238 8.6018 4.05216 8.06563 3.62125 7.50625C3.1675 6.9125 2.72375 6.25125 2.39625 5.5875C2.065 4.91687 1.875 4.28875 1.875 3.75C1.875 2.9212 2.20424 2.12634 2.79029 1.54029C3.37634 0.95424 4.1712 0.625 5 0.625C5.8288 0.625 6.62366 0.95424 7.20971 1.54029C7.79576 2.12634 8.125 2.9212 8.125 3.75C8.125 4.28875 7.93438 4.91687 7.60375 5.5875ZM5 10C5 10 8.75 6.44625 8.75 3.75C8.75 2.75544 8.35491 1.80161 7.65165 1.09835C6.94839 0.395088 5.99456 0 5 0C4.00544 0 3.05161 0.395088 2.34835 1.09835C1.64509 1.80161 1.25 2.75544 1.25 3.75C1.25 6.44625 5 10 5 10Z" />
                                                    <path
                                                        d="M5 5C4.66848 5 4.35054 4.8683 4.11612 4.63388C3.8817 4.39946 3.75 4.08152 3.75 3.75C3.75 3.41848 3.8817 3.10054 4.11612 2.86612C4.35054 2.6317 4.66848 2.5 5 2.5C5.33152 2.5 5.64946 2.6317 5.88388 2.86612C6.1183 3.10054 6.25 3.41848 6.25 3.75C6.25 4.08152 6.1183 4.39946 5.88388 4.63388C5.64946 4.8683 5.33152 5 5 5ZM5 5.625C5.49728 5.625 5.97419 5.42746 6.32583 5.07583C6.67746 4.72419 6.875 4.24728 6.875 3.75C6.875 3.25272 6.67746 2.77581 6.32583 2.42417C5.97419 2.07254 5.49728 1.875 5 1.875C4.50272 1.875 4.02581 2.07254 3.67417 2.42417C3.32254 2.77581 3.125 3.25272 3.125 3.75C3.125 4.24728 3.32254 4.72419 3.67417 5.07583C4.02581 5.42746 4.50272 5.625 5 5.625Z" />
                                                </svg>
                                                New York City
                                            </li>
                                            <li class="style-3">
                                                $25k-30k
                                            </li>
                                            <li class="style-4">
                                                Full-Time
                                            </li>
                                            <li class="style-5">
                                                Vacancy: 01
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="date">
                                        <p>Deadline: 30 Apr 2025</p>
                                    </div>
                                </div>
                                <div class="button-area">
                                    <a href="find-jobs-details.html" class="primary-btn-4">Apply Now
                                        <svg width="12" height="12" viewBox="0 0 12 12"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.8125 0.750301C10.9285 0.750301 11.0398 0.796394 11.1219 0.878442C11.2039 0.960489 11.25 1.07177 11.25 1.1878L11.25 6.4378C11.25 6.55383 11.2039 6.66511 11.1219 6.74716C11.0398 6.82921 10.9285 6.8753 10.8125 6.8753C10.6965 6.8753 10.5852 6.82921 10.5031 6.74716C10.4211 6.66511 10.375 6.55383 10.375 6.4378L10.375 2.24393L1.49725 11.1226C1.45657 11.1632 1.40828 11.1955 1.35514 11.2175C1.30199 11.2395 1.24503 11.2509 1.1875 11.2509C1.12997 11.2509 1.07301 11.2395 1.01986 11.2175C0.966718 11.1955 0.918427 11.1632 0.87775 11.1226C0.837073 11.0819 0.804806 11.0336 0.782792 10.9804C0.760777 10.9273 0.749448 10.8703 0.749448 10.8128C0.749448 10.7553 0.760777 10.6983 0.782792 10.6452C0.804806 10.592 0.837073 10.5437 0.87775 10.5031L9.75637 1.6253L5.5625 1.6253C5.44647 1.6253 5.33519 1.57921 5.25314 1.49716C5.17109 1.41511 5.125 1.30383 5.125 1.1878C5.125 1.07177 5.17109 0.96049 5.25314 0.878442C5.33519 0.796395 5.44647 0.750302 5.5625 0.750302L10.8125 0.750301Z" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 item">
                            <div class="feature-card two">
                                <div class="feature-wrape">
                                    <div class="company-area">
                                        <div class="logo">
                                            <img src="assets/image/company-logo4.png" alt="">
                                        </div>
                                        <div class="company-details">
                                            <div class="name-location">
                                                <h5>Clinical Research Associate</h5>
                                                <p>By <span>Medisync Labs</span> in Research & Development</p>
                                            </div>
                                            <div class="bookmark">
                                                <i class="bi bi-heart"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-discription">
                                        <ul>
                                            <li>
                                                On-Site
                                            </li>
                                            <li class="style-2">
                                                <svg width="10" height="10" viewBox="0 0 10 10"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.60375 5.5875C7.27625 6.25125 6.8325 6.9125 6.37875 7.50625C5.94784 8.06563 5.48762 8.6018 5 9.1125C4.51238 8.6018 4.05216 8.06563 3.62125 7.50625C3.1675 6.9125 2.72375 6.25125 2.39625 5.5875C2.065 4.91687 1.875 4.28875 1.875 3.75C1.875 2.9212 2.20424 2.12634 2.79029 1.54029C3.37634 0.95424 4.1712 0.625 5 0.625C5.8288 0.625 6.62366 0.95424 7.20971 1.54029C7.79576 2.12634 8.125 2.9212 8.125 3.75C8.125 4.28875 7.93438 4.91687 7.60375 5.5875ZM5 10C5 10 8.75 6.44625 8.75 3.75C8.75 2.75544 8.35491 1.80161 7.65165 1.09835C6.94839 0.395088 5.99456 0 5 0C4.00544 0 3.05161 0.395088 2.34835 1.09835C1.64509 1.80161 1.25 2.75544 1.25 3.75C1.25 6.44625 5 10 5 10Z" />
                                                    <path
                                                        d="M5 5C4.66848 5 4.35054 4.8683 4.11612 4.63388C3.8817 4.39946 3.75 4.08152 3.75 3.75C3.75 3.41848 3.8817 3.10054 4.11612 2.86612C4.35054 2.6317 4.66848 2.5 5 2.5C5.33152 2.5 5.64946 2.6317 5.88388 2.86612C6.1183 3.10054 6.25 3.41848 6.25 3.75C6.25 4.08152 6.1183 4.39946 5.88388 4.63388C5.64946 4.8683 5.33152 5 5 5ZM5 5.625C5.49728 5.625 5.97419 5.42746 6.32583 5.07583C6.67746 4.72419 6.875 4.24728 6.875 3.75C6.875 3.25272 6.67746 2.77581 6.32583 2.42417C5.97419 2.07254 5.49728 1.875 5 1.875C4.50272 1.875 4.02581 2.07254 3.67417 2.42417C3.32254 2.77581 3.125 3.25272 3.125 3.75C3.125 4.24728 3.32254 4.72419 3.67417 5.07583C4.02581 5.42746 4.50272 5.625 5 5.625Z" />
                                                </svg>
                                                New York City
                                            </li>
                                            <li class="style-3">
                                                $25k-30k
                                            </li>
                                            <li class="style-4">
                                                Full-Time
                                            </li>
                                            <li class="style-5">
                                                Vacancy: 01
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="date">
                                        <p>Deadline: 30 Apr 2025</p>
                                    </div>
                                </div>
                                <div class="button-area">
                                    <a href="find-jobs-details.html" class="primary-btn-4">Apply Now
                                        <svg width="12" height="12" viewBox="0 0 12 12"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.8125 0.750301C10.9285 0.750301 11.0398 0.796394 11.1219 0.878442C11.2039 0.960489 11.25 1.07177 11.25 1.1878L11.25 6.4378C11.25 6.55383 11.2039 6.66511 11.1219 6.74716C11.0398 6.82921 10.9285 6.8753 10.8125 6.8753C10.6965 6.8753 10.5852 6.82921 10.5031 6.74716C10.4211 6.66511 10.375 6.55383 10.375 6.4378L10.375 2.24393L1.49725 11.1226C1.45657 11.1632 1.40828 11.1955 1.35514 11.2175C1.30199 11.2395 1.24503 11.2509 1.1875 11.2509C1.12997 11.2509 1.07301 11.2395 1.01986 11.2175C0.966718 11.1955 0.918427 11.1632 0.87775 11.1226C0.837073 11.0819 0.804806 11.0336 0.782792 10.9804C0.760777 10.9273 0.749448 10.8703 0.749448 10.8128C0.749448 10.7553 0.760777 10.6983 0.782792 10.6452C0.804806 10.592 0.837073 10.5437 0.87775 10.5031L9.75637 1.6253L5.5625 1.6253C5.44647 1.6253 5.33519 1.57921 5.25314 1.49716C5.17109 1.41511 5.125 1.30383 5.125 1.1878C5.125 1.07177 5.17109 0.96049 5.25314 0.878442C5.33519 0.796395 5.44647 0.750302 5.5625 0.750302L10.8125 0.750301Z" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 item">
                            <div class="feature-card two">
                                <div class="feature-wrape">
                                    <div class="company-area">
                                        <div class="logo">
                                            <img src="assets/image/company-logo5.png" alt="">
                                        </div>
                                        <div class="company-details">
                                            <div class="name-location">
                                                <h5>Frontend Developer</h5>
                                                <p>By <span>TechNova Solutions</span> in Web Development</p>
                                            </div>
                                            <div class="bookmark">
                                                <i class="bi bi-heart"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-discription">
                                        <ul>
                                            <li>
                                                On-Site
                                            </li>
                                            <li class="style-2">
                                                <svg width="10" height="10" viewBox="0 0 10 10"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.60375 5.5875C7.27625 6.25125 6.8325 6.9125 6.37875 7.50625C5.94784 8.06563 5.48762 8.6018 5 9.1125C4.51238 8.6018 4.05216 8.06563 3.62125 7.50625C3.1675 6.9125 2.72375 6.25125 2.39625 5.5875C2.065 4.91687 1.875 4.28875 1.875 3.75C1.875 2.9212 2.20424 2.12634 2.79029 1.54029C3.37634 0.95424 4.1712 0.625 5 0.625C5.8288 0.625 6.62366 0.95424 7.20971 1.54029C7.79576 2.12634 8.125 2.9212 8.125 3.75C8.125 4.28875 7.93438 4.91687 7.60375 5.5875ZM5 10C5 10 8.75 6.44625 8.75 3.75C8.75 2.75544 8.35491 1.80161 7.65165 1.09835C6.94839 0.395088 5.99456 0 5 0C4.00544 0 3.05161 0.395088 2.34835 1.09835C1.64509 1.80161 1.25 2.75544 1.25 3.75C1.25 6.44625 5 10 5 10Z" />
                                                    <path
                                                        d="M5 5C4.66848 5 4.35054 4.8683 4.11612 4.63388C3.8817 4.39946 3.75 4.08152 3.75 3.75C3.75 3.41848 3.8817 3.10054 4.11612 2.86612C4.35054 2.6317 4.66848 2.5 5 2.5C5.33152 2.5 5.64946 2.6317 5.88388 2.86612C6.1183 3.10054 6.25 3.41848 6.25 3.75C6.25 4.08152 6.1183 4.39946 5.88388 4.63388C5.64946 4.8683 5.33152 5 5 5ZM5 5.625C5.49728 5.625 5.97419 5.42746 6.32583 5.07583C6.67746 4.72419 6.875 4.24728 6.875 3.75C6.875 3.25272 6.67746 2.77581 6.32583 2.42417C5.97419 2.07254 5.49728 1.875 5 1.875C4.50272 1.875 4.02581 2.07254 3.67417 2.42417C3.32254 2.77581 3.125 3.25272 3.125 3.75C3.125 4.24728 3.32254 4.72419 3.67417 5.07583C4.02581 5.42746 4.50272 5.625 5 5.625Z" />
                                                </svg>
                                                New York City
                                            </li>
                                            <li class="style-3">
                                                $25k-30k
                                            </li>
                                            <li class="style-4">
                                                Full-Time
                                            </li>
                                            <li class="style-5">
                                                Vacancy: 01
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="date">
                                        <p>Deadline: 30 Apr 2025</p>
                                    </div>
                                </div>
                                <div class="button-area">
                                    <a href="find-jobs-details.html" class="primary-btn-4">Apply Now
                                        <svg width="12" height="12" viewBox="0 0 12 12"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.8125 0.750301C10.9285 0.750301 11.0398 0.796394 11.1219 0.878442C11.2039 0.960489 11.25 1.07177 11.25 1.1878L11.25 6.4378C11.25 6.55383 11.2039 6.66511 11.1219 6.74716C11.0398 6.82921 10.9285 6.8753 10.8125 6.8753C10.6965 6.8753 10.5852 6.82921 10.5031 6.74716C10.4211 6.66511 10.375 6.55383 10.375 6.4378L10.375 2.24393L1.49725 11.1226C1.45657 11.1632 1.40828 11.1955 1.35514 11.2175C1.30199 11.2395 1.24503 11.2509 1.1875 11.2509C1.12997 11.2509 1.07301 11.2395 1.01986 11.2175C0.966718 11.1955 0.918427 11.1632 0.87775 11.1226C0.837073 11.0819 0.804806 11.0336 0.782792 10.9804C0.760777 10.9273 0.749448 10.8703 0.749448 10.8128C0.749448 10.7553 0.760777 10.6983 0.782792 10.6452C0.804806 10.592 0.837073 10.5437 0.87775 10.5031L9.75637 1.6253L5.5625 1.6253C5.44647 1.6253 5.33519 1.57921 5.25314 1.49716C5.17109 1.41511 5.125 1.30383 5.125 1.1878C5.125 1.07177 5.17109 0.96049 5.25314 0.878442C5.33519 0.796395 5.44647 0.750302 5.5625 0.750302L10.8125 0.750301Z" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 item">
                            <div class="feature-card two">
                                <div class="feature-wrape">
                                    <div class="company-area">
                                        <div class="logo">
                                            <img src="assets/image/company-logo6.png" alt="">
                                        </div>
                                        <div class="company-details">
                                            <div class="name-location">
                                                <h5>Frontend Developer</h5>
                                                <p>By <span>TechNova Solutions</span> in Web Development</p>
                                            </div>
                                            <div class="bookmark">
                                                <i class="bi bi-heart"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-discription">
                                        <ul>
                                            <li>
                                                On-Site
                                            </li>
                                            <li class="style-2">
                                                <svg width="10" height="10" viewBox="0 0 10 10"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.60375 5.5875C7.27625 6.25125 6.8325 6.9125 6.37875 7.50625C5.94784 8.06563 5.48762 8.6018 5 9.1125C4.51238 8.6018 4.05216 8.06563 3.62125 7.50625C3.1675 6.9125 2.72375 6.25125 2.39625 5.5875C2.065 4.91687 1.875 4.28875 1.875 3.75C1.875 2.9212 2.20424 2.12634 2.79029 1.54029C3.37634 0.95424 4.1712 0.625 5 0.625C5.8288 0.625 6.62366 0.95424 7.20971 1.54029C7.79576 2.12634 8.125 2.9212 8.125 3.75C8.125 4.28875 7.93438 4.91687 7.60375 5.5875ZM5 10C5 10 8.75 6.44625 8.75 3.75C8.75 2.75544 8.35491 1.80161 7.65165 1.09835C6.94839 0.395088 5.99456 0 5 0C4.00544 0 3.05161 0.395088 2.34835 1.09835C1.64509 1.80161 1.25 2.75544 1.25 3.75C1.25 6.44625 5 10 5 10Z" />
                                                    <path
                                                        d="M5 5C4.66848 5 4.35054 4.8683 4.11612 4.63388C3.8817 4.39946 3.75 4.08152 3.75 3.75C3.75 3.41848 3.8817 3.10054 4.11612 2.86612C4.35054 2.6317 4.66848 2.5 5 2.5C5.33152 2.5 5.64946 2.6317 5.88388 2.86612C6.1183 3.10054 6.25 3.41848 6.25 3.75C6.25 4.08152 6.1183 4.39946 5.88388 4.63388C5.64946 4.8683 5.33152 5 5 5ZM5 5.625C5.49728 5.625 5.97419 5.42746 6.32583 5.07583C6.67746 4.72419 6.875 4.24728 6.875 3.75C6.875 3.25272 6.67746 2.77581 6.32583 2.42417C5.97419 2.07254 5.49728 1.875 5 1.875C4.50272 1.875 4.02581 2.07254 3.67417 2.42417C3.32254 2.77581 3.125 3.25272 3.125 3.75C3.125 4.24728 3.32254 4.72419 3.67417 5.07583C4.02581 5.42746 4.50272 5.625 5 5.625Z" />
                                                </svg>
                                                New York City
                                            </li>
                                            <li class="style-3">
                                                $25k-30k
                                            </li>
                                            <li class="style-4">
                                                Full-Time
                                            </li>
                                            <li class="style-5">
                                                Vacancy: 01
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="date">
                                        <p>Deadline: 30 Apr 2025</p>
                                    </div>
                                </div>
                                <div class="button-area">
                                    <a href="find-jobs-details.html" class="primary-btn-4">Apply Now
                                        <svg width="12" height="12" viewBox="0 0 12 12"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.8125 0.750301C10.9285 0.750301 11.0398 0.796394 11.1219 0.878442C11.2039 0.960489 11.25 1.07177 11.25 1.1878L11.25 6.4378C11.25 6.55383 11.2039 6.66511 11.1219 6.74716C11.0398 6.82921 10.9285 6.8753 10.8125 6.8753C10.6965 6.8753 10.5852 6.82921 10.5031 6.74716C10.4211 6.66511 10.375 6.55383 10.375 6.4378L10.375 2.24393L1.49725 11.1226C1.45657 11.1632 1.40828 11.1955 1.35514 11.2175C1.30199 11.2395 1.24503 11.2509 1.1875 11.2509C1.12997 11.2509 1.07301 11.2395 1.01986 11.2175C0.966718 11.1955 0.918427 11.1632 0.87775 11.1226C0.837073 11.0819 0.804806 11.0336 0.782792 10.9804C0.760777 10.9273 0.749448 10.8703 0.749448 10.8128C0.749448 10.7553 0.760777 10.6983 0.782792 10.6452C0.804806 10.592 0.837073 10.5437 0.87775 10.5031L9.75637 1.6253L5.5625 1.6253C5.44647 1.6253 5.33519 1.57921 5.25314 1.49716C5.17109 1.41511 5.125 1.30383 5.125 1.1878C5.125 1.07177 5.17109 0.96049 5.25314 0.878442C5.33519 0.796395 5.44647 0.750302 5.5625 0.750302L10.8125 0.750301Z" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 item">
                            <div class="feature-card two">
                                <div class="feature-wrape">
                                    <div class="company-area">
                                        <div class="logo">
                                            <img src="assets/image/company-logo7.png" alt="">
                                        </div>
                                        <div class="company-details">
                                            <div class="name-location">
                                                <h5>Digital Marketing Executive</h5>
                                                <p>By <span>MarketHive Media</span> in Sale & Marketing </p>
                                            </div>
                                            <div class="bookmark">
                                                <i class="bi bi-heart"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-discription">
                                        <ul>
                                            <li>
                                                On-Site
                                            </li>
                                            <li class="style-2">
                                                <svg width="10" height="10" viewBox="0 0 10 10"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.60375 5.5875C7.27625 6.25125 6.8325 6.9125 6.37875 7.50625C5.94784 8.06563 5.48762 8.6018 5 9.1125C4.51238 8.6018 4.05216 8.06563 3.62125 7.50625C3.1675 6.9125 2.72375 6.25125 2.39625 5.5875C2.065 4.91687 1.875 4.28875 1.875 3.75C1.875 2.9212 2.20424 2.12634 2.79029 1.54029C3.37634 0.95424 4.1712 0.625 5 0.625C5.8288 0.625 6.62366 0.95424 7.20971 1.54029C7.79576 2.12634 8.125 2.9212 8.125 3.75C8.125 4.28875 7.93438 4.91687 7.60375 5.5875ZM5 10C5 10 8.75 6.44625 8.75 3.75C8.75 2.75544 8.35491 1.80161 7.65165 1.09835C6.94839 0.395088 5.99456 0 5 0C4.00544 0 3.05161 0.395088 2.34835 1.09835C1.64509 1.80161 1.25 2.75544 1.25 3.75C1.25 6.44625 5 10 5 10Z" />
                                                    <path
                                                        d="M5 5C4.66848 5 4.35054 4.8683 4.11612 4.63388C3.8817 4.39946 3.75 4.08152 3.75 3.75C3.75 3.41848 3.8817 3.10054 4.11612 2.86612C4.35054 2.6317 4.66848 2.5 5 2.5C5.33152 2.5 5.64946 2.6317 5.88388 2.86612C6.1183 3.10054 6.25 3.41848 6.25 3.75C6.25 4.08152 6.1183 4.39946 5.88388 4.63388C5.64946 4.8683 5.33152 5 5 5ZM5 5.625C5.49728 5.625 5.97419 5.42746 6.32583 5.07583C6.67746 4.72419 6.875 4.24728 6.875 3.75C6.875 3.25272 6.67746 2.77581 6.32583 2.42417C5.97419 2.07254 5.49728 1.875 5 1.875C4.50272 1.875 4.02581 2.07254 3.67417 2.42417C3.32254 2.77581 3.125 3.25272 3.125 3.75C3.125 4.24728 3.32254 4.72419 3.67417 5.07583C4.02581 5.42746 4.50272 5.625 5 5.625Z" />
                                                </svg>
                                                New York City
                                            </li>
                                            <li class="style-3">
                                                $25k-30k
                                            </li>
                                            <li class="style-4">
                                                Full-Time
                                            </li>
                                            <li class="style-5">
                                                Vacancy: 01
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="date">
                                        <p>Deadline: 30 Apr 2025</p>
                                    </div>
                                </div>
                                <div class="button-area">
                                    <a href="find-jobs-details.html" class="primary-btn-4">Apply Now
                                        <svg width="12" height="12" viewBox="0 0 12 12"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.8125 0.750301C10.9285 0.750301 11.0398 0.796394 11.1219 0.878442C11.2039 0.960489 11.25 1.07177 11.25 1.1878L11.25 6.4378C11.25 6.55383 11.2039 6.66511 11.1219 6.74716C11.0398 6.82921 10.9285 6.8753 10.8125 6.8753C10.6965 6.8753 10.5852 6.82921 10.5031 6.74716C10.4211 6.66511 10.375 6.55383 10.375 6.4378L10.375 2.24393L1.49725 11.1226C1.45657 11.1632 1.40828 11.1955 1.35514 11.2175C1.30199 11.2395 1.24503 11.2509 1.1875 11.2509C1.12997 11.2509 1.07301 11.2395 1.01986 11.2175C0.966718 11.1955 0.918427 11.1632 0.87775 11.1226C0.837073 11.0819 0.804806 11.0336 0.782792 10.9804C0.760777 10.9273 0.749448 10.8703 0.749448 10.8128C0.749448 10.7553 0.760777 10.6983 0.782792 10.6452C0.804806 10.592 0.837073 10.5437 0.87775 10.5031L9.75637 1.6253L5.5625 1.6253C5.44647 1.6253 5.33519 1.57921 5.25314 1.49716C5.17109 1.41511 5.125 1.30383 5.125 1.1878C5.125 1.07177 5.17109 0.96049 5.25314 0.878442C5.33519 0.796395 5.44647 0.750302 5.5625 0.750302L10.8125 0.750301Z" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 item">
                            <div class="feature-card two">
                                <div class="feature-wrape">
                                    <div class="company-area">
                                        <div class="logo">
                                            <img src="assets/image/company-logo8.png" alt="">
                                        </div>
                                        <div class="company-details">
                                            <div class="name-location">
                                                <h5>Human Resource Manager</h5>
                                                <p>By <span>CorePeople Pvt. Ltd.</span> in HR Department</p>
                                            </div>
                                            <div class="bookmark">
                                                <i class="bi bi-heart"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-discription">
                                        <ul>
                                            <li>
                                                On-Site
                                            </li>
                                            <li class="style-2">
                                                <svg width="10" height="10" viewBox="0 0 10 10"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M7.60375 5.5875C7.27625 6.25125 6.8325 6.9125 6.37875 7.50625C5.94784 8.06563 5.48762 8.6018 5 9.1125C4.51238 8.6018 4.05216 8.06563 3.62125 7.50625C3.1675 6.9125 2.72375 6.25125 2.39625 5.5875C2.065 4.91687 1.875 4.28875 1.875 3.75C1.875 2.9212 2.20424 2.12634 2.79029 1.54029C3.37634 0.95424 4.1712 0.625 5 0.625C5.8288 0.625 6.62366 0.95424 7.20971 1.54029C7.79576 2.12634 8.125 2.9212 8.125 3.75C8.125 4.28875 7.93438 4.91687 7.60375 5.5875ZM5 10C5 10 8.75 6.44625 8.75 3.75C8.75 2.75544 8.35491 1.80161 7.65165 1.09835C6.94839 0.395088 5.99456 0 5 0C4.00544 0 3.05161 0.395088 2.34835 1.09835C1.64509 1.80161 1.25 2.75544 1.25 3.75C1.25 6.44625 5 10 5 10Z" />
                                                    <path
                                                        d="M5 5C4.66848 5 4.35054 4.8683 4.11612 4.63388C3.8817 4.39946 3.75 4.08152 3.75 3.75C3.75 3.41848 3.8817 3.10054 4.11612 2.86612C4.35054 2.6317 4.66848 2.5 5 2.5C5.33152 2.5 5.64946 2.6317 5.88388 2.86612C6.1183 3.10054 6.25 3.41848 6.25 3.75C6.25 4.08152 6.1183 4.39946 5.88388 4.63388C5.64946 4.8683 5.33152 5 5 5ZM5 5.625C5.49728 5.625 5.97419 5.42746 6.32583 5.07583C6.67746 4.72419 6.875 4.24728 6.875 3.75C6.875 3.25272 6.67746 2.77581 6.32583 2.42417C5.97419 2.07254 5.49728 1.875 5 1.875C4.50272 1.875 4.02581 2.07254 3.67417 2.42417C3.32254 2.77581 3.125 3.25272 3.125 3.75C3.125 4.24728 3.32254 4.72419 3.67417 5.07583C4.02581 5.42746 4.50272 5.625 5 5.625Z" />
                                                </svg>
                                                New York City
                                            </li>
                                            <li class="style-3">
                                                $25k-30k
                                            </li>
                                            <li class="style-4">
                                                Full-Time
                                            </li>
                                            <li class="style-5">
                                                Vacancy: 01
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="date">
                                        <p>Deadline: 30 Apr 2025</p>
                                    </div>
                                </div>
                                <div class="button-area">
                                    <a href="find-jobs-details.html" class="primary-btn-4">Apply Now
                                        <svg width="12" height="12" viewBox="0 0 12 12"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10.8125 0.750301C10.9285 0.750301 11.0398 0.796394 11.1219 0.878442C11.2039 0.960489 11.25 1.07177 11.25 1.1878L11.25 6.4378C11.25 6.55383 11.2039 6.66511 11.1219 6.74716C11.0398 6.82921 10.9285 6.8753 10.8125 6.8753C10.6965 6.8753 10.5852 6.82921 10.5031 6.74716C10.4211 6.66511 10.375 6.55383 10.375 6.4378L10.375 2.24393L1.49725 11.1226C1.45657 11.1632 1.40828 11.1955 1.35514 11.2175C1.30199 11.2395 1.24503 11.2509 1.1875 11.2509C1.12997 11.2509 1.07301 11.2395 1.01986 11.2175C0.966718 11.1955 0.918427 11.1632 0.87775 11.1226C0.837073 11.0819 0.804806 11.0336 0.782792 10.9804C0.760777 10.9273 0.749448 10.8703 0.749448 10.8128C0.749448 10.7553 0.760777 10.6983 0.782792 10.6452C0.804806 10.592 0.837073 10.5437 0.87775 10.5031L9.75637 1.6253L5.5625 1.6253C5.44647 1.6253 5.33519 1.57921 5.25314 1.49716C5.17109 1.41511 5.125 1.30383 5.125 1.1878C5.125 1.07177 5.17109 0.96049 5.25314 0.878442C5.33519 0.796395 5.44647 0.750302 5.5625 0.750302L10.8125 0.750301Z" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 d-flex justify-content-center">
                            <div class="pagination-area">
                                <ul class="pagination">
                                    <li class="arrow">
                                        <a href="#">
                                            <svg width="8" height="12" viewBox="0 0 8 12"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M0.666658 6.00952C0.666005 5.8966 0.687648 5.78467 0.730347 5.68013C0.773045 5.57559 0.835958 5.48051 0.915478 5.40034L6.06349 0.252331C6.22505 0.0907662 6.44418 -4.76454e-08 6.67267 -3.76579e-08C6.90115 -2.76704e-08 7.12028 0.0907662 7.28185 0.252331C7.44341 0.413896 7.53418 0.633025 7.53418 0.861512C7.53418 1.09 7.44341 1.30913 7.28185 1.47069L2.73444 6.00952L7.27327 10.5484C7.41383 10.7125 7.48728 10.9236 7.47894 11.1396C7.4706 11.3555 7.38108 11.5603 7.22828 11.7131C7.07547 11.8659 6.87063 11.9555 6.6547 11.9638C6.43876 11.9721 6.22762 11.8987 6.06349 11.7581L0.915478 6.61012C0.756967 6.45031 0.667606 6.23461 0.666658 6.00952Z" />
                                            </svg>
                                        </a>
                                    </li>
                                    <li class="page-item active">
                                        <a href="#">1</a>
                                    </li>
                                    <li class="page-item">
                                        <a href="#">2</a>
                                    </li>
                                    <li class="page-item">
                                        <a href="#">3</a>
                                    </li>
                                    <li class="page-item">
                                        <a href="#">4</a>
                                    </li>
                                    <li class="page-item">
                                        <a href="#">
                                            <svg width="13" height="4" viewBox="0 0 13 4"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M1.86293 3.125C1.51823 3.125 1.22277 3.00379 0.976562 2.76136C0.73035 2.51894 0.609138 2.22348 0.612926 1.875C0.609138 1.53409 0.73035 1.24242 0.976562 1C1.22277 0.757576 1.51823 0.636364 1.86293 0.636364C2.19626 0.636364 2.48603 0.757576 2.73224 1C2.98224 1.24242 3.10914 1.53409 3.11293 1.875C3.10914 2.10606 3.04853 2.31629 2.93111 2.50568C2.81747 2.69508 2.66596 2.84659 2.47656 2.96023C2.29096 3.07008 2.08641 3.125 1.86293 3.125ZM6.50355 3.125C6.15885 3.125 5.8634 3.00379 5.61719 2.76136C5.37098 2.51894 5.24976 2.22348 5.25355 1.875C5.24976 1.53409 5.37098 1.24242 5.61719 1C5.8634 0.757576 6.15885 0.636364 6.50355 0.636364C6.83688 0.636364 7.12666 0.757576 7.37287 1C7.62287 1.24242 7.74976 1.53409 7.75355 1.875C7.74976 2.10606 7.68916 2.31629 7.57173 2.50568C7.4581 2.69508 7.30658 2.84659 7.11719 2.96023C6.93158 3.07008 6.72704 3.125 6.50355 3.125ZM11.1442 3.125C10.7995 3.125 10.504 3.00379 10.2578 2.76136C10.0116 2.51894 9.89039 2.22348 9.89418 1.875C9.89039 1.53409 10.0116 1.24242 10.2578 1C10.504 0.757576 10.7995 0.636364 11.1442 0.636364C11.4775 0.636364 11.7673 0.757576 12.0135 1C12.2635 1.24242 12.3904 1.53409 12.3942 1.875C12.3904 2.10606 12.3298 2.31629 12.2124 2.50568C12.0987 2.69508 11.9472 2.84659 11.7578 2.96023C11.5722 3.07008 11.3677 3.125 11.1442 3.125Z" />
                                            </svg>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a href="#">10</a>
                                    </li>
                                    <li class="arrow">
                                        <a href="#">
                                            <svg width="8" height="12" viewBox="0 0 8 12"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M7.33334 6.00952C7.33399 5.8966 7.31235 5.78467 7.26965 5.68013C7.22696 5.57559 7.16404 5.48051 7.08452 5.40034L1.93651 0.252331C1.77495 0.0907662 1.55582 -4.76454e-08 1.32733 -3.76579e-08C1.09885 -2.76704e-08 0.879716 0.0907662 0.718151 0.252331C0.556587 0.413896 0.46582 0.633025 0.46582 0.861512C0.46582 1.09 0.556587 1.30913 0.718151 1.47069L5.26556 6.00952L0.726732 10.5484C0.586168 10.7125 0.512718 10.9236 0.521059 11.1396C0.529399 11.3555 0.618917 11.5603 0.771722 11.7131C0.924528 11.8659 1.12937 11.9555 1.3453 11.9638C1.56124 11.9721 1.77238 11.8987 1.93651 11.7581L7.08452 6.61012C7.24303 6.45031 7.33239 6.23461 7.33334 6.00952Z" />
                                            </svg>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



@push('post_scripts')
    <script src="{{ asset('frontend/assets/js/range-slider.js') }}"></script>
    @include('js.front.job-search')
@endpush
