<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class CheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            //

            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'required|email|unique:users,id,'.Request()->id,
            'password' => Request()->id ? '' : 'required|min:8',
            'password_confirmation' => Request()->id ? '' : 'required|min:8|same:password',
            //company info
            'company_name' => 'required',
            'company_type_id' => 'required',
            'company_size' => 'required',
            'company_location' => 'required',
            'company_website' => 'required',
            'company_details' => 'required',

            'card_number' => Request()->method == 'stripe' ? 'required' : '',
            'cvc' => Request()->method == 'stripe' ? 'required' : '',
            'expiry' => Request()->method == 'stripe' ? 'required' : '',

        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [

            'first_name.required' => 'First Name Field is required',
            'last_name.required' => 'Last Name Field is required',
            'email.required' => 'Email Field is required',
            'password.required' => 'Password Field is required',
            'password_confirmation.required' => 'Confirm Password Field is required',
            'password_confirmation.same' => 'Password Confirmation should match the Password',
            'email.unique' => 'A user with this email address already exists.',

            'company_name.required' => 'Company Name Field is required',
            'company_size.required' => 'Comapny Size Field is required',
            'company_type_id.required' => 'Comapny type Field is required',
            'company_website.required' => 'Company Website Field is required',
            'company_location.required' => 'Company Location Field is required',
            'company_details.required' => 'Company Details Field is required',
            'card_number.required' => 'Card Number Field is required',
            'cvc.required' => 'CVC is required',
            'expiry.required' => 'Expiry is required',

        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => false,
            'errors' => $validator->errors(),
        ]));
    }
}
