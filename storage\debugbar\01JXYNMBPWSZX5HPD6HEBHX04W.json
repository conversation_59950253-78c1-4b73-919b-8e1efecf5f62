{"__meta": {"id": "01JXYNMBPWSZX5HPD6HEBHX04W", "datetime": "2025-06-17 10:05:51", "utime": **********.709043, "method": "GET", "uri": "/admin/education?draw=3&columns%5B0%5D%5Bdata%5D=Id&columns%5B0%5D%5Bname%5D=Id&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=name&columns%5B1%5D%5Bname%5D=name&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=Status&columns%5B2%5D%5Bname%5D=Status&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=&columns%5B3%5D%5Bname%5D=&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=action&columns%5B4%5D%5Bname%5D=action&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=false&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=0&order%5B0%5D%5Bdir%5D=desc&start=20&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&_=1750154740311", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.346121, "end": **********.709058, "duration": 0.36293697357177734, "duration_str": "363ms", "measures": [{"label": "Booting", "start": **********.346121, "relative_start": 0, "end": **********.564112, "relative_end": **********.564112, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.564136, "relative_start": 0.*****************, "end": **********.709059, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.573702, "relative_start": 0.*****************, "end": **********.577765, "relative_end": **********.577765, "duration": 0.0040628910064697266, "duration_str": "4.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.70647, "relative_start": 0.****************, "end": **********.706902, "relative_end": **********.706902, "duration": 0.00043201446533203125, "duration_str": "432μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/education", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "controller": "App\\Http\\Controllers\\Backend\\EducationController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FEducationController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.education", "prefix": "admin/education", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FEducationController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/EducationController.php:20-27</a>"}, "queries": {"count": 53, "nb_statements": 53, "nb_visible_statements": 53, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026270000000000016, "accumulated_duration_str": "26.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.589551, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 1.941}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": "middleware", "name": "prevent-back-history", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Middleware\\PreventBackHistory.php", "line": 19}], "start": **********.597866, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 1.941, "width_percent": 2.436}, {"sql": "select * from `education_lavels` order by `id` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 21}, {"index": 16, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 32}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/EducationController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\EducationController.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6006389, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "EducationRepostitory.php:21", "source": {"index": 15, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FAdmin%2FEducationRepostitory.php&line=21", "ajax": false, "filename": "EducationRepostitory.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 4.378, "width_percent": 2.094}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 49 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [49], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.6094902, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 6.471, "width_percent": 1.865}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.612087, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 8.337, "width_percent": 2.703}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 48 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.615005, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 11.039, "width_percent": 2.17}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6167889, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 13.209, "width_percent": 2.208}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 47 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.619404, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 15.417, "width_percent": 1.256}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.620719, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 16.673, "width_percent": 1.218}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 46 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.622653, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 17.891, "width_percent": 1.218}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.623964, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 19.109, "width_percent": 1.294}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 45 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.626001, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 20.404, "width_percent": 1.561}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.627867, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 21.964, "width_percent": 2.436}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 43 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.6302378, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 24.4, "width_percent": 1.523}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.631586, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 25.923, "width_percent": 1.865}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 41 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.6336122, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 27.788, "width_percent": 1.827}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6350482, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 29.616, "width_percent": 1.865}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 40 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.637079, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 31.481, "width_percent": 1.789}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6384962, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 33.27, "width_percent": 1.789}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 32 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.6404982, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 35.059, "width_percent": 1.827}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6419191, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 36.886, "width_percent": 1.827}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 31 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.644542, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 38.713, "width_percent": 2.512}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.646475, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 41.226, "width_percent": 2.056}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 30 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.648636, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 43.281, "width_percent": 1.903}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.65008, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 45.185, "width_percent": 1.827}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 29 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.652092, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 47.012, "width_percent": 1.827}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6535132, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 48.839, "width_percent": 1.789}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 28 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.655502, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 50.628, "width_percent": 1.789}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.656909, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 52.417, "width_percent": 1.827}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 23 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.658911, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 54.244, "width_percent": 1.865}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6607041, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 56.11, "width_percent": 1.865}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 22 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.6632679, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 57.975, "width_percent": 2.208}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.664843, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 60.183, "width_percent": 1.827}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 21 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.666894, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 62.01, "width_percent": 1.903}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.668397, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 63.913, "width_percent": 1.827}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 20 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.670429, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 65.74, "width_percent": 1.789}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.671842, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 67.53, "width_percent": 1.789}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 19 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.673842, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 69.319, "width_percent": 1.865}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6753101, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 71.184, "width_percent": 1.827}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 18 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.6777241, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 73.011, "width_percent": 2.512}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.67973, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 75.523, "width_percent": 2.056}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 17 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.681905, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.579, "width_percent": 1.941}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.683387, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 79.52, "width_percent": 1.865}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 16 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.685452, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 81.386, "width_percent": 1.903}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.686926, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 83.289, "width_percent": 1.637}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 8 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.688884, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 84.926, "width_percent": 1.865}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.6903121, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 86.791, "width_percent": 1.751}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 6 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.6922748, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 88.542, "width_percent": 1.637}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.69368, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 90.179, "width_percent": 2.17}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 4 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.696419, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 92.349, "width_percent": 2.132}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.697995, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 94.48, "width_percent": 1.865}, {"sql": "select * from `education_lavel_translations` where `education_lavel_translations`.`education_lavel_id` = 1 and `education_lavel_translations`.`education_lavel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, {"index": 21, "namespace": null, "name": "app/Repositories/Admin/EducationRepostitory.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\Admin\\EducationRepostitory.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.700033, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EducationLavel.php:21", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/EducationLavel.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\EducationLavel.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=21", "ajax": false, "filename": "EducationLavel.php", "line": "21"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 96.346, "width_percent": 1.865}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.7014952, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Helper.php:324", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 324}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=324", "ajax": false, "filename": "Helper.php", "line": "324"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 98.211, "width_percent": 1.789}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 125, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin\\EducationLavel": {"value": 25, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavel.php&line=1", "ajax": false, "filename": "EducationLavel.php", "line": "?"}}, "App\\Models\\Admin\\EducationLavelTranslation": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FEducationLavelTranslation.php&line=1", "ajax": false, "filename": "EducationLavelTranslation.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 159, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/lang/flags/en.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/theme-option\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/education?_=1750154740311&columns%5B0%5D%5Bdata%5D=Id&columns%5B0%5D%5Bn...", "action_name": "admin.education", "controller_action": "App\\Http\\Controllers\\Backend\\EducationController@index", "uri": "GET admin/education", "controller": "App\\Http\\Controllers\\Backend\\EducationController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FEducationController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/education", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FEducationController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/EducationController.php:20-27</a>", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "duration": "369ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1518122947 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">Id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">Id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1750154740311</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518122947\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2079778955 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2079778955\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1682716451 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/education</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6Im5xRHZoakw1WWhuV1FSL0lYZHM0b1E9PSIsInZhbHVlIjoiTXZUWVdpYWNtSEVUL3BDNlpJelphYkE3ckt6TVF5ZWorQ3BYVm1FVkR3MVlHN1hJNVNUckNGWXJVckRVdEVHUDhmODZUUEpVNUk5VkYrdUt0QzRYdEttWUZvNjNyeS95azRhVDBIR002TUxkWGpacEZ1T2JWb0tEcHEyc294OVYiLCJtYWMiOiI1MDI4OTMxZWY3MjRkZGExNmJjYThmY2RhODNiMmM5OGE2MjIxYjQ3ZGM3NjhlODlhYWEwNjBkZjgwZDI4ZmJiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Iks2cGRlcjBQK01jdnQ4ZVRtNVFZWnc9PSIsInZhbHVlIjoiVnorcy9ZazJPc05tTWphNnU3TkRuMENqY1NsSmZ2aWRlMGFMNUNrckU0Q0FIMjFlRTdSdkh0OEc2VUhRZWNPdmJMNGIyL0I3VVZtRVNrem5IUGh4dW9UVXhoaDIwdXlZeWFZaCtTTkFwalhMdXZvdkpQdTFJTVBla3lTWm1pTHgiLCJtYWMiOiJkODRlOTI3NmI5YTZkNmQ5NWY1YzIxYWI3MDU0ZDI5MWJjYzhhOTdkM2ViYzJhNTQ3ZWRkNmY3YTdmYzVlYjA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682716451\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-43559119 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DuRe3UYDshvVdLe1lxmS8WfvwPktcikZxAETD6MP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43559119\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1611786549 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 10:05:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611786549\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1772419941 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/storage/lang/flags/en.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/theme-option</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772419941\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/education?_=1750154740311&columns%5B0%5D%5Bdata%5D=Id&columns%5B0%5D%5Bn...", "action_name": "admin.education", "controller_action": "App\\Http\\Controllers\\Backend\\EducationController@index"}, "badge": null}}