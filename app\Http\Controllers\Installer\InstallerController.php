<?php

namespace App\Http\Controllers\Installer;

use Illuminate\Http\Request;
use App\Models\PurchaseVerify;
use App\Classes\DatabaseManager;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\DB;
use App\Classes\EnvironmentManager;
use App\Classes\PermissionsChecker;
use App\Classes\FinalInstallManager;
use App\Classes\RequirementsChecker;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\File;
use App\Classes\InstalledFileManager;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class InstallerController extends Controller
{
    protected $requirements;

    protected $permissions;

    protected $environmentManager;

    /* Minimum PHP Version Supported (Override is in installer.php config file).
    *
    * @var _minPhpVersion
    */
    private $databaseManager;

    public function __construct(
        DatabaseManager $databaseManager,
        RequirementsChecker $checker,
        PermissionsChecker $pchecker,
        EnvironmentManager $environmentManager
    ) {

        $this->databaseManager = $databaseManager;
        $this->requirements = $checker;
        $this->permissions = $pchecker;
        $this->environmentManager = $environmentManager;
    }

    /**
     * installContent
     */
    public function installContent()
    {
        $phpSupportInfo = $this->requirements->checkPHPversion(config('installer.core.minPhpVersion'));
        $requirements = $this->requirements->check(config('installer.requirements'));
        $permissions = $this->permissions->check(config('installer.permissions'));
        return view('installer.index', compact('phpSupportInfo', 'requirements', 'permissions'));
    }

    /**
     * requirement
     *
     * @param  mixed  $request
     */
    public function requirement(Request $request)
    {
        $phpSupportInfo = $this->requirements->checkPHPversion(config('installer.core.minPhpVersion'));
        $requirements = $this->requirements->check(config('installer.requirements'));
        $permissions = $this->permissions->check(config('installer.permissions'));
        if ($request->ajax()) {

            if (isset($requirements['errors']) || isset($phpSupportInfo['supported']) !== true) {
                return response()->json([
                    'status' => false,
                    'message' => 'Please make sure Server Requirements'
                ]);
            } else {
                return response()->json([
                    'status' => true,
                    'url' => route('install.permission')
                ]);
            }
        }

        return view('installer.index', compact('phpSupportInfo', 'requirements', 'permissions'));
    }

    /**
     * permission
     *
     * @param  mixed  $request
     */
    public function permissionCheck(Request $request)
    {
        $phpSupportInfo = $this->requirements->checkPHPversion(config('installer.core.minPhpVersion'));
        $requirements = $this->requirements->check(config('installer.requirements'));
        $permissions = $this->permissions->check(config('installer.permissions'));
        if ($request->ajax()) {
            if (isset($permissions['errors']) == true) {
                return response()->json([
                    'status' => false,
                    'message' => 'Please make sure folder Permissions'
                ]);
            } else {
                return response()->json([
                    'status' => true,
                    'url' => route('install.environment.form')
                ]);
            }
        }

        return view('installer.index', compact('phpSupportInfo', 'requirements', 'permissions'));
    }
    public function environmentForm()
    {
        return view('installer.index');
    }

    /**
     * environment
     *
     * @param  mixed  $request
     */
    public function environment(Request $request)
    {
        if ($request->ajax()) {
            $validator = Validator::make(
                $request->all(),
                [
                    'app_name' => 'required',
                    'app_debug' => 'required',
                    'environment' => 'required',
                    'app_log_level' => 'required',
                    'app_url' => 'required',
                ]
            );

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors()
                ]);
            }
            Session::put('environment', $request->all());

            return response()->json([
                'status' => true,
                'url' => route('install.database.form')
            ]);
        }

        return redirect(404);
    }
    public function licenseForm()
    {
        return view('installer.index');
    }
    public function databaseForm()
    {
        return view('installer.index');
    }
    /**
     * database
     *
     * @param  mixed  $request
     */
    public function database(Request $request)
    {
        $request->merge(Session::get('environment'));
        try {
            $rules = config('installer.environment.form.rules');
            $validator = Validator::make($request->all(), $rules);
            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors()
                ]);
            }


            if (! $this->checkDatabaseConnection($request)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Could not connect to the database.'
                ]);
            }

            $this->environmentManager->saveFileWizard($request);
            return response()->json([
                'status' => true,
                'message' => 'd',
                'url' => route('install.import-demo')
            ]);
        } catch (\Exception $e) {
            return true;
        }
    }
    /**
     * importDemo
     */
    public function importDemo()
    {
        Artisan::call('config:clear');

        return view('installer.index');
    }
    /**
     * imported
     *
     * @param  mixed  $redirect
     */
    public function imported(Redirector $redirect)
    {
        try {
            if (Request()->demo_import == 'on') {
                $this->databaseManager->migrateTable();
                $sql = File::get(public_path('demo.sql'));
                DB::connection()->getPdo()->exec($sql);
            } else {
                $this->databaseManager->migrateAndSeed();
                $sql = File::get(public_path('translate.sql'));
                DB::connection()->getPdo()->exec($sql);
            }
            return $redirect->route('install.final');
        } catch (\Throwable $th) {
            dd($th->getMessage());
            return $redirect->route('install.final');
        }
    }

    public function finish(InstalledFileManager $fileManager, FinalInstallManager $finalInstall, EnvironmentManager $environment)
    {

        $filePath = storage_path('app/public/file.txt');
        if (File::exists($filePath)) {
            $purchaseCode = File::get($filePath);
            PurchaseVerify::create(['purchase_code' => $purchaseCode]);
        }
        $finalMessages = $finalInstall->runFinal();
        $finalStatusMessage = $fileManager->update();
        $finalEnvFile = $environment->getEnvContent();
        $storagePath = public_path('storage');
        Artisan::call('cache:clear');
        if (! is_dir($storagePath)) {
            Artisan::call('storage:link');
        }
        return view('installer.index', compact('finalMessages', 'finalStatusMessage', 'finalEnvFile'));
    }

    /**
     * If application is already installed.
     */
    public function alreadyInstalled()
    {
        return file_exists(storage_path('installed'));
    }

    /**
     * purchaseCode
     *
     * @param  mixed  $request
     */
    public function purchaseCode(Request $request)
    {
        try {
            // Validate the request.
            $validator = Validator::make($request->all(), [
                'license_code' => 'required',
                'email' => 'required',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors()
                ]);
            }
            $response = $this->verifyApi($request);
            $data = [
                'email' => $request->email,
                'code' => $request->license_code,
            ];

            Storage::disk('local')->put('file.txt', json_encode($data));

            return response()->json([
                'status' =>   $response['status'],
                'message' => $response['result'],
                'url' => route('install.database.form')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => $response['status'],
                'message' => $e->getMessage()
            ]);
        }
    }

    private function checkDatabaseConnection(Request $request)
    {
        DB::purge();
        $connection = $request->input('database_connection');
        $settings = config("database.connections.$connection");
        config(
            [
                'database' => [
                    'default' => $connection,
                    'connections' => [
                        $connection => array_merge(
                            $settings,
                            [
                                'driver' => $connection,
                                'host' => $request->input('database_hostname'),
                                'port' => $request->input('database_port'),
                                'database' => $request->input('database_name'),
                                'username' => $request->input('database_username'),
                                'password' => $request->input('database_password'),
                            ]
                        ),
                    ],
                ],
            ]
        );

        try {

            Schema::connection($connection)->getConnection()->reconnect();


            DB::purge($connection); // Ensure the connection is fully reset
                DB::reconnect($connection);
                Schema::connection($connection)->getConnection()->reconnect();

                // Force a real database query to validate the connection
                DB::connection($connection)->statement('SELECT 1');

            return true;
        } catch (\Exception $e) {
            return false;
        }

    }

    public function licenseVerifyForm()
    {
        return view('installer.license');
    }

    public function licenseVerify(Request $request){
        try {
            $validator = Validator::make($request->all(), [
                'license_code' => 'required',
                'email' => 'required',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors()
                ]);
            }
            $response = $this->verifyApi($request);
            return response()->json([
                'status' => $response['status'],
                'message' => $response['result'],
                'url' => route('home.index')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }


    protected function verifyApi(Request $request)
    {
		return ['status' => true, 'result' => 'Verified!'];
    }

}
