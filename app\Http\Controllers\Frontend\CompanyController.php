<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Admin\Company;
use App\Repositories\CompanyRepository;
use Illuminate\Http\Request;

class CompanyController extends Controller
{
    public function __construct(protected CompanyRepository $company) {}

    public function getAll(Request $request)
    {
        try {

            $options = [];
            $company_type = [];
            $location = [];
            $sortedBy = '';

            if (isset($request->companytype)) {
                $company_type = $request->companytype;

            }

            if (isset($request->sort)) {
                $sortedBy = $request->sort;
            }
            if (isset($request->location)) {
                $location = $request->location;
            }

            $company_style = isset(Request()->style) ? Request()->style : 'list-view';

            $companies = Company::query();
            if (! empty($company_type)) {
                $companies->when($company_type, function ($q) use ($company_type) {
                    return $q->whereIn('company_type_id', explode(',', $company_type));
                });
            }
            if (! empty($location)) {
                $companies->when($location, function ($q) use ($location) {
                    return $q->whereIn('city_id', explode(',', $location));
                });
            }
            if (! empty($sortedBy)) {
                $companies->orderBy('id', $sortedBy);
            }

            $companies = $companies->withWhereHas('user', function ($query) {
                $query->where('status', 1);
                $query->where('is_email_verified', 1)
                    ->select('id', 'status', 'is_email_verified');
            })
                ->with('jobs', function ($q) {
                    $q->where('job_deadline', '>=', now())->where('status', 1)->select('id', 'company_id', 'job_deadline', 'job_vacancy');
                })
                ->select('id', 'company_name', 'company_type_id', 'user_id', 'company_logo', 'slug', 'status')
                ->countryCity()
                ->withCount('jobs')
                ->withSum('jobs', 'job_vacancy')
                ->orderBy('jobs_sum_job_vacancy', 'desc')
                ->where('status', 1)
                ->paginate(14);

            if ($request->ajax()) {
                $companies_view = view('front.pages.company.item-style', compact('companies', 'company_style'))->render();

                return response()->json(['status' => true, 'companies' => $companies_view, 'total' => $companies->total(), 'first_item' => $companies->firstItem(),  'last_item' => $companies->lastItem()]);
            }

            return view('front.pages.company.company-lists', compact('companies', 'company_style'));

        } catch (\Throwable $th) {
            return view('front.pages.404');
        }
    }

    /**=========== Company Details ============
     * ======= details=========
     *
     * @param
     * @return
     */

    public function details($slug)
    {
        try {

            $singleCompany = $this->company->companyDetails($slug);
            $latestJobs = companyLatestJob(10, $singleCompany->id);

            return view('front.pages.company.company-details', compact('singleCompany', 'latestJobs'));
        } catch (\Throwable $th) {
            return view('front.pages.404');
        }
    }

    /**
     * @param Request
     */
    public function companyListsGetDataByPagination(Request $request)
    {
        $company_style = $request->card_style ? $request->card_style : 'list_view';
        $data['company'] = $this->filterCompanyData($request);
        $companies = $data['company']->with('companytype')->latest()->paginate(2);

        return response()->json([
            'html' => view('front.pages.company.company_section', compact('companies', 'company_style'))->render(),
        ]);
    }

    public function getFilterCompanyLists(Request $request)
    {
        $company_style = $request->card_style ? $request->card_style : 'list_view';
        $data['company'] = $this->filterCompanyData($request);
        $companies = $data['company']->paginate(2);

        return response()->json([
            'html' => view('front.pages.company.company_section', compact('companies', 'company_style'))->render(),
        ]);
    }

    public function filterCompanyData($request)
    {

        $companyTypeId = $request->company_type_id ?? [];
        $sortBy_id = $request->sortBy_id;
        $data['company'] = Company::with('companytype')->where('status', 0);
        $showCompanySizeInputBox = $request->show_company_size_input_box;

        $data['company'] = $data['company']->where(function ($q) use ($companyTypeId, $showCompanySizeInputBox) {
            if (! empty($companyTypeId)) {
                $q->whereIn('company_type_id', $companyTypeId);
            }
            if (! empty($showCompanySizeInputBox)) {
                $value = explode('-', $showCompanySizeInputBox);
                $first_value = $value[0];
                $second_value = $value[1];
                $q->whereBetween('company_size', [$first_value, $second_value]);
            }
        });
        if ($sortBy_id) {
            if ($sortBy_id == 2) {
                $data['company'] = $data['company']->orderBy('id', 'DESC');
            }
            if ($sortBy_id == 3) {
                $data['company'] = $data['company']->orderBy('id', 'ASC');
            }
        }

        return $data['company'];
    }
}
