@php
    $header = getThemeOption('header' . activeLanguage()) ?? 'headeren';
    $header = $header ? $header : getThemeOption('headeren');
@endphp
<!-- topbar -->
<div class="topbar d-lg-block d-none">
    <div class="row">
        <div class="col-lg-12 d-flex justify-content-between align-items-center">
            <div class="topbar-left">
                <p>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g>
                            <path
                                d="M11.6664 7.33336C9.27258 7.33336 7.33301 9.27215 7.33301 11.6669C7.33316 14.06 9.27258 16.0001 11.6664 16.0001C14.0605 16.0001 15.9999 14.0601 15.9999 11.6666C15.9999 9.27203 14.0605 7.33336 11.6664 7.33336ZM11.6664 14.6666C10.0089 14.6666 8.66637 13.3237 8.66637 11.6666C8.66637 10.0091 10.0089 8.66656 11.6664 8.66656C13.3239 8.66656 14.6664 10.0091 14.6664 11.6666C14.6664 13.3237 13.3239 14.6666 11.6664 14.6666Z"
                                fill="#059669" />
                            <path
                                d="M13.3334 0C11.8608 0 10.6666 1.19445 10.6666 2.66691C10.6666 4.13941 9.47266 5.3334 8.00004 5.3334C6.52742 5.3334 5.3334 4.13941 5.3334 2.6668C5.3334 1.19418 4.13945 0 2.6666 0C1.19449 0 0 1.19398 0 2.66664C0 4.1393 1.19445 5.33324 2.66664 5.33324C4.13926 5.33324 5.33293 6.52727 5.33293 7.99973C5.33293 9.47238 4.13941 10.6664 2.66664 10.6664C1.19387 10.6664 0 11.8608 0 13.3334C0 14.8061 1.19398 16.0001 2.66664 16.0001C4.1393 16.0001 5.33324 14.8052 5.33324 13.3334C5.33324 8.91602 8.91465 5.33344 13.3331 5.33293C14.8061 5.33293 16.0001 4.13895 16.0001 2.66648C16.0001 1.19445 14.8061 0 13.3334 0ZM2.66664 3.99992C1.92977 3.99992 1.33316 3.40305 1.33316 2.66664C1.33316 1.92977 1.92977 1.33316 2.66664 1.33316C3.40316 1.33316 3.99992 1.92977 3.99992 2.66664C3.99992 3.40301 3.40344 3.99992 2.66664 3.99992ZM2.66664 14.6666C1.92977 14.6666 1.33316 14.0691 1.33316 13.3334C1.33316 12.5961 1.92977 12 2.66664 12C3.40352 12 3.99992 12.5961 3.99992 13.3334C3.99992 14.0691 3.40344 14.6666 2.66664 14.6666ZM13.3334 3.99992C12.5961 3.99992 12 3.40305 12 2.66664C12 1.92977 12.5961 1.33316 13.3334 1.33316C14.0704 1.33316 14.6666 1.92977 14.6666 2.66664C14.6666 3.40301 14.0704 3.99992 13.3334 3.99992H13.3334Z"
                                fill="#059669" />
                        </g>
                    </svg>
                    {!! $header['header-left-text'] ?? null !!}
                </p>
            </div>
            @php
                if (Session::has('locale')) {
                    $locale = Session::get('locale', Config::get('app.locale'));
                } else {
                    $locale = env('DEFAULT_LANGUAGE');
                }
            @endphp
            <div class="topbar-right">
                <div class="language-area">
                    <div class="lang-bar">
                        <div class="lang-btn">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path
                                        d="M8 0C3.59 0 0 3.59 0 8C0 12.41 3.59 16 8 16C12.41 16 16 12.41 16 8C16 3.59 12.41 0 8 0ZM14.3325 11.69H12.205C12.5175 10.6775 12.7075 9.54 12.7375 8.3325H15.325C15.27 9.5525 14.915 10.695 14.3325 11.69ZM0.675 8.3325H3.2625C3.2925 9.54 3.48 10.6775 3.795 11.69H1.6675C1.0684 10.6683 0.727714 9.51575 0.675 8.3325ZM1.6675 4.31H3.795C3.4825 5.3225 3.2925 6.46 3.2625 7.6675H0.675C0.73 6.4475 1.085 5.305 1.6675 4.31ZM8.3325 3.6425V0.6925C9.5375 0.87 10.595 1.99 11.275 3.6425H8.3325ZM11.52 4.31C11.8425 5.305 12.04 6.4475 12.07 7.6675H8.3325V4.31H11.52ZM7.6675 0.6925V3.6425H4.7225C5.405 1.99 6.4625 0.87 7.6675 0.6925ZM7.6675 4.31V7.6675H3.9275C3.9575 6.4475 4.155 5.305 4.4775 4.31H7.6675ZM3.9275 8.3325H7.665V11.69H4.48C4.1575 10.695 3.96 9.5525 3.9275 8.3325ZM7.6675 12.3575V15.3075C6.4625 15.13 5.405 14.01 4.725 12.3575H7.6675ZM8.3325 15.3075V12.3575H11.275C10.595 14.01 9.5375 15.13 8.3325 15.3075ZM8.3325 11.69V8.3325H12.07C12.04 9.5525 11.8425 10.695 11.52 11.69H8.3325ZM12.7375 7.6675C12.7075 6.46 12.52 5.3225 12.205 4.31H14.3325C14.915 5.305 15.27 6.4475 15.325 7.6675H12.7375ZM13.895 3.6425H11.975C11.5625 2.5775 11.005 1.6875 10.35 1.055C11.7672 1.53711 13.0038 2.43971 13.895 3.6425ZM5.65 1.055C4.995 1.69 4.4375 2.5775 4.025 3.6425H2.105C2.9875 2.4525 4.22 1.54 5.65 1.055ZM2.105 12.3575H4.025C4.4375 13.4225 4.995 14.3125 5.65 14.945C4.23279 14.4629 2.99618 13.5603 2.105 12.3575ZM10.35 14.945C11.005 14.31 11.5625 13.4225 11.975 12.355H13.895C13.0047 13.5592 11.7679 14.4628 10.35 14.945Z"
                                        fill="#059669" />
                                </g>
                            </svg>
                            {{ translate('Language') }}
                        </div>
                        <ul class="lang-card">
                            @foreach (App\Models\Admin\Language::all() as $language)
                                <li>
                                    <a class="languageTranslate dropdown-item d-flex @if ($locale == $language->code) active @endif"
                                        href="javascript:void(0)" data-action="{{ route('language.change') }}"
                                        id="{{ $language->code }}" data-code="{{ $language->code }}"
                                        data-flag="{{ $language->code }}">
                                        {{ $language->name }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                <div class="social-area">
                    @include('front.layouts.includes.social-area')
                </div>
            </div>
        </div>
    </div>
</div>
