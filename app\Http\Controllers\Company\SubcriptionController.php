<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\Front\Subscription;
use App\Repositories\Companies\SubcriptionRepository;
use Illuminate\Http\Request;

class SubcriptionController extends Controller
{
    //

    public function __construct(protected SubcriptionRepository $subscription) {}

    /**
     * Display a listing of the resource.
     *=========  planSubscription ===============

     *
     * @param Request
     * @return \Illuminate\Http\Response
     */
    public function planSubscription()
    {
        $subscriptions = $this->subscription->getByUserId(10);

        return view('front.pages.company-dashboard.plan-subscription', compact('subscriptions'));
    }

    /** subscription invoce download specific
     *
     * ========= invoiceDownload=========
     *
     * @param  int  $id
     */
    public function invoiceDownload($id)
    {
        try {
            $subscription = $this->subscription->getById($id);
            ini_set('max_execution_time', 120);
            $mpdf = new \Mpdf\Mpdf;
            $html = view('front.pages.subscription.invoice', compact('subscription'))->render();

            $mpdf->WriteHTML($html);
            $fileName = str_replace(' ', '_', $subscription->company->company_name).'_ '.str_replace(' ', '_', $subscription->created_at);
            $pdfFilePath = $fileName.'.pdf';
            $mpdf->Output($pdfFilePath, 'I');
        } catch (\Throwable $th) {

        }
    }

    /**
     * paymentSuccess
     *
     * @param  mixed  $subsctiption
     * @return view
     */
    public function paymentSuccess($subsctiption)
    {

        $subscription = Subscription::with(['company' => function ($query) {
            $query->select('id', 'company_name', 'company_email', 'company_type_id', 'company_logo', 'company_location');
            $query->with('companytype');
        }])->where('subscription_number', $subsctiption)->first();

        return view('front.pages.success', compact('subscription'));
    }
}
