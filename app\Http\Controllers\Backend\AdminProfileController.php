<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\AdminChangePasswordRequest;
use App\Models\Admin;
use App\Repositories\Admin\AdminRepostitory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AdminProfileController extends Controller
{
    public function __construct(protected AdminRepostitory $admin) {}

    /**
     * adminProfile
     *
     * @return View
     */
    public function adminProfile()
    {
        if (!Auth::guard('admin')->check()) {
            return redirect()->back();
        }
        $authEmail = Auth::guard('admin')->user()->email;
        $admin = Admin::where('email', $authEmail)->first();
        if ($admin == $authEmail) {
            $admin = Admin::where('email', $authEmail)->first();
        }
        return view('admin.pages.profile.index', compact('admin'));
    }

    /**
     * profileUpdate
     *
     * @param  mixed  $request
     * @return Response
     */
    public function profileUpdate(Request $request)
    {
        $result = $this->admin->update($request);
        if ($result['status'] !== true) {
            return response()->json([
                'status' => false, 
                'message' => $result['message']
            ]);
        }

        return response()->json([
            'status' => true, 
            'message' => $result['message'], 
            'url' => route('admin.profile')
        ]);
       
    }

    public function adminProfilePasswordUpdate(AdminChangePasswordRequest $request)
    {
        $admin = Admin::whereId(Auth::id())->first();

        // Handle password update for admin
        if (empty($admin)) {
            return $this->sendToastrWarning('User not found!');
        }

        // Check if the current password matches
        if (!Hash::check($request->password, $admin->password)) {
            return $this->sendToastrWarning('Current password is invalid!');
        }

        // Check if the new password and confirm password match
        if ($request->new_password !== $request->confirm_password) {
            return $this->sendToastrWarning('Confirm password does not match!');
        }

        // Update the password and handle success or failure
        if ($admin->update(['password' => Hash::make($request->new_password)])) {
            return $this->sendToastrSuccess('Password successfully updated!');
        }

        return $this->sendToastrWarning('Something went wrong!');
    }


    /**
     * Send a success toastr message and redirect back.
     *
     * @param string $message
     * @return \Illuminate\Http\RedirectResponse
     */
    private function sendToastrSuccess(string $message)
    {
        toastr()->success('', translate($message), ['positionClass' => 'toast-top-right']);
        return redirect()->back();
    }

    /**
     * Send a warning toastr message and redirect back.
     *
     * @param string $message
     * @return \Illuminate\Http\RedirectResponse
     */
    private function sendToastrWarning(string $message)
    {
        toastr()->warning('',translate($message), ['positionClass' => 'toast-top-right']);
        return redirect()->back();
    }
}
