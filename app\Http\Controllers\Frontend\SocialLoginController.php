<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Admin\Candidate;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

class SocialLoginController extends Controller
{
    public function redirect($provider)
    {
        return Socialite::driver($provider)->redirect();
    }

    public function callBack($provider)
    {
        try {
            $userSocial = Socialite::driver($provider)->stateless()->user();
            $user = User::where(['email' => $userSocial->getEmail()])->first();
            if (! $user) {
                $user = User::create([
                    'username' => $userSocial->getName(),
                    'email' => $userSocial->getEmail(),
                    'provider_id' => $userSocial->getId(),
                    'provider' => $provider,
                    'status' => 1,
                    'is_email_verified' => 1,
                    'role' => 1,
                ]);
                if ($user) {
                    $user->assignRole('candidate');
                    Candidate::create([
                        'user_id' => $user->id,
                    ]);
                    Auth::login($user);

                    return redirect()->route('home');
                }

                return redirect()->back();
            }

            Auth::login($user);

            return redirect()->route('home');

        } catch (\Throwable $th) {
            return redirect()->back();
        }
    }
}
