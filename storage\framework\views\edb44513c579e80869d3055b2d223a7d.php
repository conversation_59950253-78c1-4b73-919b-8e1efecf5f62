<script>
    (function($){
        "use strict";

        let key = document.getElementById("social_area_list");
        key = key.querySelectorAll('.row.social-item').length

        $(".add-social").on('click', function (e) {
            key++;
            e.preventDefault();



            let html = `<div class="row social-item">
                    <div class="col-xl-3">
                        <div class="form-inner mb-2"><label>Icon Class(Bootstrap) <span></span></label>
                                <input type="text" class="username-input" name="social[${key}][class]">
                        </div>
                    </div>
                    <div class="col-xl-7">
                        <div class="form-inner mb-2">
                            <label>Social Url <span></span></label>
                            <input type="text" class="username-input"  name="social[${key}][url]">
                        </div>
                    </div>
                    <div class="col-sm-2 mt-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="add-row">
                                <button type="button" class="remove-work-area-row remove eg-btn btn--red back-btn"><i class="bi bi-trash"></i></button>
                            </div>
                        </div>
                    </div>
                </div>`

                $('.social-area').append(html);

            });




        $(document).on('click', '.remove-work-area-row', function () {
            let parent= $(this).closest('.social-item').remove();

        });

    })(jQuery);

    </script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/admin/them-optiopn.blade.php ENDPATH**/ ?>