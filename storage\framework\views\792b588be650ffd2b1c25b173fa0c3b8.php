<script>
    (function($) {
        "use strict";

        let table = $('#datatable').DataTable({
            processing: true,
            serverSide: true,
            destroy: true,
            ajax: {
                url: "<?php echo e(route('admin.company')); ?>",
            },
            columns: [{
                    data: 'Company',
                    name: 'Company'
                },

                {
                    data: 'OwnerName',
                    name: 'OwnerName'
                },
                {
                    data: 'Organization/Country',
                    name: 'Organization/Country'
                },
                {
                    data: 'Establishment Date',
                    name: 'Establishment Date'
                },

                {
                    data: 'Verified',
                    name: 'Verified'
                },
                {
                    data: 'Status',
                    name: 'Status'
                },
                {
                    data: 'action',
                    name: 'action',
                    orderable: false
                }
            ]
        });

        ///delete category

        $(document).on('click', '.deleteCompany', function(e) {
            e.preventDefault();
            var id = $(this).data('id');

            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '/admin/company/delete/' + id,
                        type: "GET",
                        dataType: "JSON",
                        success: function(data) {
                            table.draw();
                            if (data.hasOwnProperty('message')) {
                                Swal.fire(
                                    'error!',
                                    `${data.message}`,
                                )
                            } else {
                                Swal.fire(
                                    'Deleted!',
                                    'Your file has been deleted.',
                                    'success'
                                )
                            }


                        },
                    });

                } else if (
                    result.dismiss === Swal.DismissReason.cancel
                ) {
                    Swal.fire(
                        'Cancelled',
                        'Your file is safe :)',
                        'error'
                    )
                }
            })
        });

        $(document).on('change', '.status-change', function(e) {
            e.preventDefault();
            let action = $(this).data('action');
            $.ajax({
                url: action,
                type: "GET",
                dataType: "JSON",
                success: function(data) {
                    if (data.status === true) {
                        toastr["success"](`${data.message}`);
                        table.draw();
                    } else if (data.status == false) {
                        toastr["error"](`${data.message}`);
                    }
                },
            });
        });
    })(jQuery);
</script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/company/company.blade.php ENDPATH**/ ?>