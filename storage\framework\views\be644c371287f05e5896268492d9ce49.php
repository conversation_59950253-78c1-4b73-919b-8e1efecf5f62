<script>
    (function($) {
        "use strict";

        // save data
        $(document).on('submit', '.add-form', function(e) {
            e.preventDefault();

            let form = $(this);
            let formData = new FormData(form[0]);
            let action = form.attr('action');
            $.ajax({
                type: "POST",
                url: action,
                data: formData,
                dataType: "json",
                cache: false,
                contentType: false,
                processData: false,
                success: function(data) {
                    if (data.status == false) {
                        if (data.hasOwnProperty('message')) {
                            toastr["error"](`${data.message}`);
                        }
                        printErrorMsg(data.errors);
                    } else if (data.status == true) {
                        $(".modal").modal("hide");
                        if (data.hasOwnProperty('message')) {
                            toastr["info"](`${data.message}`);
                        }
                        if (data.hasOwnProperty('url')) {
                            $(form)[0].reset();
                            location.replace(`${data.url}`)
                        }
                        if (data.hasOwnProperty('menu')) {
                            $(".menu-structure").load(location.href + " .menu-structure>*", "");
                            location.reload();

                        }
                    }
                },
            })
        })

        /** print error message
         * ======== printErrorMsg======
         *
         * @param msg
         *
         */
        function printErrorMsg(msg) {
            $.each(msg, function(key, value) {
                $('.' + key + '_err').text(value).fadeIn().delay(5000).fadeOut("slow");
            });
        }
    })(jQuery);
</script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/custom.blade.php ENDPATH**/ ?>