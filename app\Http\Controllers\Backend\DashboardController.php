<?php

namespace App\Http\Controllers\Backend;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Admin\EducationLavel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Session;
use App\Repositories\Admin\DashboardRepostitory;

class DashboardController extends Controller
{
    public function __construct(protected DashboardRepostitory $dashboard) {}

    /** admin dashboard
     * ======= adminDashboard ========
     */
    public function adminDashboard()
    {
        $authEmail = Auth::guard('admin')->user()->email;
        if (Session::get('authEmail') == $authEmail) {
            return redirect()->route('admin.lock');
        }
        $data = $this->dashboard->dashboardInfo();

        // return $data;
        $users = $this->dashboard->latestUser(6);
        $companies = $this->dashboard->company(6);
        $candidates = $this->dashboard->candidate(6);
        $subscriptions = $this->dashboard->latestSubscription(6);

        return view('admin.pages.dashboard', compact('data', 'users', 'companies', 'candidates', 'subscriptions'));
    }

    /** company login from admin dashboard
     *  ===========  company login ============
     *
     * @param Request
     */
    public function loginByAdmin(Request $request)
    {

        try {
            $user = User::find($request->id);
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'User not found'
                ]);
            }

            Auth::guard('web')->login($user);
            $url = match (true) {
                $user->hasRole('company') => route('company.dashboard'),
                $user->hasRole('candidate') => route('candidate.dashboard'),
                default => null,
            };

            return $url
                ? response()->json([
                    'status' => true,
                     'url' => $url
                     ])
                : response()->json([
                    'status' => false,
                    'message' => 'Unauthorized access'
                ]);

        } catch (\Throwable $th) {
            return response()->json([
                'status' => true,
                'message' => $th->getMessage()
            ]);
        }
    }

    /**
     * backendSetting
     *
     * @return View
     */
    public function backendSetting()
    {

        Artisan::call('cache:clear');
        return view('admin.pages.backend-setting.index');
    }

    /**
     * sendTestMail
     *
     * @param  mixed  $request
     * @return Response
     */
    public function sendTestMail(Request $request)
    {
        if (!$request) {
            return redirect()->back()->with('error', 'Input your email');
        }
        $general = getThemeOption('general') ?? [];


        $mailTo = $request->email;
        Mail::send('mail.test', ['content' => $request->message], function ($message) use ($mailTo, $general) {
            $message->to($mailTo)->subject('Test Email From'.$general['application_name'] ?? '')->from(Config::get('mail.from.address'), Config::get('mail.from.name'));
        });
        toastr()->success('', 'Test Mail Sent Successfully', ['positionClass' => 'toast-top-right']);
        return redirect()->back();
    }

    /**
     * qaualification
     *
     * @param  mixed  $name
     * @return Response
     */
    public function qaualification($name)
    {

        $qaualifications = EducationLavel::where('status', 1)
        ->where('name', 'like', '%'.$name.'%')
        ->get();
        return response()->json($qaualifications);
    }

    /**
     * cacheClear
     *
     *
     */
    public function cacheClear(){

         Artisan::call('optimize:clear');
         Artisan::call('config:clear');
         toastr()->success('Cahce Clear Successfully');
         return redirect()->back();
    }


    /**
     * storageLink
     */
    public function storageLink(){
       Artisan::call('storage:link');
       toastr()->success('storage link successfully');
       return redirect()->back();
  }
}
