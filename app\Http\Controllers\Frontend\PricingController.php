<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Admin\Package;
use App\Models\Admin\PackageAddon;

class PricingController extends Controller
{
    /**
     * index
     *
     * @return view
     */
    public function index()
    {

        $package = Package::with('packageItems')->first();
        $packageAddons = PackageAddon::with('addonItems')->where('package_id', $package->id)->get();

        return view('front.pages.pricing.index', compact('package', 'packageAddons'));
    }

    /**
     * addonCheck
     *
     * @param  mixed  $addonId
     * @return Response
     */
    public function addonCheck($addonId)
    {

        try {
            $packageAddon = PackageAddon::where('id', $addonId)->first();
            if ($packageAddon) {
                return response()->json(['status' => true, 'addon' => $packageAddon]);
            }
        } catch (\Throwable $th) {
            return response()->json(['status' => false]);
        }
    }
}
