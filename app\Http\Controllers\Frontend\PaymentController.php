<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Front\Subscription;
use Cartalyst\Stripe\Stripe;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    //

    public function paymentConfirm(Request $request)
    {

        try {

            $stripe = new Stripe;
            $stripe = Stripe::make(env('STRIPE_SECRET'));
            $token = $stripe->tokens()->create([
                'card' => [
                    'number' => $request->card_number,
                    'exp_month' => $request->exp_month,
                    'exp_year' => $request->exp_year,
                    'cvc' => $request->cvc,
                ],

            ]);

            if (! isset($token['id'])) {
                // Toastr::error('Your Card Information is not valid');
                return redirect('/success');
            }

            $customer = $stripe->customers()->create([
                'name' => 'Mahabubul',
                'phone' => '017789881913',
                'address' => [
                    'line1' => '12',
                    'postal_code' => 'A94 HY59',
                    'city' => 'Stillorgan',
                    'state' => 'Dublin',
                    'country' => 'Ireland',
                ],
                'shipping' => [
                    'name' => 'Kanan',
                    'address' => [
                        'line1' => '12',
                        'postal_code' => 'A94 HY59',
                        'city' => 'Stillorgan',
                        'state' => 'Dublin',
                        'country' => 'Ireland',
                    ],
                ],

                'source' => $token['id'],
            ]);

            $charge = $stripe->charges()->create([
                'customer' => $customer['id'],
                'currency' => 'usd',
                'amount' => 1000,
                'description' => 'Payment for Order no.'.'sdfsdaf',
            ]);

            // return  response()->json(['status' => true, 'message' => 'success', 'url' => route('payment.success')]);
        } catch (\Exception $e) {
            //throw $th;

            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    public function paymentSuccess($subsctiption)
    {
        $subsctiption = Subscription::where('subscription_number', $subsctiption)->first();

        return view('front.pages.success', compact('subsctiption'));
    }
}
