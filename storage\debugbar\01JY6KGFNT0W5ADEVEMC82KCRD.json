{"__meta": {"id": "01JY6KGFNT0W5ADEVEMC82KCRD", "datetime": "2025-06-20 12:02:43", "utime": **********.003615, "method": "GET", "uri": "/admin/pages/edit/1?lang=en", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.265151, "end": **********.003632, "duration": 0.7384810447692871, "duration_str": "738ms", "measures": [{"label": "Booting", "start": **********.265151, "relative_start": 0, "end": **********.484306, "relative_end": **********.484306, "duration": 0.*****************, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.484319, "relative_start": 0.*****************, "end": **********.003634, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "519ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.49519, "relative_start": 0.*****************, "end": **********.499085, "relative_end": **********.499085, "duration": 0.0038950443267822266, "duration_str": "3.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.535334, "relative_start": 0.*****************, "end": **********.001466, "relative_end": **********.001466, "duration": 0.*****************, "duration_str": "466ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "1x admin.pages.pages.edit", "param_count": null, "params": [], "start": **********.537421, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/pages/edit.blade.phpadmin.pages.pages.edit", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fpages%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.pages.edit"}, {"name": "1x admin.pages.widgets.hero-section", "param_count": null, "params": [], "start": **********.555272, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/hero-section.blade.phpadmin.pages.widgets.hero-section", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fwidgets%2Fhero-section.blade.php&line=1", "ajax": false, "filename": "hero-section.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.widgets.hero-section"}, {"name": "1x admin.pages.widgets.latest-jobs-category", "param_count": null, "params": [], "start": **********.81841, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/latest-jobs-category.blade.phpadmin.pages.widgets.latest-jobs-category", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fwidgets%2Flatest-jobs-category.blade.php&line=1", "ajax": false, "filename": "latest-jobs-category.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.widgets.latest-jobs-category"}, {"name": "1x admin.pages.widgets.featured-jobs", "param_count": null, "params": [], "start": **********.832106, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/featured-jobs.blade.phpadmin.pages.widgets.featured-jobs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fwidgets%2Ffeatured-jobs.blade.php&line=1", "ajax": false, "filename": "featured-jobs.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.widgets.featured-jobs"}, {"name": "1x admin.pages.widgets.working-process", "param_count": null, "params": [], "start": **********.843522, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/working-process.blade.phpadmin.pages.widgets.working-process", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fwidgets%2Fworking-process.blade.php&line=1", "ajax": false, "filename": "working-process.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.widgets.working-process"}, {"name": "1x admin.pages.widgets.dream-location", "param_count": null, "params": [], "start": **********.850874, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/dream-location.blade.phpadmin.pages.widgets.dream-location", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fwidgets%2Fdream-location.blade.php&line=1", "ajax": false, "filename": "dream-location.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.widgets.dream-location"}, {"name": "1x admin.pages.widgets.trusted-company", "param_count": null, "params": [], "start": **********.861605, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/trusted-company.blade.phpadmin.pages.widgets.trusted-company", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fwidgets%2Ftrusted-company.blade.php&line=1", "ajax": false, "filename": "trusted-company.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.widgets.trusted-company"}, {"name": "1x admin.pages.widgets.top-recruiters", "param_count": null, "params": [], "start": **********.868552, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/top-recruiters.blade.phpadmin.pages.widgets.top-recruiters", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fwidgets%2Ftop-recruiters.blade.php&line=1", "ajax": false, "filename": "top-recruiters.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.widgets.top-recruiters"}, {"name": "1x admin.pages.widgets.latest-blog", "param_count": null, "params": [], "start": **********.879267, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/latest-blog.blade.phpadmin.pages.widgets.latest-blog", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fpages%2Fwidgets%2Flatest-blog.blade.php&line=1", "ajax": false, "filename": "latest-blog.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.pages.widgets.latest-blog"}, {"name": "1x js.admin.widget", "param_count": null, "params": [], "start": **********.888286, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/admin/widget.blade.phpjs.admin.widget", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Fadmin%2Fwidget.blade.php&line=1", "ajax": false, "filename": "widget.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.admin.widget"}, {"name": "1x admin.master", "param_count": null, "params": [], "start": **********.895961, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/master.blade.phpadmin.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.master"}, {"name": "1x admin.includes.head", "param_count": null, "params": [], "start": **********.896276, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/head.blade.phpadmin.includes.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.includes.head"}, {"name": "1x admin.includes.header", "param_count": null, "params": [], "start": **********.897497, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/header.blade.phpadmin.includes.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.includes.header"}, {"name": "1x admin.includes.sidebar", "param_count": null, "params": [], "start": **********.913988, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/sidebar.blade.phpadmin.includes.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.includes.sidebar"}, {"name": "1x admin.includes.script", "param_count": null, "params": [], "start": **********.987078, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/script.blade.phpadmin.includes.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.includes.script"}, {"name": "1x js.translate", "param_count": null, "params": [], "start": **********.987477, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/translate.blade.phpjs.translate", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftranslate.blade.php&line=1", "ajax": false, "filename": "translate.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.translate"}]}, "route": {"uri": "GET admin/pages/edit/{id}", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "controller": "App\\Http\\Controllers\\Backend\\PagesController@edit<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=55\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.pages.edit", "prefix": "admin/pages", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=55\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/PagesController.php:55-62</a>"}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01041, "accumulated_duration_str": "10.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.514001, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 5.476}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": "middleware", "name": "prevent-back-history", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Middleware\\PreventBackHistory.php", "line": 19}], "start": **********.522278, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 5.476, "width_percent": 5.091}, {"sql": "select * from `pages` where `pages`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.524949, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "PagesController.php:58", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=58", "ajax": false, "filename": "PagesController.php", "line": "58"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 10.567, "width_percent": 15.274}, {"sql": "select * from `widgets_contents` where `widgets_contents`.`page_id` in (1) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.529493, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:58", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=58", "ajax": false, "filename": "PagesController.php", "line": "58"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 25.841, "width_percent": 4.899}, {"sql": "select * from `widgets` where `widgets`.`widget_slug` in ('dream-location', 'featured-jobs', 'hero-section', 'latest-blog', 'latest-jobs-category', 'top-recruiters', 'trusted-company', 'working-process')", "type": "query", "params": [], "bindings": ["dream-location", "featured-jobs", "hero-section", "latest-blog", "latest-jobs-category", "top-recruiters", "trusted-company", "working-process"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.531995, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:58", "source": {"index": 27, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=58", "ajax": false, "filename": "PagesController.php", "line": "58"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 30.74, "width_percent": 4.419}, {"sql": "select * from `widgets` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.533443, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:59", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=59", "ajax": false, "filename": "PagesController.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 35.159, "width_percent": 4.995}, {"sql": "select * from `pages_translations` where `pages_translations`.`page_id` = 1 and `pages_translations`.`page_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Pages.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Pages.php", "line": 29}, {"index": 21, "namespace": "view", "name": "admin.pages.pages.edit", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/pages/edit.blade.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.547298, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Pages.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Pages.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Pages.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FPages.php&line=29", "ajax": false, "filename": "Pages.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 40.154, "width_percent": 4.803}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 3 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "admin.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/hero-section.blade.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.800984, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 44.957, "width_percent": 5.668}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 111 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "admin.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/latest-jobs-category.blade.php", "line": 29}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.819059, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 50.624, "width_percent": 5.091}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 5 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "admin.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/featured-jobs.blade.php", "line": 29}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.832809, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 55.716, "width_percent": 5.86}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 96 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "admin.pages.widgets.working-process", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/working-process.blade.php", "line": 29}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.844185, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 61.575, "width_percent": 5.091}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 105 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [105], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "admin.pages.widgets.dream-location", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/dream-location.blade.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.8514838, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.667, "width_percent": 5.572}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 106 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "admin.pages.widgets.trusted-company", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/trusted-company.blade.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.862437, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 72.238, "width_percent": 6.82}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 10 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "admin.pages.widgets.top-recruiters", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/top-recruiters.blade.php", "line": 29}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.869217, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 79.059, "width_percent": 5.572}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 136 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [136], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "admin.pages.widgets.latest-blog", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/pages/widgets/latest-blog.blade.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.879945, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 84.63, "width_percent": 5.187}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "admin.includes.header", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/header.blade.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9068182, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "admin.includes.header:40", "source": {"index": 16, "namespace": "view", "name": "admin.includes.header", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/admin/includes/header.blade.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fadmin%2Fincludes%2Fheader.blade.php&line=40", "ajax": false, "filename": "header.blade.php", "line": "40"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 89.817, "width_percent": 4.707}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": [1, "App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.9231339, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 94.524, "width_percent": 5.476}]}, "models": {"data": {"App\\Models\\Admin\\WidgetContentTranslation": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetContentTranslation.php&line=1", "ajax": false, "filename": "WidgetContentTranslation.php", "line": "?"}}, "App\\Models\\Admin\\Widgets": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgets.php&line=1", "ajax": false, "filename": "Widgets.php", "line": "?"}}, "App\\Models\\Admin\\WidgetsContents": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=1", "ajax": false, "filename": "WidgetsContents.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Admin\\Pages": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FPages.php&line=1", "ajax": false, "filename": "Pages.php", "line": "?"}}, "App\\Models\\Admin\\PagesTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FPagesTranslation.php&line=1", "ajax": false, "filename": "PagesTranslation.php", "line": "?"}}}, "count": 74, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 35, "messages": [{"message": "[\n  ability => dashoard.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1714842034 data-indent-pad=\"  \"><span class=sf-dump-note>dashoard.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">dashoard.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714842034\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.927476, "xdebug_link": null}, {"message": "[\n  ability => subscription.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1169428483 data-indent-pad=\"  \"><span class=sf-dump-note>subscription.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">subscription.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169428483\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.929298, "xdebug_link": null}, {"message": "[\n  ability => subscription.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>subscription.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">subscription.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.930547, "xdebug_link": null}, {"message": "[\n  ability => companytype.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1327378127 data-indent-pad=\"  \"><span class=sf-dump-note>companytype.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">companytype.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327378127\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.932403, "xdebug_link": null}, {"message": "[\n  ability => company.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1971981559 data-indent-pad=\"  \"><span class=sf-dump-note>company.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">company.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971981559\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.933477, "xdebug_link": null}, {"message": "[\n  ability => candidate.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1124768658 data-indent-pad=\"  \"><span class=sf-dump-note>candidate.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">candidate.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1124768658\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.934833, "xdebug_link": null}, {"message": "[\n  ability => jobcategory.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1664957147 data-indent-pad=\"  \"><span class=sf-dump-note>jobcategory.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">jobcategory.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664957147\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.936952, "xdebug_link": null}, {"message": "[\n  ability => jobcategory.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1967483017 data-indent-pad=\"  \"><span class=sf-dump-note>jobcategory.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">jobcategory.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967483017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.938159, "xdebug_link": null}, {"message": "[\n  ability => jobsubcategory.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1312218381 data-indent-pad=\"  \"><span class=sf-dump-note>jobsubcategory.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">jobsubcategory.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312218381\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.939468, "xdebug_link": null}, {"message": "[\n  ability => jobtype.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-564616907 data-indent-pad=\"  \"><span class=sf-dump-note>jobtype.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">jobtype.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564616907\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.941088, "xdebug_link": null}, {"message": "[\n  ability => jobskill.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2066248199 data-indent-pad=\"  \"><span class=sf-dump-note>jobskill.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">jobskill.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2066248199\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.944208, "xdebug_link": null}, {"message": "[\n  ability => jobrole.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1210379601 data-indent-pad=\"  \"><span class=sf-dump-note>jobrole.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">jobrole.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1210379601\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.945605, "xdebug_link": null}, {"message": "[\n  ability => jobexperience.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-169802539 data-indent-pad=\"  \"><span class=sf-dump-note>jobexperience.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">jobexperience.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169802539\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.946935, "xdebug_link": null}, {"message": "[\n  ability => languagelevel.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1089189638 data-indent-pad=\"  \"><span class=sf-dump-note>languagelevel.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">languagelevel.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1089189638\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948293, "xdebug_link": null}, {"message": "[\n  ability => careerlevel.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-79489630 data-indent-pad=\"  \"><span class=sf-dump-note>careerlevel.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">careerlevel.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79489630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.949672, "xdebug_link": null}, {"message": "[\n  ability => profession.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1538985843 data-indent-pad=\"  \"><span class=sf-dump-note>profession.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">profession.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538985843\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.951363, "xdebug_link": null}, {"message": "[\n  ability => education.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-210024571 data-indent-pad=\"  \"><span class=sf-dump-note>education.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">education.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-210024571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.952737, "xdebug_link": null}, {"message": "[\n  ability => package.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-585560580 data-indent-pad=\"  \"><span class=sf-dump-note>package.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">package.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585560580\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.95395, "xdebug_link": null}, {"message": "[\n  ability => user.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-220743833 data-indent-pad=\"  \"><span class=sf-dump-note>user.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220743833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.956065, "xdebug_link": null}, {"message": "[\n  ability => permission.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-952635189 data-indent-pad=\"  \"><span class=sf-dump-note>permission.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">permission.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-952635189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.957312, "xdebug_link": null}, {"message": "[\n  ability => role.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1345266929 data-indent-pad=\"  \"><span class=sf-dump-note>role.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345266929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.959778, "xdebug_link": null}, {"message": "[\n  ability => themeoption.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1737487807 data-indent-pad=\"  \"><span class=sf-dump-note>themeoption.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">themeoption.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737487807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.962611, "xdebug_link": null}, {"message": "[\n  ability => emailtemplate.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1460652380 data-indent-pad=\"  \"><span class=sf-dump-note>emailtemplate.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">emailtemplate.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460652380\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.964102, "xdebug_link": null}, {"message": "[\n  ability => menu.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1286831679 data-indent-pad=\"  \"><span class=sf-dump-note>menu.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">menu.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286831679\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.965451, "xdebug_link": null}, {"message": "[\n  ability => page.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-810828857 data-indent-pad=\"  \"><span class=sf-dump-note>page.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">page.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810828857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.967272, "xdebug_link": null}, {"message": "[\n  ability => blogcategory.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2130138464 data-indent-pad=\"  \"><span class=sf-dump-note>blogcategory.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">blogcategory.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130138464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.96966, "xdebug_link": null}, {"message": "[\n  ability => blog.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1035264873 data-indent-pad=\"  \"><span class=sf-dump-note>blog.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">blog.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035264873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.971075, "xdebug_link": null}, {"message": "[\n  ability => testimonial.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>testimonial.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">testimonial.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.972499, "xdebug_link": null}, {"message": "[\n  ability => trustedcompany.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>trustedcompany.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">trustedcompany.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.974126, "xdebug_link": null}, {"message": "[\n  ability => faq.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>faq.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">faq.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.976901, "xdebug_link": null}, {"message": "[\n  ability => language.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1603522117 data-indent-pad=\"  \"><span class=sf-dump-note>language.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">language.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603522117\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.979603, "xdebug_link": null}, {"message": "[\n  ability => country.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-275221420 data-indent-pad=\"  \"><span class=sf-dump-note>country.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">country.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275221420\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.981409, "xdebug_link": null}, {"message": "[\n  ability => state.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-977162195 data-indent-pad=\"  \"><span class=sf-dump-note>state.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">state.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977162195\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.983118, "xdebug_link": null}, {"message": "[\n  ability => city.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1319621724 data-indent-pad=\"  \"><span class=sf-dump-note>city.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">city.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319621724\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.984551, "xdebug_link": null}, {"message": "[\n  ability => backendsetting.menu,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-667209446 data-indent-pad=\"  \"><span class=sf-dump-note>backendsetting.menu </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">backendsetting.menu</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667209446\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.986025, "xdebug_link": null}]}, "session": {"_token": "hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/pages/edit/1?lang=en\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/assets/image/icon/apple-icon.svg\"\n]", "locale": "en", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/pages/edit/1?lang=en", "action_name": "admin.pages.edit", "controller_action": "App\\Http\\Controllers\\Backend\\PagesController@edit", "uri": "GET admin/pages/edit/{id}", "controller": "App\\Http\\Controllers\\Backend\\PagesController@edit<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=55\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/pages", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=55\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/PagesController.php:55-62</a>", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "duration": "741ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1663408548 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1663408548\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1790623703 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1790623703\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1044481210 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/pages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6IlA4VkJ4ZTl0YXd3Vzd6d1FvcHlmMnc9PSIsInZhbHVlIjoibkRFbVFoOTBSUXFCOTF2dldWQ0Vrd25tVldyR3pXS0R0S0VaNWFJUkVYNUNMY0xzSmRNS3FGRTYwMnN5K0NHNFlXeWc0QkN4TE9wVzlLczBPbk42N25qMkZjR2JDM2tQeUVIeGFEeWd1SC9TbXJhVm9HZjBUTTVVUm4ycFR4ZS8iLCJtYWMiOiI0ZGJkMWYxMmUwOTg1ZGMxZGVkOTNiMTQ2OTk4MDMxZjQ3ZWE0YjAxMWZlYThkNzljOTYyMjExOWJjM2Q1NmQzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkNxQ1dhdWlBZjF2OHhoRlgrbzFSOHc9PSIsInZhbHVlIjoiSERRSGNBTVZrQ1REcGpQNTdGRVdWMlc3V2ZJZkZwR1RXVHVDcUMydEgvSnNHR0ZhWGl1WXE3RnZQTk9PZ3FlYlFwemhJbERUWXkyRDU1ZjBlTVBPSUljS0o3SDErUXZuaGZxVERaZ2t4UkJRaXRBRmlNMnpKYU05WWhMWnFSVEciLCJtYWMiOiI0NDQzYmY5ZDY5Nzg0NDg5OGZhODgzOGJhZTdkNzQ2ZTM1OTM0Y2U1ZGVkZjFkMmFmMzJlMjUzY2ZjZjMyYTg1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044481210\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1841892430 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">73NVybykaJRblf91BMgO3Gbz1VJmhdzXGQr1QbOg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841892430\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1528755734 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 12:02:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1528755734\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-363837209 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/admin/pages/edit/1?lang=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">http://127.0.0.1:8000/assets/image/icon/apple-icon.svg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363837209\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/pages/edit/1?lang=en", "action_name": "admin.pages.edit", "controller_action": "App\\Http\\Controllers\\Backend\\PagesController@edit"}, "badge": null}}