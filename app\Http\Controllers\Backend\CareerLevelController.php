<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\CareerLevelRequest;
use App\Repositories\Admin\CareerLevelRepostitory;
use Illuminate\Http\Request;

class CareerLevelController extends Controller
{
    //

    public function __construct(protected CareerLevelRepostitory $careerLevel) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->careerLevel->content();
        }
        return view('admin.pages.career-level.index');
    }

    /** new Job Sub Category store
     * ========= store============
     *
     * @param CareerLevelRequest
     * @return Response
     */
    public function store(CareerLevelRequest $request)
    {

        if (!$this->hasPermissions(['careerlevel.add', 'careerlevel.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->careerLevel->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $careerLevel = $this->careerLevel->getById($id);
        $data['name'] = $careerLevel->getTranslation('name', Request()->lang);
        $data['id'] = $careerLevel->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['careerlevel.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->careerLevel->destroy($id);
        return $this->formatResponse($result);

    }

    /** Job Type status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['careerlevel.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->careerLevel->statusChange($id);
        return $this->formatResponse($result);

    }
}
