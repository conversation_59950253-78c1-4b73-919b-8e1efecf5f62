
<?php $__env->startSection('breadcrumb'); ?>
    <div class="breadcrumb-area">
        <h5><?php echo e(translate('Dashboard')); ?></h5>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>"><?php echo e(translate('Dashboard')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(translate('Education Lavel')); ?></li>
            </ol>
        </nav>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('main_content'); ?>
    <div class="main-content">
        <div class="row mb-3 g-4">
            <div class=" col-md-3">
                <div class="page-title text-md-start text-center">
                    <h4><?php echo e(translate('Education Lavel List')); ?></h4>
                </div>
            </div>
            <div class=" col-md-9 text-md-end text-center d-flex justify-content-md-end justify-content-center flex-row align-items-center flex-wrap gap-4">
                <button id="createNewBlogCategory" type="button" class="eg-btn btn--primary back-btn add-btn "> <img src="<?php echo e(asset('backend/assets/images/icons/add-icon.svg')); ?>" alt=""> <?php echo e(translate('Create Education')); ?> </button>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="table-wrapper">
                    <table id="datatable" class="display" style="width:100%">
                        <thead>
                        <tr class="block-header">
                            <th><?php echo e(translate('Id')); ?></th>
                            <th><?php echo e(translate('Name')); ?></th>
                            <th><?php echo e(translate('Status')); ?></th>
                            <th class="lang-name-list">
                                <?php $__currentLoopData = language(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <img src="<?php echo e(asset(LanguageImage() . $lang->thumb)); ?>"  width="20" class="img-rounded"  />
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </th>
                            <th><?php echo e(translate('Action')); ?></th>
                        </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php echo $__env->make('admin.pages.education-lavel.create', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('post_scripts'); ?>
    <?php echo $__env->make('js.admin.education', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\empzio\resources\views/admin/pages/education-lavel/index.blade.php ENDPATH**/ ?>