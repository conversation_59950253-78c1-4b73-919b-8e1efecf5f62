<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\JobInformationRequest;
use App\Models\Admin\Job;
use App\Models\Admin\JobSkill;
use App\Repositories\JobRepository;
use Illuminate\Http\Request;

class JobController extends Controller
{
    public function __construct(protected JobRepository $job) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->job->content();
        }

        return view('admin.pages.job.index');
    }

    /** new  resource create form
     * ========== create ==========
     *
     * @return view
     */
    public function create()
    {
        return view('admin.pages.job.create');
    }

    /** new  resource create form
     *
     *
     * @return Response
     */
    public function jobInformation(JobInformationRequest $request)
    {
        if (!$this->hasPermissions(['job.add'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->job->jobPrimaryInformation($request);

        if (!$result['status']) {
            return $this->formatResponse($result);
        }

        if ($result['prefix'] === 'additional-filed') {
            $job = $this->job->getById($result['jobId']);

            $previewContent = view('admin.pages.job.preview-job', [
                'job'            => $job,
                'types'          => $job->jobJobTypes,
                'jobLevels'      => $job->jobLevels,
                'jobExperiences' => $job->experiences,
                'jobRole'        => $job?->jobRole?->select('id', 'name')->first(),
                'skills'         => $job->skills->map(fn($skill) => [
                    'id'   => $skill->id,
                    'name' => $skill->getTranslation('name', $request->lang),
                ]),
                'lang'           => $request->lang
            ])->render();

            return response()->json([
                'status'  => true,
                'message' => $result['message'],
                'jobId'   => $result['jobId'],
                'prefix'  => $result['prefix'],
                'preview' => $previewContent
            ]);
        }

        return response()->json([
            'status'  => true,
            'message' => $result['message'],
            'jobId'   => $result['jobId'],
            'prefix'  => $result['prefix']
        ]);

    }


    public function skillSearch(Request $request)
    {

        if (!empty($request->q )) {
            $skills = JobSkill::where('name', 'like', "%{$request->q}%")->select('id', 'name')->get();
            $skillTranslate = [];
            foreach ($skills as $skill) {
                $skillTranslate[] = [
                    'id' => $skill->id,
                    'name' => $skill->getTranslation('name', $request->lang),
                ];
            }
            return response()->json(['items' => $skillTranslate]);
        }
        return response()->json(Request()->q);

    }

    /** custom field delete by job id
     *
     *======= customFieldDelete ========
     *
     * @param int id
     * @return Response
     */
    public function customFieldDelete($id, Request $request)
    {

        if (!$this->hasPermissions(['job.custom.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->job->customFieldDelete($id);
        return $this->formatResponse($result);

    }

    /**
     * edit
     *
     * @param  int  $id
     * @return Response
     */
    public function edit($id, Request $request)
    {
        $lang = Request()->lang;
        if (!$this->hasPermissions(['job.edit'])) {
            toastr()->error('Error!', translate('You have no permission'));
            return redirect()->back();
        }
        $job = $this->job->getById($id);
        $types = $job?->jobJobTypes;
        $jobExperiences = $job?->experiences;
        $role = $job?->jobRole?->select('id', 'name')->first();

        $jobRole = [];

        $jobRole = [
            'id' => $role?->id,
            'name' => $role?->getTranslation('name', $lang),
        ];

        $jobRole = json_encode($jobRole);

        $skills = [];
        foreach ($job->skills as $skill) {
            $skills[] = [
                'id' => $skill->id,
                'name' => $skill->getTranslation('name', $lang),
            ];
        }
        $skills = json_encode($skills);

        $jobLevels = [];
        foreach ($job->jobLevels as $jobLevel) {
            $jobLevels[] = [
                'id' => $jobLevel?->id,
                'name' => $jobLevel?->getTranslation('name', $lang),
            ];
        }

        return view('admin.pages.job.edit', compact('job', 'types', 'jobLevels', 'jobExperiences', 'jobRole', 'skills', 'lang'));
    }

    public function delete($id, Request $request)
    {
        if (!$this->hasPermissions(['job.delete'])) {
            return $this->permissionDeniedResponse();
        }

        if (!$id) {
            return redirect()->back();
        }

        $data = Job::with('jobCustomFields')->find($id);

        if (!$data) {
            return response()->json(['success' => false]);
        }

        $data->jobCustomFields()->delete();
        $data->delete();
        return response()->json(['success' => true]);

    }

    /** company  status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id, Request $request)
    {

        if (!$this->hasPermissions(['job.status'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->job->statusChange($id);
        return  $this->formatResponse($result);
    }

    /** company  status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function jobFeatured($id, Request $request)
    {

        if (!$this->hasPermissions(['job.featured'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->job->jobFeatured($id);
        return  $this->formatResponse($result);


    }
}
