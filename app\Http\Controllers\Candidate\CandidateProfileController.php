<?php

namespace App\Http\Controllers\Candidate;

use App\Classes\FileUpload;
use App\Http\Controllers\Controller;
use App\Http\Requests\CandidateBasicRequest;
use App\Http\Requests\CandidateRequest;
use App\Http\Requests\PasswordRequest;
use App\Models\Admin\Candidate;
use App\Models\User;
use App\Repositories\CandidateRepository;
use App\Repositories\UserRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CandidateProfileController extends Controller
{
    public function __construct(
        protected CandidateRepository $candidate,
        protected UserRepository $user,
    ) {}

    /** =======profile details=========
     *
     * =========== profile ========
     *
     * @return Response
     */
    public function profile()
    {
        $candidate = User::where('id', Auth::user()->id)->with('candidate')->first();

        return view('front.pages.candidate.candidate-profile', compact('candidate'));
    }

    /** ************profile update
     *
     * ========= profileUpdate =======
     *
     * @param Request
     */
    public function profileUpdate(CandidateRequest $request)
    {

        $result = $this->candidate->update($request->id, $request);
        if ($result['status'] == true) {
            return response()->json(['status' => true,  'message' => $result['message']]);
        }

        return response()->json(['status' => false,  'message' => $result['message']]);

    }

    /** social Link Update
     *===== socialLinkUpdate=====
     *
     * @param Request
     * @return Response
     */
    public function socialLinkUpdate(Request $request)
    {

        $result = $this->candidate->socialUpdate($request);
        return $this->formatResponse($result);

    }

    /** resumeEdit
     *
     * ========= resumeEdit =========
     *
     * @return View
     */
    public function resumeEdit()
    {
        $authEmail = Auth::user()->email;
        $user = User::with('candidate')->where('email', $authEmail)->with('candidate')->first();
        $candidate = Candidate::with('user', 'profession', 'candidateSkills')->where('user_id', Auth::user()->id)->first();

        return view('front.pages.candidate.edit-resume', compact('user', 'candidate'));
    }

    /**
     * skillRemove
     *
     * @param  int  $id
     */
    public function skillRemove($id)
    {
        $result = $this->candidate->skillRemove($id);
        if ($result['status']) {
            return response()->json(['status' => true]);
        }

        return response()->json(['status' => false]);
    }

    /**
     * candidateSettings
     *
     * @return void
     */
    public function candidateSettings()
    {
        $authEmail = Auth::user()->email;
        $user = User::with('company')->where('email', $authEmail)->with('candidate')->first();

        return view('front.pages.candidate.candidate-settings', compact('user'));
    }

    /**
     * resumeUpdate
     *
     * @param  mixed  $request
     * @return Response
     */
    public function resumeUpdate(CandidateBasicRequest $request)
    {
        $result = $this->candidate->resumeUpdate($request);
        return $this->formatResponse($result);

    }

    /**
     * changePassword
     *
     * @param  mixed  $request
     * @return Response
     */
    public function changePassword(PasswordRequest $request)
    {
        $result = $this->user->changePassword($request);
         if($result['status'] == false){
            return $this->formatResponse($result);
         }


        toastr()->success(translate('Password changed successfully'));
        return response()->json([
            'status' => true,
            'type' => true,
            'url'=>route('auth.user.login')
        ]);

    }

    /**
     * candidatePhoneEmailChange
     */
    public function candidatePhoneEmailChange(Request $request)
    {
        $user = User::find(Auth::id());
        if (!$user) {
            toastr()->error('User not found', '', ['positionClass' => 'toast-top-right']);
            return redirect()->back();
        }

        $userUpdateData = [
            'email' => $request->email,
            'secondary_email' => $request->secondary_email,
        ];

        $candidateUpdateData = [
            'candidate_phone_number' => $request->candidate_phone_number,
            'candidate_secondary_phone_number' => $request->candidate_secondary_phone_number,
            'candidate_present_address' => $request->candidate_present_address,
        ];

        if ($user->update($userUpdateData)) {
            $candidate = Candidate::where('user_id', $user->id)->first();

            if ($candidate && $candidate->update($candidateUpdateData)) {
                toastr()->success(translate('Successfully updated!'), '', ['positionClass' => 'toast-top-right']);
            } else {
                toastr()->error(translate('Candidate data not updated'), '', ['positionClass' => 'toast-top-right']);
            }
        } else {
            toastr()->error(translate('Email and secondary email not updated'), '', ['positionClass' => 'toast-top-right']);
        }

        return redirect()->back();
    }

    /**
     * resumeView
     *
     * @return View
     */
    public function resumeView()
    {

        $candidate = Candidate::with('user', 'profession', 'candidateSkills')->where('user_id', Auth::user()->id)->first();

        return view('front.pages.candidate.view-resume', compact('candidate'));
    }

    /**
     * resumePdfDownload
     *
     * @return response
     */
    public function resumePdfDownload()
    {
        $candidate = Candidate::with('user', 'profession', 'candidateSkills')
            ->where('user_id', Auth::user()->id)
            ->orWhere('id', Auth::user()->id)
            ->first();

        return view('front.pages.candidate.pdf-view-resume', compact('candidate'));

    }

    /** bookmark Jobs
     *
     * ======== bookmarkJobs =========
     *
     * @return Response
     */
    public function bookmarkJobs()
    {
        $bookmarkJobs = $this->candidate->bookmarkJobs(5);

        return view('front.pages.candidate.bookmark-job', compact('bookmarkJobs'));
    }

    /** applied jobs List
     *
     * ======== appliedJobsList =========
     *
     * @return Response
     */
    public function appliedJobsList()
    {
        $applyJobs = $this->candidate->applyJobs('', 5);
        return view('front.pages.candidate.candidate-applied-jobs', compact('applyJobs'));
    }

    /** apply Jobs
     *
     * ======== applyJob =========
     *
     * @return Response
     */
    public function applyJob(Request $request)
    {


        try {

            $applyJob =$this->candidate->candidateJobApply($request);
             if($applyJob['status'] !== true){
                return response()->json($applyJob);
             }
             return response()->json([
                'status' => true,
                'job' => 'apply',
                'message' => translate('Job Apply Successfully')
            ]);


        } catch (\Throwable $th) {
            //throw $th;
            return \response()->json(['status' => false,  'message' => $th->getMessage()]);
        }

    }

    /** base64 image upload
     * ============ imageUpload ========
     *
     * @param Request
     * @return Response
     */
    public function applyJobFileupload(Request $request)
    {
        try {
            $fileName = FileUpload::base64ImgUpload($request->image, $file = $request->old_file ? $request->old_file : '', $folder = 'applyfiles');

            return response()->json(['status' => true, 'image_name' => $fileName]);
        } catch (\Throwable $th) {
            return response()->json(['status' => false]);
        }
    }

    /**
     * notifications
     */
    public function notifications(Request $request)
    {

        $notifications = $request->user()->notifications()->latest()->paginate(10);

        return view('front.pages.candidate.notification', compact('notifications'));

    }

    /**
     * readAllNotificaiton
     */
    public function readAllNotificaiton()
    {
        auth()->user()->unreadNotifications->markAsRead();
        toastr()->success('Notification Read All Successfully');

        return redirect()->back();

    }

    /**
     * notificationReadUnreadById
     *
     * @param  int  $id
     */
    public function notificationReadUnreadById($id, Request $request)
    {

        $notification = $request->user()->notifications()->find($id);
        $notification->read_at = Carbon::now();
        $notification->update();
        toastr()->success('Notification Read Successfully');

        return redirect()->route('candidate.all.notification');

    }

    /**
     * notificationDelete
     *
     * @param  int  $id
     */
    public function notificationDelete($id, Request $request)
    {
        $request->user()->notifications()->where('id', $id)->delete();
        toastr()->success('Notification Delete Successfully');

        return redirect()->back();
    }
}
