

<?php
$pageLink=getThemeOption('page_setting')?? [];
$pageLink =  $pageLink['job_list_url'] ??  'javascript:void(0)';



 $blogs =$blogs ?? [];
?>

<div class="row g-lg-4 gy-5 justify-content-center">
    <?php if(count($blogs) > 0): ?>
        <?php $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $blog_details = substr($blog->blog_details, 0, 250);
                $blog_details = strip_tags($blog_details);

                $dateFormate= customDateFormate($blog->created_at, $format = 'd') ;
                $dateFormate2 =customDateFormate($blog->created_at, $format = 'M') ;
           
            ?>

            <div class="col-md-12 mb-30">
                <div class="recent-article-wrap">
                    <div class="recent-article-img">
                        <?php if(fileExists('blog', $blog->blog_thumbnail) == true && $blog->blog_thumbnail !== ''): ?>
                            <img class="img-fluid" src="<?php echo e(asset('storage/blog/' . $blog->blog_thumbnail)); ?>"
                                alt="">
                        <?php else: ?>
                            <img class="img-fluid" src="<?php echo e(asset('frontend/assets/images/416-240.png')); ?>" alt="">
                        <?php endif; ?>

                        <div class="publish-area d-xl-none d-flex">
                            <a href="<?php echo e(route('blog.details', $blog->slug)); ?>"
                                ><span> <?php echo e($dateFormate); ?></span><?php echo e($dateFormate2); ?>

                            </a>
                        </div>
                    </div>
                    <div class="recent-article-content">
                        <div class="recent-article-meta">
                            <div class="publish-area">
                                <a href="<?php echo e(route('blog.details', $blog->slug)); ?>"><span>
                                        <?php echo e($dateFormate); ?> </span> <?php echo e($dateFormate2); ?> </a>
                            </div>
                            <ul>
                                <li><a href="<?php echo e(route('blog.details', $blog->slug)); ?>">
                                    <img src="<?php echo e(asset('frontend/assets/images/icon/comment.svg')); ?>" alt="">
                                        <?php echo e($blog->comments_count); ?>  <?php echo e($blog->comments_count >1 ? translate('Comments') : translate('Comment')); ?> </a></li>

                                <li><a href="<?php echo e(route('blog.details', $blog->slug)); ?>">
                                    <img src="<?php echo e(asset('frontend/assets/images/icon/user.svg')); ?>" alt=""><?php echo e($blog->author_name); ?></a>
                                </li>
                            </ul>
                        </div>
                        <h3><a href="<?php echo e(route('blog.details', $blog->slug)); ?>"><?php echo e($blog->getTranslation('blog_title')); ?></a>
                        </h3>
                         <a href="<?php echo e(route('blog.details', $blog->slug)); ?>">  <p><?php echo e(clean( $blog_details)); ?>.</p></a>
                        <div class="explore-btn">
                            <a href="<?php echo e(route('blog.details', $blog->slug)); ?>"><span>
                                <img src="<?php echo e(asset('frontend/assets/images/icon/explore-elliose.svg')); ?>" alt=""></span> <?php echo e(translate('Explore More')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <div class="col-lg-12 d-flex justify-content-center pt-20">
            <div class="pagination-area">
                <?php echo e($blogs->links('front.vendor.pagination.custom')); ?>

            </div>
        </div>
     <?php else: ?>
      <?php echo $__env->make('front.pages.not-found', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
</div>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/pages/blog/standard-content.blade.php ENDPATH**/ ?>