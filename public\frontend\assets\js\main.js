(function ($) {
  "use strict";

  // sticky header
  window.addEventListener("scroll", function () {
    const header = document.querySelector("header.header-area");
    if (header) {
      header.classList.toggle("sticky", window.scrollY > 0);
    }
  });

  const sidebarButton = document.querySelector(".sidebar-button");

  if (sidebarButton) {
    sidebarButton.addEventListener("click", () => {
      document.querySelector(".main-menu").classList.toggle("show-menu");
    });
  }

  $(".menu-close-btn").on("click", function () {
    $(".main-menu").removeClass("show-menu");
  });

  jQuery(".dropdown-icon, .category-dropdown-icon").on("click", function () {
    jQuery(this)
      .toggleClass("active")
      .next("ul, .mega-menu, .mega-menu2")
      .slideToggle();
    jQuery(this)
      .parent()
      .siblings()
      .children("ul, .mega-menu, .mega-menu2")
      .slideUp();
    jQuery(this).parent().siblings().children(".active").removeClass("active");
  });
  // Serch Btn
  $(".search-btn, .lang-btn, .currency-btn").on("click", function (e) {
    let parent = $(this).parent();

    parent
      .find(".search-input, .lang-card, .currency-card")
      .toggleClass("active");

    e.stopPropagation();
  });
  $(document).on("click", function (e) {
    if (
      !$(e.target).closest(
        ".search-input, .search-btn, .lang-btn, .currency-btn"
      ).length
    ) {
      $(".search-input, .lang-card, .currency-card").removeClass("active");
    }
  });
  $(".serch-close").on("click", function (e) {
    $(".search-input").removeClass("active");
  });

  $(document).on("click", function (e) {
    if (
      !$(e.target).closest(
        ".search-input, .search-btn, .currency-btn, .currency-btn"
      ).length
    ) {
      $(".search-input, .currency-card, .currency-card").removeClass("active");
    }
  });
  $(".serch-close").on("click", function (e) {
    $(".search-input").removeClass("active");
  });

  // mobile dropdown

  jQuery(".category-area").on("click", function () {
    jQuery(this).toggleClass("active");
  });

  // popup on load
  setTimeout(function () {
    $("#myModal").modal("show");
  }, 500);
  

  // Home One Location
  var swiper = new Swiper(".location-slider", {
    slidesPerView: 4,
    spaceBetween: 24,
    loop: true,
    speed: 1500,
    //  autoplay: {
    //   delay: 2500, // Autoplay duration in milliseconds
    //   disableOnInteraction: false,
    // },
    pagination: {
			el: ".swiper-pagination1",
			clickable: true,
		},
    breakpoints: {
      280: {
        slidesPerView: 1,
        spaceBetween: 15,
      },
      480: {
        slidesPerView: 1,
      },
      768: {
        slidesPerView: 2,
      },
      992: {
        slidesPerView: 3,
      },
      1200: {
        slidesPerView: 4,
      },
      1400: {
        slidesPerView: 4,
      },
      1600: {
        slidesPerView: 4,
      },
    },
  });
  var swiper = new Swiper(".testimonial-slider", {
    slidesPerView: 1,
    speed: 1500,
    spaceBetween: 30,
    loop: true,
    effect: "fade", // Use the fade effect
    fadeEffect: {
      crossFade: true, // Enable cross-fade transition
    },
    autoplay: {
      delay: 2500, // Autoplay duration in milliseconds
      disableOnInteraction: false,
    },
    navigation: {
      nextEl: ".testimonial-slider-next",
      prevEl: ".testimonial-slider-prev",
    },
    pagination: {
      el: ".franctional-pagi",
      type: "fraction",
    },
  });

//Counter up
	$('.counter').counterUp({
		delay: 10,
		time: 1000
	});

  jQuery(".category-area").on("click", function () {
    jQuery(this).toggleClass("active");
  });

  $(".category-btn").on("click", function (e) {
    e.stopPropagation();
    $(".category-menu-list").toggleClass("active");
  });
  $(document).on("click", function (e) {
    if (!$(e.target).closest(".category-menu-list, .category-btn").length) {
      $(".category-menu-list").removeClass("active");
    }
  });
  $(".category-menu-close").on("click", function (e) {
    $(".category-menu-list").removeClass("active");
  });
  //Select wrap
  $(".select-wrap").on("click", function () {
    $(this).addClass("selected").siblings().removeClass("selected");
  });

  //Quantity Increment
  $(".quantity__minus").on("click", function (e) {
    e.preventDefault();
    var input = $(this).siblings(".quantity__input");
    var value = parseInt(input.val(), 10);
    if (value > 1) {
      value--;
    }
    input.val(value.toString().padStart(2, "0"));
  });
  $(".quantity__plus").on("click", function (e) {
    e.preventDefault();
    var input = $(this).siblings(".quantity__input");
    var value = parseInt(input.val(), 10);
    value++;
    input.val(value.toString().padStart(2, "0"));
  });
 
  //list grid view
  $(".grid-view li").on("click", function () {
    // Get the class of the clicked li element
    var clickedClass = $(this).attr("class");
    // Extract the class name without "item-" prefix
    var className = clickedClass.replace("item-", "");
    // Add a new class to the target div and remove old classes
    var targetDiv = $(".list-grid-product-wrap");
    targetDiv
      .removeClass()
      .addClass("list-grid-product-wrap " + className + "-wrapper");
    // Remove the 'selected' class from siblings and add it to the clicked element
    $(this).siblings().removeClass("active");
    $(this).addClass("active");
  });
  // Video Popup
  $('[data-fancybox="gallery"]').fancybox({
    buttons: ["close"],
    loop: false,
    protect: true,
  });
  $(".video-player").fancybox({
    buttons: ["close"],
    loop: false,
    protect: true,
  });

  // Language Btn
  $(".language-btn").on("click", function (e) {
    let parent = $(this).parent();
    parent.find(".language-list").toggleClass("active");
    e.stopPropagation();
  });
  $(document).on("click", function (e) {
    if (!$(e.target).closest(".language-btn").length) {
      $(".language-list").removeClass("active");
    }
  });

  //wow js
  jQuery(window).on("load", function () {
    new WOW().init();
    window.wow = new WOW({
      boxClass: "wow",
      animateClass: "animated",
      offset: 0,
      mobile: true,
      live: true,
      offset: 80,
    });
    window.wow.init();
  });

  // niceSelect
  if ($("select").length) {
    $("select").niceSelect();
  }

})(jQuery);
