<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\CompanyTypeRepostitory;
use App\Repositories\CheckoutRepository;
use App\Repositories\PaymentRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class CheckoutController extends Controller
{
    //

    protected $gateway;

    public function __construct(protected CompanyTypeRepostitory $companyType, protected CheckoutRepository $checkOut, protected PaymentRepository $payment) {}

    /**
     * checkOut
     *
     * @return view
     */
    public function checkOut()
    {

        if (!authCheck() || !authCheck()->hasRole('company')) {
            toastr()->error(translate('Need to Login as Company'));
            return redirect()->back();
        }

        if (request()->filled(['packageId', 'packageQty'])) {
            $this->checkOut->checkOut(request());
            return view('front.pages.checkout.checkout');
        }
        return view('front.pages.404');

    }

    /**
     * payment
     *
     * @param  mixed  $request
     * @return Response
     */
    public function payment(Request $request)
    {

        $validator = $this->validation($request);
        if ($request->ajax()) {
            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ]);
            }
            $result = $this->payment->paymentContent($request);
            if ($result['status'] == true) {
                return response()->json([
                    'status' => true,
                    'message' => $result['message'],
                    'type' => $result['type'],
                     'url' => $result['url']
                    ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => $result['message']
                ]);
            }

        } else {
            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            }
            try {

                $response = $this->payment->paypalPayment($request);
                if ($response->isRedirect()) {
                    $response->redirect();
                } else {
                    return redirect()->back()->with('error', $response->getMessage());
                }

            } catch (\Throwable $th) {
                return redirect()->back()->with('error', $th->getMessage());
            }

        }
    }

    /**
     * success
     *
     * @param  mixed  $request
     * @return Response
     */
    public function success(Request $request)
    {

        $response = $this->payment->success($request, Session::get('billing_information'));
        if ($response['status'] == true) {
            toastr()->success($response['message']);

            return redirect($response['url']);
        } else {
            toastr()->error($response['message']);
        }

    }

    public function transactionSuccess($subscription)
    {

        $subscription = $this->payment->transactionSuccess($subscription);

        return view('front.pages.success', compact('subscription'));
    }

    /** validation http field validation
     *
     *
     * @param mixed  request
     * @return response error message
     */
    protected function validation($request)
    {
        $validator = Validator::make($request->all(), [

            'first_name' => 'required',
            'last_name' => 'required',
            'phone' => 'required',
            'email' => 'required|email|unique:users,id,'.$request->id,
            'company_name' => 'required',
            'company_location' => 'required',
            'card_number' => $request->method == 'stripe' ? 'required' : '',
            'cvc' => $request->method == 'stripe' ? 'required' : '',
            'expiry' => $request->method == 'stripe' ? 'required' : '',
            'method' => 'required',
            'terms_condition' => 'required',
        ], [

            'first_name.required' => 'First Name Field is required',
            'last_name.required' => 'Last Name Field is required',
            'email.required' => 'Email Field is required',
            'email.unique' => 'A user with this email address already exists.',
            'company_name.required' => 'Company Name Field is required',
            'company_location.required' => 'Company Location Field is required',
            'card_number.required' => 'Card Number Field is required',
            'cvc.required' => 'CVC is required',
            'expiry.required' => 'Expiry is required',
            'method.required' => 'Select Payment',
            'terms_condition.required' => 'Checked Terms Condition',
        ]);

        return $validator;
    }

    /**
     * paypalCancel
     *
     * @return View
     */
    public function paypalCancel()
    {
        toastr()->error(translate('Your Transection is declined'));
        return redirect()->back();
    }
}
