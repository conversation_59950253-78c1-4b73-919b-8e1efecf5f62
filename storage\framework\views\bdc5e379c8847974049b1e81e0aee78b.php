<?php
$generalSetting= getThemeOption('general') ?? [];
?>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title>  <?php echo e($generalSetting['application_name'] ??  translate('Jobes -Job Portal Laravel Application')); ?> </title>

    <link rel="icon" href="<?php echo e(asset('frontend/assets/images/fav.svg')); ?>" type="image/gif" sizes="20x20">

    <!-- bootstrap 5 -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/bootstrap.min.css')); ?>">
    <!-- box-icon -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/boxicons.min.css')); ?>">
    <!-- bootstrap icon -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/bootstrap-icons.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/bootstrap-icons.min.css')); ?>">
    <!-- animate css -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/animate.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/jquery.fancybox.min.css')); ?>">
    <!-- nice-select -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/nice-select.css')); ?>">
    <!--swiper css -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/swiper-bundle.min.css')); ?>">
    <!-- style css -->
    <?php echo $__env->yieldPushContent('post_styles'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/style.css')); ?>">

   <?php echo $__env->make('css.theme-custom-css', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</head>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/layouts/includes/head.blade.php ENDPATH**/ ?>