
<?php $__env->startSection('breadcrumb'); ?>
    <div class="breadcrumb-area">
        <h5><?php echo e(translate('Dashboard')); ?></h5>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>"><?php echo e(translate('Dashboard')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(translate('Company Edit')); ?></li>
            </ol>
        </nav>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('main_content'); ?>
    <div class="main-content">
        <div class="row mb-35">
            <div class="page-title d-flex justify-content-between align-items-center">
                <h4><?php echo e(translate('Edit Company')); ?></h4>
                <a href="<?php echo e(route('admin.company')); ?>" class="eg-btn btn--primary back-btn"> <img
                        src="<?php echo e(asset('backend/assets/images/icons/back.svg')); ?>" alt=""> <?php echo e(translate('Go Back')); ?>

                </a>
            </div>
        </div>
        <form method="POST" class="add-form" action="<?php echo e(route('admin.company.update')); ?>" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="id" value="<?php echo e($company->id); ?>">
            <input type="hidden" name="lang" value="<?php echo e($lang); ?>">
            <input type="hidden" name="user_id" value="<?php echo e($company->user->id); ?>">

            <div class="row">
                <div class="col-sm-9">


                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card-header dashboard-header"> <?php echo e(translate('Owner Information')); ?> </div>
                            <div class="eg-card product-card">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-inner mb-25">
                                            <label> <?php echo e(translate('First Name')); ?><span>*</span> </label>
                                            <input type="text" class="username-input" name="first_name"
                                                value="<?php echo e($company->user->first_name); ?>">
                                            <span class="text-danger error-text first_name_err"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-25">
                                            <label> <?php echo e(translate('Last Name')); ?><span>*</span></label>
                                            <input type="text" class="username-input" name="last_name"
                                                value="<?php echo e($company->user->last_name); ?>">
                                            <span class="text-danger error-text last_name_err"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-25">
                                            <label><?php echo e(translate('Phone')); ?></label>
                                            <input type="text" class="username-input" name="phone"
                                                value="<?php echo e($company->user->phone); ?>">
                                            <span class="text-danger error-text phone_err"></span>
                                        </div>
                                    </div>


                                    <div class="col-md-4">
                                        <div class="form-inner mb-25">
                                            <label><?php echo e(translate('Email')); ?> <span>*</span></label>
                                            <input type="email" class="username-input" name="email"
                                                value="<?php echo e($company->user->email); ?>">
                                            <span class="text-danger error-text email_err"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="col-lg-12">
                            <div class="card-header dashboard-header"><?php echo e(translate('Company Info')); ?> </div>
                            <div class="eg-card product-card">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Company Name')); ?> <span>*</span></label>
                                            <input type="text" class="username-input" name="company_name"
                                                placeholder="<?php echo e(translate('Enter Your Name')); ?>"
                                                value="<?php echo e($company->company_name); ?>">
                                            <span class="text-danger error-text company_name_err"></span>
                                        </div>
                                    </div>


                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Email')); ?> <span>*</span></label>
                                            <input type="text" class="username-input" name="company_email"
                                                placeholder="<?php echo e(translate('Enter Your Email')); ?>"
                                                value="<?php echo e($company->company_email); ?>">
                                            <span class="text-danger error-text company_email_err"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Phone')); ?> <span>*</span></label>
                                            <input type="text" class="username-input" name="company_phone_number"
                                                placeholder="<?php echo e(translate('Enter Your Phone')); ?>"
                                                value="<?php echo e($company->company_phone_number); ?>">
                                            <span class="text-danger error-text company_phone_number_err"></span>
                                        </div>
                                    </div>



                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Company Size')); ?> <span>*</span></label>
                                            <input type="number" class="username-input" name="company_size"
                                                placeholder="<?php echo e(translate('Enter Your Company Size')); ?>"
                                                value="<?php echo e($company->company_size); ?>">
                                            <span class="text-danger error-text company_size_err"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label> <?php echo e(translate('Category Type')); ?> <span>*</span></label>
                                            <select class="form-select" name="company_type" id="company_type_id">
                                                <option disabled selected> <?php echo e(translate('Open this select menu')); ?>

                                                </option>
                                                <?php $__currentLoopData = CompanyType(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $c_type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($c_type->company_type_name); ?>"
                                                        <?php echo e($c_type->id === $company->company_type_id ? 'selected' : ''); ?>>
                                                        <?php echo e($c_type->getTranslation('company_type_name', $lang)); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <span class="text-danger error-text company_type_err"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Website')); ?></label>
                                            <input type="text" class="username-input" name="company_website"
                                                placeholder="<?php echo e(translate('Enter Your Website Name')); ?>"
                                                value="<?php echo e($company->company_website); ?>">

                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Establishment Date')); ?></label>
                                            <input type="date" class="username-input" name="establishment_date"
                                                value="<?php echo e($company->establishment_date); ?>">

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="card-header dashboard-header"><?php echo e(translate('Company Location')); ?> </div>
                            <div class="eg-card product-card">
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="form-inner mb-25">
                                            <label><?php echo e(translate('Country')); ?> <span>*</span></label>
                                            <select class="form-control mb-2 mb-md-0 countryName" name="country_id"
                                                id="countryList">
                                                <option disabled selected> </option>
                                                <?php $__currentLoopData = countryAll(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($country->id); ?>"
                                                        <?php echo e($country->id == $company->country_id ? 'selected' : ' '); ?>>
                                                        <?php echo e($country->name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <span class="text-danger error-text country_id_err"></span>
                                        </div>
                                    </div>

                                    <div class="col-lg-4">
                                        <div class="form-inner mb-25">
                                            <label><?php echo e(translate('State')); ?> <span>*</span></label>
                                            <select class="form-control mb-2 mb-md-0 stateName" name="state_id"
                                                id="stateList">
                                                <option disabled selected> </option>

                                            </select>
                                            <span class="text-danger error-text state_id_err"></span>
                                        </div>
                                    </div>

                                    <div class="col-lg-4">
                                        <div class="form-inner mb-25">
                                            <label><?php echo e(translate('City')); ?> <span>*</span></label>
                                            <select class="form-control mb-2 mb-md-0" name="city_id" id="cityList">
                                                <option disabled selected> </option>
                                            </select>
                                            <span class="text-danger error-text city_id_err"></span>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Postal code')); ?> <span>*</span></label>
                                            <input type="text" class="username-input" name="postal_code"
                                                placeholder="<?php echo e(translate('Enter Your Postal code')); ?>"
                                                value="<?php echo e($company->postal_code); ?>">
                                            <span class="text-danger error-text postal_code_err"></span>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Latitude')); ?></label>
                                            <input type="text" class="username-input" name="Latitude"
                                                placeholder="<?php echo e(translate('Enter Your Latitude')); ?>"
                                                value="<?php echo e($company->Latitude); ?>">

                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Longitude')); ?></label>
                                            <input type="text" class="username-input" name="Longitude"
                                                placeholder="<?php echo e(translate('Enter Your Longitude')); ?>"
                                                value="<?php echo e($company->Longitude); ?>">

                                        </div>
                                    </div>


                                    <div class="col-md-6">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Address')); ?> <span>*</span></label>
                                            <input type="text" class="username-input" name="address"
                                                placeholder="<?php echo e(translate('Enter Your address')); ?>"
                                                value="<?php echo e($company->address); ?>">
                                            <span class="text-danger error-text address_err"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Location Iframe Limk')); ?> <span>*</span></label>
                                            <input type="text" class="username-input" name="iframe_link"
                                                placeholder="<?php echo e(translate('Enter Your Iframe Limk')); ?>"
                                                value="<?php echo e($company->iframe_link); ?>">
                                            <span class="text-danger error-text iframe_link_err"></span>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <div class="card-header dashboard-header"><?php echo e(translate('Company Overview')); ?> </div>
                            <div class="eg-card product-card p-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-inner mb-33">
                                            <label><?php echo e(translate('Overview')); ?> <span>*</span></label>
                                            <textarea id="summernote" name="company_details"><?php echo e(clean($company->company_details)); ?></textarea>
                                            <span class="text-danger error-text company_details_err"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-inner mb-33">
                                            <label><?php echo e(translate('Vision')); ?> <span>*</span></label>
                                            <textarea class="description" name="company_vision"><?php echo e(clean($company->company_vision)); ?></textarea>
                                            <span class="text-danger error-text company_vision_err"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <div class="card-header dashboard-header"><?php echo e(translate('Company Social Option')); ?> </div>
                            <div class="eg-card product-card">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Facebook')); ?></label>
                                            <input type="text" class="username-input" name="company_facebook"
                                                placeholder="<?php echo e(translate('Enter Your Facebook Link')); ?>"
                                                value="<?php echo e($company->company_facebook); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Twitter')); ?></label>
                                            <input type="text" class="username-input" name="company_twitter"
                                                placeholder="<?php echo e(translate('Enter Your Twitter Link')); ?>"
                                                value="<?php echo e($company->company_twitter); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Linkedin')); ?></label>
                                            <input type="text" class="username-input" name="company_linkedin"
                                                placeholder="<?php echo e(translate('Enter Your Linkedin Link')); ?>"
                                                value="<?php echo e($company->company_linkedin); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label><?php echo e(translate('Pinterest')); ?></label>
                                            <input type="text" class="username-input" name="company_pinterest"
                                                placeholder="<?php echo e(translate('Enter Your Pinterest Link')); ?>"
                                                value="<?php echo e($company->company_pinterest); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label> <?php echo e(translate('Dribble')); ?> </label>
                                            <input type="text" class="username-input" name="company_dribble"
                                                placeholder="<?php echo e(translate('Enter Your Dribble Link')); ?>"
                                                value="<?php echo e($company->company_dribble); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-inner mb-35">
                                            <label> <?php echo e(translate('Instagram')); ?></label>
                                            <input type="text" class="username-input" name="company_behance"
                                                placeholder="<?php echo e(translate('Enter Your Instagram Link')); ?>"
                                                value="<?php echo e($company->company_behance); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-12">
                            <div class="card-header dashboard-header"><?php echo e(translate('Working Type')); ?> </div>
                            <div class="company_working_area eg-card product-card" id="companyWorkingAreaAppend">
                                <?php if($company->workAreas->count() > 0): ?>
                                    <?php $__currentLoopData = $company->workAreas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $workarea): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="row item-row">
                                            <div class="col-sm-12">
                                                <div class="row">
                                                    <div class="col-sm-10">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="form-inner mb-25">
                                                                    <label
                                                                        for="working-field"><?php echo e(translate('Working Field')); ?></label>
                                                                    <div class="input-area">
                                                                        <input type="text" id="company_working_field"
                                                                            name="working_field[<?php echo e($key); ?>][working_field]"
                                                                            value="<?php echo e($workarea['working_field']); ?>" />
                                                                        <input
                                                                            type="hidden"name="working_field[<?php echo e($key); ?>][id]"
                                                                            value="<?php echo e($workarea['id']); ?>" />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-inner mb-25">
                                                                    <label><?php echo e(translate('Add Icon')); ?></label>
                                                                    <div class="input-area">
                                                                        <input type="file"
                                                                            name="working_field[<?php echo e($key); ?>][working_image]">
                                                                    </div>
                                                                    <?php if($workarea['working_image'] !== ''): ?>
                                                                        <img width="80px"
                                                                            src="<?php echo e(asset('storage/' . CompanyImage() . '/icons/' . $workarea['working_image'])); ?>"
                                                                            id="target3" />
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div class="form-inner mb-40">
                                                                    <label
                                                                        for="description"><?php echo e(translate('Short Description')); ?></label>
                                                                    <textarea name="working_field[<?php echo e($key); ?>][working_description]" id="company_working_description"><?php echo $workarea['working_description']; ?></textarea>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <div
                                                            class="d-flex align-items-center justify-content-between mb-50">
                                                            <div class="add-row">
                                                                <button type="button"
                                                                    data-key-index="<?php echo e($key); ?>"
                                                                    id="removeWorkingArea"
                                                                    data-action="<?php echo e(route('admin.company.work.delete', $workarea['id'])); ?>"
                                                                    data-id="<?php echo e($workarea['id']); ?>"
                                                                    class="remove"><?php echo e(translate('Remove Working Area')); ?></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="row">
                                                <div class="col-sm-10">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-inner mb-25">
                                                                <label
                                                                    for="working-field"><?php echo e(translate('Working Field')); ?></label>
                                                                <div class="input-area">
                                                                    <input type="text" id="company_working_field"
                                                                        name="working_field[1][working_field]">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-inner mb-25">
                                                                <label><?php echo e(translate('Add Icon')); ?></label>
                                                                <div class="input-area">
                                                                    <input type="file"
                                                                        name="working_field[1][working_image]">
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="form-inner mb-40">
                                                                <label
                                                                    for="description"><?php echo e(translate('Short Description')); ?></label>
                                                                <textarea name="working_field[1][working_description]" id="company_working_description"></textarea>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-2">
                                                    <div class="d-flex align-items-center justify-content-between mb-50">
                                                        <div class="add-row">
                                                            <button type="button"
                                                                class="add-working-btn eg-btn btn--primary back-btn"><?php echo e(translate('Add')); ?></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">

                    <div class="card-header dashboard-header"> Password Change Option </div>
                    <div class="eg-card">
                        <div class="form-inner mb-25">
                            <label><?php echo e(translate('New Password')); ?> <span>*</span></label>
                            <input type="password" class="username-input" name="password" placeholder="********">

                        </div>

                        <div class="form-inner mb-25">
                            <label> <?php echo e(translate('Confirm Password')); ?> <span>*</span></label>
                            <input type="password" class="username-input" name="confirm_password"
                                placeholder="********">

                            <span class="text-danger error-text confirm_password_err"></span>
                        </div>
                    </div>



                    <div class="card-header dashboard-header"> Image </div>
                    <div class="eg-card product-card">
                        <div class="form-inner file-upload mb-35">
                            <label class="control-label"><?php echo e(translate('Logo')); ?> <b>(60px × 60px)</b>
                                <span>*</span></label>
                            <div class="dropzone-wrapper">
                                <div class="dropzone-desc">
                                    <i class="glyphicon glyphicon-download-alt"></i>
                                    <p><?php echo e(translate('Choose an image file or drag it here')); ?></p>
                                </div>
                                <input type="file" name="company_logo" class="dropzone dropzone-image">
                                <span class="text-danger error-text company_logo_err"></span>
                            </div>
                            <div class="preview-zone hidden">
                                <div class="box box-solid">
                                    <div class="box-body">

                                        <?php if($company->company_logo != null): ?>
                                            <div class="img-thumb-wrapper card shadow"> <span class="remove text-danger">
                                                    <i class="bi bi-trash"></i> </span>
                                                <img class="img-thumb" width="100"
                                                    src="<?php echo e(asset('storage/' . CompanyImage() . '/' . $company->company_logo)); ?>" />
                                            </div>
                                        <?php endif; ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="eg-card product-card">
                        <div class="form-inner file-upload mb-35">
                            <label class="control-label"><?php echo e(translate('Cover Photo')); ?> <b>(1296px × 322px)</label>
                            <div class="dropzone-wrapper">
                                <div class="dropzone-desc">
                                    <i class="glyphicon glyphicon-download-alt"></i>
                                    <p><?php echo e(translate('Choose an image file or drag it here')); ?></p>
                                </div>
                                <input type="file" name="company_cover_photo" class="dropzone dropzone-image">
                                <span class="text-danger error-text company_cover_photo_err"></span>
                            </div>

                            <div class="preview-zone hidden">
                                <div class="box box-solid">
                                    <div class="box-body">
                                        <?php if(!is_null($company->company_cover_photo)): ?>
                                            <div class="img-thumb-wrapper card shadow"> <span class="remove text-danger">
                                                    <i class="bi bi-trash"></i> </span>
                                                <img class="img-thumb" width="100"
                                                    src="<?php echo e(asset('storage/' . CompanyImage() . '/' . $company->company_cover_photo)); ?>" />
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="eg-card product-card">
                        <div class="form-inner img-upload mb-35">

                            <label class="control-label"><?php echo e(translate('Image Gallery')); ?> <b>(900px × 600px)</label>

                            <div class="dropzone-wrapper">
                                <div class="dropzone-desc">
                                    <i class="glyphicon glyphicon-download-alt"></i>
                                    <p><?php echo e(translate('Choose image files or drag it here')); ?></p>
                                </div>
                                <input type="file" name="company_gallery_image[]" class="dropzone galleryImage"
                                    multiple>
                                <span class="text-danger error-text company_gallery_image_err"></span>
                            </div>

                            <div class="gallery-preview-zone hidden">
                                <div class="box box-solid">
                                    <div class="box-body">
                                        <?php if($company->companygalleryimage->count() > 0): ?>
                                            <?php $__currentLoopData = $company->companygalleryimage; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="img-thumb-wrapper card shadow"> <span
                                                        class="text-danger gallery-remove"
                                                        data-action="<?php echo e(route('admin.company.gallery.delete', $image->id)); ?>">
                                                        <i class="bi bi-trash"></i> </span>
                                                    <img class="img-thumb" width="100" src="<?php echo e(asset('storage/' . CompanyGalleryImageShow() . '/' . $image->company_gallery_image)); ?>" />
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="eg-card product-card">
                        <div class="button-group mt-15">
                            <button type="submit"
                                class="eg-btn btn--green medium-btn me-3"><?php echo e(translate('Update')); ?></button>
                        </div>
                    </div>
                </div>

            </div>
        </form>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('post_scripts'); ?>
<script src="<?php echo e(asset('backend/assets/js/custom.js')); ?>"></script>
    <?php echo $__env->make('js.admin.company', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('js.company.company-edit', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('js.custom', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\empzio\resources\views/admin/pages/company/edit.blade.php ENDPATH**/ ?>