
<?php $__env->startSection('breadcrumb'); ?>
    <div class="breadcrumb-area">
        <h5><?php echo e(translate('Dashboard')); ?></h5>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>"><?php echo e(translate('Dashboard')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(translate('Admin Role')); ?></li>
            </ol>
        </nav>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('main_content'); ?>
    <div class="main-content">
        <div class="row mb-3 g-4">
            <div class=" col-md-3">
                <div class="page-title text-md-start text-center">
                    <h4> <?php echo e(translate('User List')); ?></h4>
                </div>
            </div>
            <div class=" col-md-9 text-md-end text-center d-flex justify-content-md-end justify-content-center flex-row align-items-center flex-wrap gap-4">
                <a href="<?php echo e(route('admin.create.admin')); ?>" class="eg-btn btn--primary back-btn"><img src="<?php echo e(asset('backend/assets/images/icons/add-icon.svg')); ?>" alt=""> Add Admin</a>
            </div>
        </div>
        <div class="table-wrapper" id="srcDataShow">
            <table class="eg-table table blog-table">
                <thead>
                <tr>
                    <th><?php echo e(translate('Name')); ?></th>
                    <th><?php echo e(translate('Email')); ?></th>
                    <th><?php echo e(translate('Role')); ?></th>
                    <th><?php echo e(translate('Action')); ?></th>
                </tr>
                </thead>
                <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tbody>
                    <tr>
                        <td data-label="Title"><?php echo e($item->first_name.' '.$item->last_name); ?></td>
                        <td data-label="Title"><?php echo e($item->email); ?></td>
                        <td data-label="Title">
                            <?php $__currentLoopData = $item->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo e($data->name); ?>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </td>
                        <td data-label="Action">
                            <div class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                <a href="<?php echo e(route('admin.edit.admin',$item->id)); ?>" class="eg-btn add--btn"><i class="bi bi-pencil-square"></i></a>
                                <a href="<?php echo e(route('admin.delete.admin',$item->id)); ?>" class="eg-btn delete--btn"><i class="bi bi-trash"></i></a>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </table>
        </div>
    </div>
    <!-- footer-area -->
    <div class="footer">
        <nav aria-label="Page navigation example">
            <ul class="pagination gap-3">
                <li class="page-item"><a class="page-link" href="#"><i class='bx bxs-chevron-left'></i></a></li>
                <li class="page-item"><a class="page-link" href="#">01</a></li>
                <li class="page-item"><a class="page-link" href="#">02</a></li>
                <li class="page-item"><a class="page-link" href="#"><i class='bx bx-chevron-right'></i></a></li>
            </ul>
        </nav>
    </div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('admin.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\jobes-application\resources\views/admin/pages/user/index.blade.php ENDPATH**/ ?>