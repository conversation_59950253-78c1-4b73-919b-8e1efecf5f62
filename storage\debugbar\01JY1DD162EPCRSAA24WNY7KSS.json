{"__meta": {"id": "01JY1DD162EPCRSAA24WNY7KSS", "datetime": "2025-06-18 11:39:46", "utime": **********.242608, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.093387, "end": **********.242624, "duration": 3.1492371559143066, "duration_str": "3.15s", "measures": [{"label": "Booting", "start": **********.093387, "relative_start": 0, "end": **********.311715, "relative_end": **********.311715, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.311724, "relative_start": 0.****************, "end": **********.242626, "relative_end": 1.9073486328125e-06, "duration": 2.****************, "duration_str": "2.93s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.320962, "relative_start": 0.*****************, "end": **********.326628, "relative_end": **********.326628, "duration": 0.005666017532348633, "duration_str": "5.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.420264, "relative_start": 0.****************, "end": **********.24078, "relative_end": **********.24078, "duration": 2.****************, "duration_str": "2.82s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 23, "nb_templates": 23, "templates": [{"name": "1x front.pages.eg_job", "param_count": null, "params": [], "start": **********.422467, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/eg_job.blade.phpfront.pages.eg_job", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Feg_job.blade.php&line=1", "ajax": false, "filename": "eg_job.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.eg_job"}, {"name": "1x front.pages.widgets.hero-section", "param_count": null, "params": [], "start": **********.466455, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.phpfront.pages.widgets.hero-section", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Fhero-section.blade.php&line=1", "ajax": false, "filename": "hero-section.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.hero-section"}, {"name": "1x front.pages.widgets.latest-jobs-category", "param_count": null, "params": [], "start": **********.668626, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.phpfront.pages.widgets.latest-jobs-category", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Flatest-jobs-category.blade.php&line=1", "ajax": false, "filename": "latest-jobs-category.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.latest-jobs-category"}, {"name": "1x front.pages.widgets.featured-jobs", "param_count": null, "params": [], "start": **********.373632, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.phpfront.pages.widgets.featured-jobs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Ffeatured-jobs.blade.php&line=1", "ajax": false, "filename": "featured-jobs.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.featured-jobs"}, {"name": "1x front.pages.widgets.working-process", "param_count": null, "params": [], "start": **********.634627, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/working-process.blade.phpfront.pages.widgets.working-process", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Fworking-process.blade.php&line=1", "ajax": false, "filename": "working-process.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.working-process"}, {"name": "1x front.pages.widgets.dream-location", "param_count": null, "params": [], "start": **********.743151, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/dream-location.blade.phpfront.pages.widgets.dream-location", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Fdream-location.blade.php&line=1", "ajax": false, "filename": "dream-location.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.dream-location"}, {"name": "1x front.pages.widgets.reviews", "param_count": null, "params": [], "start": **********.856342, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/reviews.blade.phpfront.pages.widgets.reviews", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Freviews.blade.php&line=1", "ajax": false, "filename": "reviews.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.reviews"}, {"name": "1x front.pages.widgets.trusted-company", "param_count": null, "params": [], "start": **********.967696, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/trusted-company.blade.phpfront.pages.widgets.trusted-company", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Ftrusted-company.blade.php&line=1", "ajax": false, "filename": "trusted-company.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.trusted-company"}, {"name": "1x front.pages.widgets.top-recruiters", "param_count": null, "params": [], "start": **********.041056, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/top-recruiters.blade.phpfront.pages.widgets.top-recruiters", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Ftop-recruiters.blade.php&line=1", "ajax": false, "filename": "top-recruiters.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.top-recruiters"}, {"name": "1x front.layouts.master", "param_count": null, "params": [], "start": **********.197093, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/master.blade.phpfront.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.master"}, {"name": "1x front.layouts.includes.head", "param_count": null, "params": [], "start": **********.19749, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/head.blade.phpfront.layouts.includes.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.head"}, {"name": "1x css.theme-custom-css", "param_count": null, "params": [], "start": **********.19885, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/css/theme-custom-css.blade.phpcss.theme-custom-css", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fcss%2Ftheme-custom-css.blade.php&line=1", "ajax": false, "filename": "theme-custom-css.blade.php", "line": "?"}, "render_count": 1, "name_original": "css.theme-custom-css"}, {"name": "1x front.layouts.includes.header", "param_count": null, "params": [], "start": **********.199419, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/header.blade.phpfront.layouts.includes.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.header"}, {"name": "1x front.layouts.includes.top", "param_count": null, "params": [], "start": **********.199974, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/top.blade.phpfront.layouts.includes.top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.top"}, {"name": "1x front.layouts.includes.logo", "param_count": null, "params": [], "start": **********.203287, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/logo.blade.phpfront.layouts.includes.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.logo"}, {"name": "1x front.layouts.includes.menu", "param_count": null, "params": [], "start": **********.20409, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/menu.blade.phpfront.layouts.includes.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.menu"}, {"name": "1x front.layouts.includes.mobile", "param_count": null, "params": [], "start": **********.207376, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/mobile.blade.phpfront.layouts.includes.mobile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.mobile"}, {"name": "1x front.layouts.includes.right-menu", "param_count": null, "params": [], "start": **********.214251, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.phpfront.layouts.includes.right-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=1", "ajax": false, "filename": "right-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.right-menu"}, {"name": "1x front.layouts.includes.company-menu", "param_count": null, "params": [], "start": **********.222977, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/company-menu.blade.phpfront.layouts.includes.company-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fcompany-menu.blade.php&line=1", "ajax": false, "filename": "company-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.company-menu"}, {"name": "1x front.layouts.includes.footer", "param_count": null, "params": [], "start": **********.226909, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/footer.blade.phpfront.layouts.includes.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.footer"}, {"name": "1x front.layouts.includes.script", "param_count": null, "params": [], "start": **********.228697, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/script.blade.phpfront.layouts.includes.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.script"}, {"name": "1x js.translate", "param_count": null, "params": [], "start": **********.229068, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/translate.blade.phpjs.translate", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftranslate.blade.php&line=1", "ajax": false, "filename": "translate.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.translate"}, {"name": "1x js.theme-custom-js", "param_count": null, "params": [], "start": **********.239881, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/js/theme-custom-js.blade.phpjs.theme-custom-js", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Fjs%2Ftheme-custom-js.blade.php&line=1", "ajax": false, "filename": "theme-custom-js.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.theme-custom-js"}]}, "route": {"uri": "GET /", "middleware": "web, xss", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "home", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:17-33</a>"}, "queries": {"count": 78, "nb_statements": 78, "nb_visible_statements": 78, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.054879999999999984, "accumulated_duration_str": "54.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `pages` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\HomeRepository.php", "line": 16}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.412232, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HomeRepository.php:16", "source": {"index": 16, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\HomeRepository.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FHomeRepository.php&line=16", "ajax": false, "filename": "HomeRepository.php", "line": "16"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 0.929}, {"sql": "select * from `widgets_contents` where `widgets_contents`.`page_id` in (1) and `status` = 1 order by `position` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\HomeRepository.php", "line": 16}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.41542, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "HomeRepository.php:16", "source": {"index": 21, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\HomeRepository.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FHomeRepository.php&line=16", "ajax": false, "filename": "HomeRepository.php", "line": "16"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0.929, "width_percent": 6.122}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 3 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 4}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.595577, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 7.052, "width_percent": 6.214}, {"sql": "select count(`id`) as aggregate from `jobs` where `job_deadline` >= '2025-06-18 11:39:43'", "type": "query", "params": [], "bindings": ["2025-06-18 11:39:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.6223621, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1228", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1228}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1228", "ajax": false, "filename": "Helper.php", "line": "1228"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 13.265, "width_percent": 0.802}, {"sql": "select count(`id`) as aggregate from `companies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1235}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.624783, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1235", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1235}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1235", "ajax": false, "filename": "Helper.php", "line": "1235"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 14.067, "width_percent": 0.656}, {"sql": "select count(`id`) as aggregate from `candidates`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1242}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.626551, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1242", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1242}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1242", "ajax": false, "filename": "Helper.php", "line": "1242"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 14.723, "width_percent": 0.638}, {"sql": "select count(`id`) as aggregate from `jobs` where `job_deadline` >= '2025-06-11 11:39:43'", "type": "query", "params": [], "bindings": ["2025-06-11 11:39:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1249}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.628042, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1249", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1249", "ajax": false, "filename": "Helper.php", "line": "1249"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 15.361, "width_percent": 0.893}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 426}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.631658, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Helper.php:426", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=426", "ajax": false, "filename": "Helper.php", "line": "426"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 16.254, "width_percent": 1.166}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 1 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.633972, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 17.42, "width_percent": 4.173}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 2 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6382232, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 21.593, "width_percent": 1.494}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 5 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.640184, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 23.087, "width_percent": 1.002}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 6 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.641685, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 24.089, "width_percent": 0.929}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 7 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.64314, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 25.018, "width_percent": 0.911}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 8 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.644576, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 25.929, "width_percent": 0.911}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 9 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.646007, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 26.84, "width_percent": 0.911}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 10 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.647444, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 27.751, "width_percent": 0.911}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 11 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.648885, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 28.663, "width_percent": 0.911}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 12 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.650328, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 29.574, "width_percent": 0.911}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 13 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.651916, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 30.485, "width_percent": 0.948}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 14 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.653415, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 31.432, "width_percent": 1.057}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 15 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.655701, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 32.489, "width_percent": 1.348}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 16 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.657461, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 33.837, "width_percent": 0.911}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 17 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.658904, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 34.749, "width_percent": 0.875}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 18 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6603281, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 35.623, "width_percent": 0.856}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 19 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6617231, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 36.48, "width_percent": 0.856}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 20 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.663124, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 37.336, "width_percent": 0.856}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 34 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6645272, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 38.192, "width_percent": 0.856}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 35 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6659071, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 39.049, "width_percent": 0.893}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 111 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.768478, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 39.942, "width_percent": 1.075}, {"sql": "select `categories`.*, (select count(*) from `jobs` where `categories`.`id` = `jobs`.`category_id`) as `jobs_count` from `categories` where `status` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 616}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3289, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Helper.php:616", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 616}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=616", "ajax": false, "filename": "Helper.php", "line": "616"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 41.017, "width_percent": 1.257}, {"sql": "select `id`, `company_id`, `category_id`, `job_deadline`, `status` from `jobs` where `jobs`.`category_id` in (13, 14, 15, 16, 17, 18, 19, 20, 34, 35) and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `status` = 1 and `job_deadline` >= '2025-06-18 11:39:45'", "type": "query", "params": [], "bindings": [1, 1, 1, 1, "2025-06-18 11:39:45"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 616}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3342679, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Helper.php:616", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 616}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=616", "ajax": false, "filename": "Helper.php", "line": "616"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 42.274, "width_percent": 1.385}, {"sql": "select `id`, `company_name`, `slug`, `status` from `companies` where `companies`.`id` in (26, 35, 39, 43, 44, 46, 55, 63) and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 616}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3362272, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Helper.php:616", "source": {"index": 25, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 616}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=616", "ajax": false, "filename": "Helper.php", "line": "616"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 43.659, "width_percent": 0.856}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 35 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.346184, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 44.515, "width_percent": 0.838}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 34 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.34911, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 45.353, "width_percent": 0.656}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 20 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.351543, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 46.009, "width_percent": 0.601}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 19 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.3542001, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 46.611, "width_percent": 0.893}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 18 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.357012, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 47.504, "width_percent": 0.82}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 17 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.3596358, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 48.324, "width_percent": 0.765}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 16 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.362152, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 49.089, "width_percent": 0.929}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 15 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.3648791, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 50.018, "width_percent": 0.82}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 14 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.367497, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 50.838, "width_percent": 0.784}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 13 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.370088, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 51.622, "width_percent": 0.838}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 5 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.563745, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 52.46, "width_percent": 1.148}, {"sql": "select `jobs`.*, (select `name` from `countries` where `jobs`.`country_id` = `countries`.`id`) as `country_name`, (select `name` from `cities` where `jobs`.`city_id` = `cities`.`id`) as `city_name` from `jobs` where exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `status` = 1 and `job_deadline` >= '2025-06-18 11:39:45' and `job_featured` = 1 order by `id` desc limit 6", "type": "query", "params": [], "bindings": [1, 1, 1, 1, "2025-06-18 11:39:45", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.565854, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "Helper.php:650", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=650", "ajax": false, "filename": "Helper.php", "line": "650"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 53.608, "width_percent": 2.879}, {"sql": "select `id`, `company_name`, `slug`, `status` from `companies` where `companies`.`id` in (26, 46, 63, 65) and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.568798, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Helper.php:650", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=650", "ajax": false, "filename": "Helper.php", "line": "650"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 56.487, "width_percent": 1.148}, {"sql": "select * from `job_job_types` where `job_job_types`.`job_id` in (21, 24, 25, 27, 29, 32)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5715032, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Helper.php:650", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=650", "ajax": false, "filename": "Helper.php", "line": "650"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 57.635, "width_percent": 2.114}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 32 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.5760982, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 59.749, "width_percent": 0.838}, {"sql": "select * from `users` where `id` = 216 limit 1", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 53}], "start": **********.5807889, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 60.587, "width_percent": 0.711}, {"sql": "select `jobs`.*, `book_mark_jobs`.`user_id` as `pivot_user_id`, `book_mark_jobs`.`job_id` as `pivot_job_id`, `book_mark_jobs`.`created_at` as `pivot_created_at`, `book_mark_jobs`.`updated_at` as `pivot_updated_at` from `jobs` inner join `book_mark_jobs` on `jobs`.`id` = `book_mark_jobs`.`job_id` where `book_mark_jobs`.`user_id` in (216)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.584784, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Helper.php:83", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=83", "ajax": false, "filename": "Helper.php", "line": "83"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 61.297, "width_percent": 2.132}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 29 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.59325, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 63.429, "width_percent": 0.856}, {"sql": "select `jobs`.*, `book_mark_jobs`.`user_id` as `pivot_user_id`, `book_mark_jobs`.`job_id` as `pivot_job_id`, `book_mark_jobs`.`created_at` as `pivot_created_at`, `book_mark_jobs`.`updated_at` as `pivot_updated_at` from `jobs` inner join `book_mark_jobs` on `jobs`.`id` = `book_mark_jobs`.`job_id` where `book_mark_jobs`.`user_id` in (216)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.594856, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Helper.php:83", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=83", "ajax": false, "filename": "Helper.php", "line": "83"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 64.286, "width_percent": 0.765}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 27 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.600714, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 65.051, "width_percent": 0.692}, {"sql": "select `jobs`.*, `book_mark_jobs`.`user_id` as `pivot_user_id`, `book_mark_jobs`.`job_id` as `pivot_job_id`, `book_mark_jobs`.`created_at` as `pivot_created_at`, `book_mark_jobs`.`updated_at` as `pivot_updated_at` from `jobs` inner join `book_mark_jobs` on `jobs`.`id` = `book_mark_jobs`.`job_id` where `book_mark_jobs`.`user_id` in (216)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.6022131, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:83", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=83", "ajax": false, "filename": "Helper.php", "line": "83"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 65.743, "width_percent": 0.893}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 25 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.608503, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.636, "width_percent": 0.966}, {"sql": "select `jobs`.*, `book_mark_jobs`.`user_id` as `pivot_user_id`, `book_mark_jobs`.`job_id` as `pivot_job_id`, `book_mark_jobs`.`created_at` as `pivot_created_at`, `book_mark_jobs`.`updated_at` as `pivot_updated_at` from `jobs` inner join `book_mark_jobs` on `jobs`.`id` = `book_mark_jobs`.`job_id` where `book_mark_jobs`.`user_id` in (216)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.61016, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Helper.php:83", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=83", "ajax": false, "filename": "Helper.php", "line": "83"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 67.602, "width_percent": 0.966}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 24 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.615976, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 68.568, "width_percent": 0.82}, {"sql": "select `jobs`.*, `book_mark_jobs`.`user_id` as `pivot_user_id`, `book_mark_jobs`.`job_id` as `pivot_job_id`, `book_mark_jobs`.`created_at` as `pivot_created_at`, `book_mark_jobs`.`updated_at` as `pivot_updated_at` from `jobs` inner join `book_mark_jobs` on `jobs`.`id` = `book_mark_jobs`.`job_id` where `book_mark_jobs`.`user_id` in (216)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.617536, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Helper.php:83", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=83", "ajax": false, "filename": "Helper.php", "line": "83"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 69.388, "width_percent": 0.82}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 21 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.625143, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 70.208, "width_percent": 0.838}, {"sql": "select `jobs`.*, `book_mark_jobs`.`user_id` as `pivot_user_id`, `book_mark_jobs`.`job_id` as `pivot_job_id`, `book_mark_jobs`.`created_at` as `pivot_created_at`, `book_mark_jobs`.`updated_at` as `pivot_updated_at` from `jobs` inner join `book_mark_jobs` on `jobs`.`id` = `book_mark_jobs`.`job_id` where `book_mark_jobs`.`user_id` in (216)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.627017, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Helper.php:83", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=83", "ajax": false, "filename": "Helper.php", "line": "83"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 71.046, "width_percent": 1.057}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 96 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.working-process", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/working-process.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.729537, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 72.103, "width_percent": 1.221}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 105 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [105], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.dream-location", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/dream-location.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.8411071, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 73.324, "width_percent": 1.184}, {"sql": "select `name`, `location_image`, (select `name` from `countries` where `cities`.`country_id` = `countries`.`id`) as `country_name`, (select count(*) from `jobs` where `cities`.`id` = `jobs`.`city_id`) as `jobs_count` from `cities` where exists (select `id`, `city_id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `cities`.`id` = `jobs`.`city_id` and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `job_deadline` >= '2025-06-18 11:39:45' and `status` = 1) and `status` = 1 limit 6", "type": "query", "params": [], "bindings": [1, 1, 1, "2025-06-18 11:39:45", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 742}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.84393, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "Helper.php:742", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=742", "ajax": false, "filename": "Helper.php", "line": "742"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 74.508, "width_percent": 3.043}, {"sql": "select `id`, `city_id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `jobs`.`city_id` in (0) and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `job_deadline` >= '2025-06-18 11:39:45' and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, 1, "2025-06-18 11:39:45", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 742}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.847142, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Helper.php:742", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=742", "ajax": false, "filename": "Helper.php", "line": "742"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.551, "width_percent": 1.221}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 8 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.reviews", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/reviews.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.9463859, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 78.772, "width_percent": 1.367}, {"sql": "select * from `testimonials` where `status` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 783}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.962788, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "Helper.php:783", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 783}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=783", "ajax": false, "filename": "Helper.php", "line": "783"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.138, "width_percent": 3.426}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 106 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.trusted-company", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/trusted-company.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.02336, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 83.564, "width_percent": 1.057}, {"sql": "select * from `trusted_companies` where `status` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 763}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.037419, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "Helper.php:763", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 763}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=763", "ajax": false, "filename": "Helper.php", "line": "763"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 84.621, "width_percent": 3.116}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 10 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.top-recruiters", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/widgets/top-recruiters.blade.php", "line": 7}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.169045, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 87.737, "width_percent": 1.184}, {"sql": "select `id`, `company_name`, `company_type_id`, `user_id`, `company_logo`, `slug`, `status`, (select `name` from `countries` where `companies`.`country_id` = `countries`.`id`) as `country_name`, (select `name` from `cities` where `companies`.`city_id` = `cities`.`id`) as `city_name`, (select `name` from `states` where `companies`.`state_id` = `states`.`id`) as `state_name`, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id`) as `jobs_count`, (select sum(`jobs`.`job_vacancy`) from `jobs` where `companies`.`id` = `jobs`.`company_id`) as `jobs_sum_job_vacancy` from `companies` where exists (select `id`, `status`, `is_email_verified` from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and exists (select `id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `companies`.`id` = `jobs`.`company_id` and `job_deadline` >= '2025-06-18 11:39:46' and `status` = 1) and `status` = 1 order by `jobs_sum_job_vacancy` desc limit 6", "type": "query", "params": [], "bindings": [1, 1, "2025-06-18 11:39:46", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 684}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.174543, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Helper.php:684", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=684", "ajax": false, "filename": "Helper.php", "line": "684"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 88.921, "width_percent": 2.587}, {"sql": "select `id`, `status`, `is_email_verified` from `users` where `users`.`id` in (213, 214, 217, 225, 264, 271) and `status` = 1 and `is_email_verified` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.177099, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Helper.php:684", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=684", "ajax": false, "filename": "Helper.php", "line": "684"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 91.509, "width_percent": 0.893}, {"sql": "select `id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `jobs`.`company_id` in (45, 46, 48, 55, 63, 65) and `job_deadline` >= '2025-06-18 11:39:46' and `status` = 1", "type": "query", "params": [], "bindings": ["2025-06-18 11:39:46", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.178637, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Helper.php:684", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=684", "ajax": false, "filename": "Helper.php", "line": "684"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 92.402, "width_percent": 0.929}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.2014358, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.top:19", "source": {"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=19", "ajax": false, "filename": "top.blade.php", "line": "19"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 93.331, "width_percent": 0.747}, {"sql": "select * from `menu_items` where (`menu_id` = 2 and `parent_id` is null) order by `order` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.204571, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 94.078, "width_percent": 0.911}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (215, 216, 231, 232, 233, 235) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.2061172, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 94.989, "width_percent": 0.583}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (216) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionServiceProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\PermissionServiceProvider.php", "line": 89}, {"index": 24, "namespace": "view", "name": "front.layouts.includes.mobile", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/mobile.blade.php", "line": 5}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.209364, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 95.572, "width_percent": 1.057}, {"sql": "select * from `companies` where `companies`.`user_id` = 216 and `companies`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.215718, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.right-menu:2", "source": {"index": 21, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 2}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=2", "ajax": false, "filename": "right-menu.blade.php", "line": "2"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 96.629, "width_percent": 1.184}, {"sql": "select * from `candidates` where `candidates`.`user_id` = 216 and `candidates`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.218329, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.right-menu:3", "source": {"index": 22, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 3}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=3", "ajax": false, "filename": "right-menu.blade.php", "line": "3"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 97.813, "width_percent": 1.112}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\Admin\\\\Company' and `notifications`.`notifiable_id` = 47 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\Admin\\Company", 47], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 7}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.219743, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.right-menu:7", "source": {"index": 16, "namespace": "view", "name": "front.layouts.includes.right-menu", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/layouts/includes/right-menu.blade.php", "line": 7}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=7", "ajax": false, "filename": "right-menu.blade.php", "line": "7"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 98.925, "width_percent": 1.075}]}, "models": {"data": {"App\\Models\\Admin\\CategoryTranslation": {"value": 129, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategoryTranslation.php&line=1", "ajax": false, "filename": "CategoryTranslation.php", "line": "?"}}, "App\\Models\\Admin\\WidgetContentTranslation": {"value": 32, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetContentTranslation.php&line=1", "ajax": false, "filename": "WidgetContentTranslation.php", "line": "?"}}, "App\\Models\\Admin\\Category": {"value": 30, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Admin\\Job": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Admin\\Company": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Admin\\TrustedCompany": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FTrustedCompany.php&line=1", "ajax": false, "filename": "TrustedCompany.php", "line": "?"}}, "App\\Models\\Admin\\WidgetsContents": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=1", "ajax": false, "filename": "WidgetsContents.php", "line": "?"}}, "App\\Models\\Admin\\JobJobType": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJobJobType.php&line=1", "ajax": false, "filename": "JobJobType.php", "line": "?"}}, "App\\Models\\User": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\City": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCity.php&line=1", "ajax": false, "filename": "City.php", "line": "?"}}, "App\\Models\\Admin\\MenuItem": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Admin\\Testimonial": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FTestimonial.php&line=1", "ajax": false, "filename": "Testimonial.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin\\Pages": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FPages.php&line=1", "ajax": false, "filename": "Pages.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 293, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/company/job/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "216"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:17-33</a>", "middleware": "web, xss", "duration": "3.16s", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1605719167 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1605719167\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-349655697 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-349655697\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-146676944 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6InRNOEVQS0k3a01zdEsvc2V3eUVwdnc9PSIsInZhbHVlIjoiMnpjUVlVNE1nZmtWY2t4b2xXYkNlQUJ5NnNyT3czME5YWnhFaXA2NHhWVU9OQXd5eXNOWmJvbkdFeVVkOWgxZlI0WmhSN0RQVmFpSDVycGprRnBlc0RxYU8zVmJtODRDK2hEbXRRdmtTTjRUVEprcFRnTU9mRzlUNjZnR0VqVlciLCJtYWMiOiI5OGMxMGMxNzQwNjhjZjI3Mzg1MThlMTUyNjdhZmUzMjIwNjllNjZiZDg0YmI2OTI4YzMzODNlYWY3NDJlYzIxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ilh5WWc5T3lnK1d3dVJ1cEdUNHRqdHc9PSIsInZhbHVlIjoiVnpNTk84azVXOGVyNzJJRUYzand2cDdzb0U1U3EwbWpJeHFXc3lwRkZlSjhzSGlJMUNDWHhJc0J0OS9EbFZRMGZZMW1mbXJrT0dPUEdiazhMd05IS1VJUEdIOHJkRVd3YjlVSW9EQ3BwSDRNd0pCR0hpRHVkaEExSGwzWDFXMnAiLCJtYWMiOiJkOGVkZDMzODA0ODc2OGMwOTJlNjNlYzAxNzQzMmZhNGRlZGQ2ODliMmZkYTNmZGQ1MzRiMjVjM2Q3ZDc2MzhlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146676944\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1145806787 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tQf33pMyNyWOY8sdEZw5gl6WCpMho5WMM5teqzOL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145806787\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-93742576 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 11:39:43 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-93742576\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1927498566 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/company/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>216</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927498566\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index"}, "badge": null}}