<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class WidgetsContents extends Model
{
    use HasFactory;

    protected $fillable = [
        'page_id',
        'widget_slug',
        'ui_card_number',
        'widget_content',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'widget_content' => 'array',
    ];

    public function getTranslation($field = '', $lang = false)
    {
        $lang = $lang == false ? App::getLocale() : $lang;
        $widgetTranslations = $this->widgetTranslations->where('lang', $lang)->first();

        return $widgetTranslations != null ? $widgetTranslations->$field : $this->$field;
    }

    public function widgetTranslations()
    {
        return $this->hasMany(WidgetContentTranslation::class, 'widget_content_id', 'id');
    }

    public static function getSingleWidgetContent($cardId = '')
    {
        $singleWidgets = WidgetsContents::where(['ui_card_number' => $cardId])->first();
        if ($singleWidgets) {
            return $singleWidgets;
        }
         return WidgetsContents::get();

    }

    public function widget()
    {
        return $this->belongsTo(Widgets::class, 'widget_slug', 'widget_slug');
    }
}
