<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\CountryRequest;
use App\Repositories\Admin\CountryRepository;
use Illuminate\Http\Request;

class CountryController extends Controller
{
    //

    public function __construct(protected CountryRepository $country) {}

    /** Display the resource
     *========= index========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->country->content();
        }

        return view('admin.pages.country.index');
    }

    /** new resource store
     * ============ store =======
     *
     * @param Request
     * @return Response
     */
    public function store(CountryRequest $request)
    {
        if (!$this->hasPermissions(['country.add', 'country.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->country->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $country = $this->country->getById($id);
        $data['name'] = $country->name;
        $data['id'] = $country->id;
        $data['lang'] = Request()->lang;
        $data['code'] = $country->code;

        return response()->json($data);
    }

    /** category status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['country.status'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->country->statusChange($id);
        return $this->formatResponse($result);

    }
}
