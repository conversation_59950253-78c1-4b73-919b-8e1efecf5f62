<div class="modal fade ajaxModelSubCategory" id="ajaxModelSubCategory" tabindex="-1" role="dialog" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title title" id="staticBackdropLabel"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form class="form" action="<?php echo e(route('admin.subcategory.store')); ?>">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="id" id="id">
                        <input type="hidden" name="lang" id="lang">

                        <div class="col-12" data-select2-id="select2-data-9-1wva">
                            <div class="form-inner mb-25" data-select2-id="select2-data-8-u6ea">
                                <label> <?php echo e(translate('Category Name')); ?> <span>*</span></label>
                                <select class="form-select" name="category_id" id="category_id" aria-label="Default select example">
                                    <option selected disabled> <?php echo e(translate('Select Category Name')); ?></option>
                                    <?php $__currentLoopData = Category(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id); ?>"><?php echo e($category->getTranslation('category_name')); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>

                                <span class="text-danger error-text category_id_err"></span>
                            </div>
                        </div>
                        <div class="form-inner mb-35">
                            <label><?php echo e(translate('Sub Category Name')); ?> <span>*</span></label>
                            <input type="text" class="username-input" name="subcategory_name" id="subcategory_name"  placeholder="<?php echo e(translate('Enter Your Name')); ?>">
                            <span class="text-danger error-text subcategory_name_err"></span>
                        </div>
                        <div class="button-group d-flex justify-content-center mt-30">
                            <button type="submit" class="btn eg-btn btn--green medium-btn me-3 saveBtn"></button>
                        </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/admin/pages/subcategory/create.blade.php ENDPATH**/ ?>