<?php

namespace App\Http\Controllers\Backend;

use App\Classes\FileUpload;
use App\Http\Controllers\Controller;
use App\Models\Admin\ThemeOption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class ThemeOptionController extends Controller
{
    /** theme Option
     * ====== themeOption ==========
     *
     * @return View
     */
    public function themeOption()
    {
         Artisan::call('cache:clear');
        return view('admin.pages.theme-option.index');
    }

    /** Theme Setting
     *============ themeSetting ===========
     *
     * @return Response
     */
    public function themeSetting(Request $request)
    {

        try {
            if (!$this->hasPermissions(['themeoption.edit'])) {
                return $this->permissionDeniedResponse();
            }

            $data = json_encode($request->except('_method', '_token', 'key'));
            $theme = ThemeOption::updateOrCreate(
                ['key' => $request->key],
                ['value' => $data]
            );

            return response()->json([
                'status' => true,
                'message' => translate('Change Successfully'),
                'type' => true
            ]);

        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => translate('Something went wrong!')
            ]);
        }


    }

    /** base64 image upload
     * ============ imageUpload ========
     *
     * @param Request
     * @return Response
     */
    public function imageUpload(Request $request)
    {

        try {
            $fileName = FileUpload::base64ImgUpload($request->image, $file = $request->old_file ? $request->old_file : '', $folder = 'theme-options');
            return response()->json([
                'status' => true,
                'image_name' => $fileName
            ]);
        } catch (\Throwable $th) {
            return response()->json(['status' => false]);
        }

    }
}
