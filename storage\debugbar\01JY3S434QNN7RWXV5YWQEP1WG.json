{"__meta": {"id": "01JY3S434QNN7RWXV5YWQEP1WG", "datetime": "2025-06-18 21:43:05", "utime": **********.111813, "method": "GET", "uri": "/frontendassets/images/icon/apply-ellipse.svg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750326184.819986, "end": **********.111831, "duration": 0.29184484481811523, "duration_str": "292ms", "measures": [{"label": "Booting", "start": 1750326184.819986, "relative_start": 0, "end": **********.026879, "relative_end": **********.026879, "duration": 0.****************, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.026893, "relative_start": 0.*****************, "end": **********.111833, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "84.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.03715, "relative_start": 0.****************, "end": **********.042712, "relative_end": **********.042712, "duration": 0.0055620670318603516, "duration_str": "5.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.065272, "relative_start": 0.****************, "end": **********.109985, "relative_end": **********.109985, "duration": 0.*****************, "duration_str": "44.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 14, "nb_templates": 14, "templates": [{"name": "1x front.pages.404", "param_count": null, "params": [], "start": **********.067309, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/404.blade.phpfront.pages.404", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2F404.blade.php&line=1", "ajax": false, "filename": "404.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.404"}, {"name": "1x front.layouts.master", "param_count": null, "params": [], "start": **********.07237, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/master.blade.phpfront.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.master"}, {"name": "1x front.layouts.includes.head", "param_count": null, "params": [], "start": **********.072761, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/head.blade.phpfront.layouts.includes.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.head"}, {"name": "1x css.theme-custom-css", "param_count": null, "params": [], "start": **********.073269, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/css/theme-custom-css.blade.phpcss.theme-custom-css", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Fcss%2Ftheme-custom-css.blade.php&line=1", "ajax": false, "filename": "theme-custom-css.blade.php", "line": "?"}, "render_count": 1, "name_original": "css.theme-custom-css"}, {"name": "1x front.layouts.includes.header", "param_count": null, "params": [], "start": **********.073793, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/header.blade.phpfront.layouts.includes.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.header"}, {"name": "1x front.layouts.includes.top", "param_count": null, "params": [], "start": **********.081724, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/top.blade.phpfront.layouts.includes.top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.top"}, {"name": "1x front.layouts.includes.logo", "param_count": null, "params": [], "start": **********.085714, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/logo.blade.phpfront.layouts.includes.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.logo"}, {"name": "1x front.layouts.includes.menu", "param_count": null, "params": [], "start": **********.086209, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/menu.blade.phpfront.layouts.includes.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.menu"}, {"name": "1x front.layouts.includes.mobile", "param_count": null, "params": [], "start": **********.089768, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/mobile.blade.phpfront.layouts.includes.mobile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.mobile"}, {"name": "1x front.layouts.includes.right-menu", "param_count": null, "params": [], "start": **********.095189, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/right-menu.blade.phpfront.layouts.includes.right-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=1", "ajax": false, "filename": "right-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.right-menu"}, {"name": "1x front.layouts.includes.footer", "param_count": null, "params": [], "start": **********.09747, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/footer.blade.phpfront.layouts.includes.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.footer"}, {"name": "1x front.layouts.includes.script", "param_count": null, "params": [], "start": **********.099307, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/script.blade.phpfront.layouts.includes.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.script"}, {"name": "1x js.translate", "param_count": null, "params": [], "start": **********.099671, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/js/translate.blade.phpjs.translate", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Fjs%2Ftranslate.blade.php&line=1", "ajax": false, "filename": "translate.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.translate"}, {"name": "1x js.theme-custom-js", "param_count": null, "params": [], "start": **********.108776, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/js/theme-custom-js.blade.phpjs.theme-custom-js", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Fjs%2Ftheme-custom-js.blade.php&line=1", "ajax": false, "filename": "theme-custom-js.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.theme-custom-js"}]}, "route": {"uri": "GET {any}", "middleware": "web", "controller": "App\\Http\\Controllers\\Frontend\\PagesController@customPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PagesController.php:15-70</a>"}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00151, "accumulated_duration_str": "1.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `pages` where `page_route` = 'frontendassets/images/icon/apply-ellipse.svg' limit 1", "type": "query", "params": [], "bindings": ["frontendassets/images/icon/apply-ellipse.svg"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0635989, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:27", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=27", "ajax": false, "filename": "PagesController.php", "line": "27"}, "connection": "jobes", "explain": null, "start_percent": 0, "width_percent": 28.477}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.083609, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.top:19", "source": {"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=19", "ajax": false, "filename": "top.blade.php", "line": "19"}, "connection": "jobes", "explain": null, "start_percent": 28.477, "width_percent": 23.841}, {"sql": "select * from `menu_items` where (`menu_id` = 2 and `parent_id` is null) order by `order` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 580}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.0866542, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "jobes", "explain": null, "start_percent": 52.318, "width_percent": 29.139}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (215, 216, 231, 232, 233, 235) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 580}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.088191, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "jobes", "explain": null, "start_percent": 81.457, "width_percent": 18.543}]}, "models": {"data": {"App\\Models\\Admin\\MenuItem": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 11, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "locale": "in", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/storage/lang/flags/ae.png\"\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/frontendassets/images/icon/apply-ellipse.svg", "action_name": null, "controller_action": "App\\Http\\Controllers\\Frontend\\PagesController@customPage", "uri": "GET {any}", "controller": "App\\Http\\Controllers\\Frontend\\PagesController@customPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PagesController.php:15-70</a>", "middleware": "web", "duration": "293ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1270070305 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1270070305\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1575882756 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1575882756\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1951752238 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6InB4dzdGZXN5MkgycStxTDhzNFlGbmc9PSIsInZhbHVlIjoiMzJZWC9GU3hGV2h4dFlTSU5VV2M2VlBFNEkwZWFqOXFiM1I2WGtTSmtFckhDcUxiNVZyMjZlS2xQUWVxNm42cU5ha3VsVmRGdW1hMUpEbEd1YlhaUFVybjM4UU1kWFlpMldNMFpuYjc4akhCd2k2SnNLa2hqd25sbkh2NWlTK1AiLCJtYWMiOiI2MGEwMWMzZDM0MGQxZjVlYTE0M2VkNGNjZmUxZTE0Yzc2NDZmMDc2OTgxMGFhN2QzZGFiMzIzNzk0M2YzNDI0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkFibjBEUGtnLzlwaU9JSWNCUUhoOHc9PSIsInZhbHVlIjoibjZJa2FadkJ3ZmxENTBocE0rMEY1VVpRNVcrRXgwR1ZlWWVTc3hiYzdKSzVXREJJZjBFVEszbTNPd2RhZUo5V1pLY25ORGo4SzZRaytCWFZUN01TbUp5SExDdDROWCtLRmNzd3VSaVo5TDFFL0VpM210N21lVGNSVCtxbFh5Ly8iLCJtYWMiOiJiNWEyNDhhZmJhOGNmODBkZTdhNzY3NjBmZGFjZjhiOTEyMWQwMGYwNWU1NDI5NDZlMmY3Nzg0YWUwYTljZTI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951752238\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1473114631 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">goYAETE0L476RtcoTEIFmskXRueE29TUnO1QyiU6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473114631\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1569395125 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 09:43:05 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569395125\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2073694305 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8001/storage/lang/flags/ae.png</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073694305\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/frontendassets/images/icon/apply-ellipse.svg", "controller_action": "App\\Http\\Controllers\\Frontend\\PagesController@customPage"}, "badge": null}}