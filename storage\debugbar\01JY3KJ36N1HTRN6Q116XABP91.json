{"__meta": {"id": "01JY3KJ36N1HTRN6Q116XABP91", "datetime": "2025-06-18 20:05:52", "utime": **********.47201, "method": "POST", "uri": "/candidate/register", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.084424, "end": **********.472055, "duration": 5.387630939483643, "duration_str": "5.39s", "measures": [{"label": "Booting", "start": **********.084424, "relative_start": 0, "end": **********.338933, "relative_end": **********.338933, "duration": 0.*****************, "duration_str": "255ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.338943, "relative_start": 0.***************, "end": **********.472059, "relative_end": 4.0531158447265625e-06, "duration": 5.***************, "duration_str": "5.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.351581, "relative_start": 0.*****************, "end": **********.356259, "relative_end": **********.356259, "duration": 0.004678010940551758, "duration_str": "4.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.462498, "relative_start": 5.****************, "end": **********.463999, "relative_end": **********.463999, "duration": 0.0015010833740234375, "duration_str": "1.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST candidate/register", "middleware": "web, xss", "controller": "App\\Http\\Controllers\\Frontend\\UserAuthController@candidateRegister<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserAuthController.php&line=50\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "candidate.register", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserAuthController.php&line=50\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserAuthController.php:50-81</a>"}, "queries": {"count": 13, "nb_statements": 11, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026170000000000002, "accumulated_duration_str": "26.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 658}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 457}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 492}], "start": **********.398014, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "jobes", "explain": null, "start_percent": 0, "width_percent": 5.502}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 53}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.403719, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "UserAuthController.php:53", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserAuthController.php&line=53", "ajax": false, "filename": "UserAuthController.php", "line": "53"}, "connection": "jobes", "explain": null, "start_percent": 5.502, "width_percent": 0}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'jobes' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\UserRepository.php", "line": 54}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4673312, "duration": 0.01259, "duration_str": "12.59ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:54", "source": {"index": 22, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\UserRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FRepositories%2FUserRepository.php&line=54", "ajax": false, "filename": "UserRepository.php", "line": "54"}, "connection": "jobes", "explain": null, "start_percent": 5.502, "width_percent": 48.109}, {"sql": "insert into `users` (`username`, `first_name`, `last_name`, `phone`, `email`, `password`, `role`, `updated_at`, `created_at`) values ('vizuno<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', null, '<EMAIL>', '$2y$10$C0OXqmIgPB3i4yTEfi1le.rnz4/EYN5ADyv9ak7u6uw7vHG5cpVqm', 1, '2025-06-18 20:05:47', '2025-06-18 20:05:47')", "type": "query", "params": [], "bindings": ["vizuno<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", null, "<EMAIL>", "$2y$10$C0OXqmIgPB3i4yTEfi1le.rnz4/EYN5ADyv9ak7u6uw7vHG5cpVqm", 1, "2025-06-18 20:05:47", "2025-06-18 20:05:47"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\UserRepository.php", "line": 54}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4813352, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:54", "source": {"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\UserRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FRepositories%2FUserRepository.php&line=54", "ajax": false, "filename": "UserRepository.php", "line": "54"}, "connection": "jobes", "explain": null, "start_percent": 53.611, "width_percent": 5.159}, {"sql": "insert into `users_verify` (`user_id`, `token`, `updated_at`, `created_at`) values (435, '0WeybMJPJ5eidnU1jGXfJBknQAzjuR8EnBCerszjiRyQPXOpeeSKHxCYDsMRZZRM', '2025-06-18 20:05:47', '2025-06-18 20:05:47')", "type": "query", "params": [], "bindings": [435, "0WeybMJPJ5eidnU1jGXfJBknQAzjuR8EnBCerszjiRyQPXOpeeSKHxCYDsMRZZRM", "2025-06-18 20:05:47", "2025-06-18 20:05:47"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\UserRepository.php", "line": 104}, {"index": 22, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\UserRepository.php", "line": 65}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.484249, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "UserRepository.php:104", "source": {"index": 21, "namespace": null, "name": "app/Repositories/UserRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\UserRepository.php", "line": 104}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FRepositories%2FUserRepository.php&line=104", "ajax": false, "filename": "UserRepository.php", "line": "104"}, "connection": "jobes", "explain": null, "start_percent": 58.77, "width_percent": 5.846}, {"sql": "select * from `roles` where `name` = 'candidate' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["candidate", "web"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 165}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 324}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 111}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 106}], "start": **********.488404, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Role.php:165", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=165", "ajax": false, "filename": "Role.php", "line": "165"}, "connection": "jobes", "explain": null, "start_percent": 64.616, "width_percent": 4.089}, {"sql": "select * from `model_has_roles` where `model_has_roles`.`model_id` = 435 and `model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [435, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 127}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 55}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.494302, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:127", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=127", "ajax": false, "filename": "HasRoles.php", "line": "127"}, "connection": "jobes", "explain": null, "start_percent": 68.705, "width_percent": 1.987}, {"sql": "insert into `model_has_roles` (`model_id`, `model_type`, `role_id`) values (435, 'App\\\\Models\\\\User', 3)", "type": "query", "params": [], "bindings": [435, "App\\Models\\User", 3], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 127}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 55}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4958189, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:127", "source": {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 127}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=127", "ajax": false, "filename": "HasRoles.php", "line": "127"}, "connection": "jobes", "explain": null, "start_percent": 70.692, "width_percent": 1.528}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (435) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 128}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 55}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4974701, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:128", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=128", "ajax": false, "filename": "HasRoles.php", "line": "128"}, "connection": "jobes", "explain": null, "start_percent": 72.22, "width_percent": 1.758}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'jobes' and table_name = 'candidates' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 56}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.501182, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "UserAuthController.php:56", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserAuthController.php&line=56", "ajax": false, "filename": "UserAuthController.php", "line": "56"}, "connection": "jobes", "explain": null, "start_percent": 73.978, "width_percent": 12.801}, {"sql": "insert into `candidates` (`user_id`, `updated_at`, `created_at`) values (435, '2025-06-18 20:05:47', '2025-06-18 20:05:47')", "type": "query", "params": [], "bindings": [435, "2025-06-18 20:05:47", "2025-06-18 20:05:47"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 56}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.505641, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "UserAuthController.php:56", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserAuthController.php&line=56", "ajax": false, "filename": "UserAuthController.php", "line": "56"}, "connection": "jobes", "explain": null, "start_percent": 86.779, "width_percent": 6.114}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 60}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.511659, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "UserAuthController.php:60", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserAuthController.php&line=60", "ajax": false, "filename": "UserAuthController.php", "line": "60"}, "connection": "jobes", "explain": null, "start_percent": 92.893, "width_percent": 0}, {"sql": "select * from `email_templates` where `caption` = 'CANDIDATE_VERIFY_MAIL' limit 1", "type": "query", "params": [], "bindings": ["CANDIDATE_VERIFY_MAIL"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Classes/EmailFormat.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Classes\\EmailFormat.php", "line": 89}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/UserAuthController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\UserAuthController.php", "line": 67}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5121262, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EmailFormat.php:89", "source": {"index": 16, "namespace": null, "name": "app/Classes/EmailFormat.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Classes\\EmailFormat.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FClasses%2FEmailFormat.php&line=89", "ajax": false, "filename": "EmailFormat.php", "line": "89"}, "connection": "jobes", "explain": null, "start_percent": 92.893, "width_percent": 7.107}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Admin\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 1, "mails": [{"to": ["<EMAIL>"], "subject": "Candidate Verify Email", "headers": "From: <PERSON><PERSON> <<EMAIL>>\r\nTo: <EMAIL>\r\nSubject: Candidate Verify Email\r\n", "body": null, "html": null}]}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4QTRzP8Edrtf6zyGaMBkTwF91NcoHFTbFdcoobLP", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/storage/lang/flags/in.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:1 [\n    0 => \"toastr::messages\"\n  ]\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "state": "F5OyVo94BEMU2Vcfmz3q5YM023bQd2jmOQVjL0Wi", "PHPDEBUGBAR_STACK_DATA": "[]", "toastr::messages": "array:1 [\n  0 => array:4 [\n    \"type\" => \"info\"\n    \"title\" => \"Please Verify Your Email\"\n    \"message\" => \"\"\n    \"options\" => array:1 [\n      \"positionClass\" => \"toast-top-right\"\n    ]\n  ]\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/candidate/register", "action_name": "candidate.register", "controller_action": "App\\Http\\Controllers\\Frontend\\UserAuthController@candidateRegister", "uri": "POST candidate/register", "controller": "App\\Http\\Controllers\\Frontend\\UserAuthController@candidateRegister<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserAuthController.php&line=50\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FUserAuthController.php&line=50\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/UserAuthController.php:50-81</a>", "middleware": "web, xss", "duration": "5.39s", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1801369220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1801369220\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1621234587 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4QTRzP8Edrtf6zyGaMBkTwF91NcoHFTbFdcoobLP</span>\"\n  \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Kasimir</span>\"\n  \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Davis</span>\"\n  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"10 characters\">vizunolymu</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>password_confirmation</span>\" => \"<span class=sf-dump-str title=\"6 characters\">654321</span>\"\n  \"<span class=sf-dump-key>terms_condition</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1621234587\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-277417260 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">920</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">4QTRzP8Edrtf6zyGaMBkTwF91NcoHFTbFdcoobLP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryDHnLqcYUH7T3QnLi</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://127.0.0.1:8001/signup</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6Ijlia1dDbEtHUWVjMkxyWmI1VmtMWHc9PSIsInZhbHVlIjoiTkV4alpHT1FCamdXeFYyZlpwR1M1TGVjWFBFbDBWeGpSM241WjVqUVp3aEQ1UUZuSjBIbk9aaDBpSHh5NGRyc3QrUVFaT09ZWmhQdmRPbFFIUTFxd2tzb0xNWWxFZm1DaUtEaXQ2dmVLa3U5ZVBZK041dEhMZ1dLM25Dc3hldEYiLCJtYWMiOiI3ZTkzZTgyNzU1ZDJiOWE4ZWNkODE3MDU2NTMwODQ5YjhjMmJmMGQyYThhNDE0NWI3MjFkZDgwMjYxMWUxMTBhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlhJaWNpaHlZWlIzNE9BblNPZEhpQWc9PSIsInZhbHVlIjoiWG9sMWl2ZmgrSEpWYWJVbmMyNGZOVDFIWGpJblB2K21pRVRRUmZyZTVHdEZDUDg1UnJ2b0ZXVm5qbnJWN2xiaThrZlgvUWpxaDVEU0FINjdSTVdvdkxGYnZ3WGtHMjMyelRoN0hxcjh4UUpvaS9XV0hEL2RjSUdqa0JPSXdjSjEiLCJtYWMiOiI4M2ViNjhmOTU5MTg1YzExMDJmZjMxMDYyMGY0YTNiYjA2MDk5YTQ2NzhkNTU5Nzg5NDRhZDhjYWU3MWJjNDEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277417260\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-277171243 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4QTRzP8Edrtf6zyGaMBkTwF91NcoHFTbFdcoobLP</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y3GVpqyImvcEByL1Fs4fzvBslmkACTDqJ5prkbR4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277171243\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-108175052 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 08:05:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108175052\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1026302736 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4QTRzP8Edrtf6zyGaMBkTwF91NcoHFTbFdcoobLP</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8001/storage/lang/flags/in.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">toastr::messages</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"40 characters\">F5OyVo94BEMU2Vcfmz3q5YM023bQd2jmOQVjL0Wi</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>toastr::messages</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">info</span>\"\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Please Verify Your Email</span>\"\n      \"<span class=sf-dump-key>message</span>\" => \"\"\n      \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>positionClass</span>\" => \"<span class=sf-dump-str title=\"15 characters\">toast-top-right</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026302736\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/candidate/register", "action_name": "candidate.register", "controller_action": "App\\Http\\Controllers\\Frontend\\UserAuthController@candidateRegister"}, "badge": null}}