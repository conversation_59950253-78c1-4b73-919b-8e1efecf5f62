<?php

namespace App\Http\Controllers\Candidate;

use App\Http\Controllers\Controller;
use App\Models\Admin\Department;
use App\Models\Admin\EducationLavel;
use App\Models\Admin\JobSkill;
use App\Models\Admin\Profession;
use App\Models\Admin\University;
use App\Repositories\CandidateRepository;

class CandidateDashboardController extends Controller
{
    public function __construct(protected CandidateRepository $candidate) {}

    /** =========candidate dashboard =======
     *
     * @return Response
     */
    public function candidateDashboard()
    {

        try {

            $dashboardInfo = $this->candidate->candidateDashboardInfo();
            return view('front.pages.candidate.dashboard', compact('dashboardInfo'));
        } catch (\Throwable $th) {

            return  view('front.404');
        }

    }

    /**
     * designation
     *
     * @param  mixed  $name
     * @return void
     */
    public function designation($name)
    {

        $pressions = Profession::where('name', 'like', '%'.$name.'%')->get();

        return response()->json($pressions);
    }

    /**
     * qaualification
     *
     * @param  mixed  $name
     * @return void
     */
    public function qaualification($name)
    {

        $qaualifications = EducationLavel::where('status', 1)->where('name', 'like', '%'.$name.'%')->get();

        return response()->json($qaualifications);
    }

    /**
     * skill
     *
     * @param  mixed  $name
     * @return void
     */
    public function skill($name)
    {

        $skills = JobSkill::where('name', 'like', '%'.$name.'%')->get();

        return response()->json($skills);
    }

    /**
     * department
     *
     * @param  mixed  $name
     * @return void
     */
    public function department($name)
    {

        $departments = Department::where('name', 'like', '%'.$name.'%')->get();

        return response()->json($departments);
    }

    /**
     * university
     *
     * @param  mixed  $name
     * @return void
     */
    public function university($name)
    {

        $universites = University::where('name', 'like', '%'.$name.'%')->get();

        return response()->json($universites);
    }
}
