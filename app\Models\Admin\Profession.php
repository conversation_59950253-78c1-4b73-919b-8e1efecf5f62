<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class Profession extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function getTranslation($field = '', $lang = false)
    {
        $lang = $lang == false ? App::getLocale() : $lang;
        $professionTranslation = $this->professionTranslation->where('lang', $lang)->first();

        return $professionTranslation != null ? $professionTranslation->$field : $this->$field;
    }

    public function professionTranslation()
    {
        return $this->hasMany(ProfessionTranslation::class);
    }
}
