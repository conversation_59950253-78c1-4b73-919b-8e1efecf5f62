<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\LanguageRepostiory;
use App\Repositories\HomeRepository;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function __construct(protected HomeRepository $home, protected LanguageRepostiory $language) {}

    /*
     * Home page all section data fetch
     * */
    public function index()
    {

        $data = $this->home->homeContent();

        $data['page']?->page_slug;
        if ($data['page']) {
            return view('front.pages.eg_job', [
                'params' => $data['page']->page_slug,
                'title' => $data['page']->page_name,
                'widget_contents' => $data['page']->widgetContents,
            ]);
        } else {
            return view('front.pages.not-found');
        }

    }

    public function languageChange(Request $request)
    {
        $result = $this->language->changeLanguage($request);
        if ($result['status'] == true) {
            return response()->json(['status' => true, 'message' => $result['message']]);
        }
    }
}
