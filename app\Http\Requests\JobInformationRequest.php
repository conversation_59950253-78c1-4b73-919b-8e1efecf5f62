<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;

class JobInformationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            //
            'job_title' => Request()->prefix == 'parimary-information' ? 'required' : '',
            'category_id' => Request()->prefix == 'parimary-information' ? 'required' : '',
            'job_vacancy' => Request()->prefix == 'parimary-information' ? (Request()->ch_vacancies == 'on' ? '' : 'required|int') : '',
            'job_type_id' => Request()->prefix == 'parimary-information' ? 'required' : '',
            'job_deadline' => Request()->prefix == 'parimary-information' ? 'required' : '',
            'cv_receive_option' => Request()->prefix == 'parimary-information' ? 'required' : '',
            'applied_email' => Request()->prefix == 'parimary-information' ? (Request()->cv_receive_email == 'on' ? 'required' : '') : '',

            'job_level' => Request()->prefix == 'more-information' ? 'required' : '',
            'job_role' => Request()->prefix == 'more-information' ? 'required' : '',
            'job_description' => Request()->prefix == 'more-information' ? 'required' : '',
            'job_responsibility' => Request()->prefix == 'more-information' ? 'required' : '',
            'salary_mode' => Request()->prefix == 'more-information' ? 'required' : '',
            'min_salary' => Request()->prefix == 'more-information' ? (Request()->salary_mode == 'range' ? 'required|min:1' : '') : '',
            'max_salary' => Request()->prefix == 'more-information' ? (Request()->salary_mode == 'range' ? 'required|gt:min_salary' : '') : '',
            'currency' => Request()->prefix == 'more-information' ? (Request()->salary_mode == 'range' ? 'required' : '') : '',
            'salary_type' => Request()->prefix == 'more-information' ? 'required' : '',
            'experience' => Request()->prefix == 'more-information' ? 'required' : '',

            'education_id' => Request()->prefix == 'candidate' ? 'required' : '',
            'skill' => Request()->prefix == 'candidate' ? 'required' : '',
            'gender' => Request()->prefix == 'candidate' ? 'required' : '',

            'company_id' => Request()->prefix == 'company' ? 'required' : '',
            'country_id' => Request()->prefix == 'company' ? 'required' : '',
            'state_id' => Request()->prefix == 'company' ? 'required' : '',
            'city_id' => Request()->prefix == 'company' ? 'required' : '',
            'address' => Request()->prefix == 'company' ? 'required' : '',

        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'job_title.required' => 'Job Title is required.',
            'category_id.required' => 'Job Category Name is required.',
            'job_vacancy.required' => 'Job Vacancy is required.',
            'job_type_id.required' => 'Job Type is required.',
            'job_deadline.required' => 'Job Deadline is required.',
            'cv_receive_option.required' => 'Cv Receive Option is required.',
            'applied_email.required' => 'Apply E-mail is required.',

            'job_level.required' => 'Job Level is required.',
            'job_role.required' => 'Job Role is required.',
            'job_description.required' => 'Job description is required.',
            'job_responsibility.required' => 'Job Responsibility is required.',
            'salary_mode.required' => 'Salary Mode is required.',
            'min_salary.required' => 'Min Salary is required.',
            'max_salary.required' => 'Max Salary is required.',
            'currency.required' => 'Currency is required.',
            'salary_type.required' => 'Salary Type is required.',
            'experience.required' => 'Experience is required.',
            'max_salary.gt' => 'Max Salary will be greater than Min Salary .',

            'education_id.required' => 'Education Level is required.',
            'skill.required' => 'Skill is required.',
            'gender.required' => 'Gender is required.',

            'company_id.required' => 'Company Name  is required.',
            'country_id.required' => 'Country is required.',
            'state_id.required' => 'State is required.',
            'city_id.required' => 'City is required.',
            'address.required' => 'Address is required.',
            'job_vacancy.integer' => 'Job Vacancy must be Number.',

        ];
    }

    public function failedValidation(Validator $validator)
    {

        if (Auth::user()->hasRole('company')) {

            if (isset(Request()->id) || isset(Request()->job_id)) {
                throw new HttpResponseException(response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ]));
            } else {
                if ($this->customConditionPasses()) {
                    throw new HttpResponseException(response()->json([
                        'status' => false,
                        'errors' => $validator->errors(),
                    ]));
                }
            }

        } else {

            throw new HttpResponseException(response()->json([
                'status' => false,
                'errors' => $validator->errors(),
            ]));
        }

    }

    protected function customConditionPasses()
    {
        if (subcriptionCheck()) {
            if (subcriptionCheck()?->avilable_qty > 0) {
                return true;
            } else {
                return false;
            }
        }

        return false;

    }
}
