
<?php $__env->startSection('breadcrumb'); ?>
    <div class="breadcrumb-area">
        <h5><?php echo e(translate('Dashboard')); ?></h5>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>"><?php echo e(translate('Dashboard')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(translate('Candidate Edit')); ?></li>
            </ol>
        </nav>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('main_content'); ?>
    <div class="main-content">
        <div class="row mb-35">
            <div class="page-title d-flex justify-content-between align-items-center">
                <h4><?php echo e(translate('Edit Candidate')); ?></h4>
                <a href="<?php echo e(route('admin.candidate')); ?>" class="eg-btn btn--primary back-btn"> <img
                        src="<?php echo e(asset('backend/assets/images/icons/back.svg')); ?>" alt="">  <?php echo e(translate('Go Back')); ?></a>
            </div>
        </div>
        <form method="POST"  class="add-form" action="<?php echo e(route('admin.candidate.update')); ?>" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>

            <input type="hidden" name="id" value="<?php echo e($candidate->id); ?>">
            <input type="hidden" name="user_id" value="<?php echo e($candidate->user->id); ?>">
            <input type="hidden" name="lang" value="<?php echo e($lang); ?>">

            <div class="row">
                <div class="col-9">
                    <div class="col-lg-12">
                        <div class="card-header dashboard-header"><?php echo e(translate('Account Details')); ?></div>
                        <div class="eg-card product-card">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-inner mb-25">
                                        <label><?php echo e(translate('First Name')); ?><span>*</span></label>
                                        <input type="text" class="username-input" name="first_name"  value="<?php echo e($candidate?->user?->first_name); ?>"  placeholder="<?php echo e(translate('Enter Your First Name')); ?>">
                                        <span class="text-danger error-text first_name_err"></span>

                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-25">
                                        <label><?php echo e(translate('Last Name')); ?><span>*</span> </label>
                                        <input type="text" class="username-input" name="last_name"  value="<?php echo e($candidate?->user?->last_name); ?>" placeholder="<?php echo e(translate('Enter Your Last Name')); ?>">
                                        <span class="text-danger error-text last_name_err"></span>

                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-25">
                                        <label><?php echo e(translate('Username')); ?></label>
                                        <input type="text" class="username-input" name="username"  value="<?php echo e($candidate?->user?->username); ?>"  placeholder="<?php echo e(translate('Enter Your User Name')); ?>">
                                        <span class="text-danger error-text username_err"></span>

                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-25">
                                        <label><?php echo e(translate('Email')); ?><span>*</span> </label>
                                        <input type="text" class="username-input" name="email" value="<?php echo e($candidate?->user?->email); ?>"  placeholder="<?php echo e(translate('Enter Your Email')); ?>">
                                        <span class="text-danger error-text email_err"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-25">
                                        <label><?php echo e(translate('Phone')); ?> </label>
                                        <input type="text" class="username-input" value="<?php echo e($candidate?->user?->phone); ?>"   name="phone" placeholder="<?php echo e(translate('Enter Your phone')); ?>">

                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>


                    <div class="col-lg-12">
                        <div class="card-header dashboard-header"><?php echo e(translate('Profile Details')); ?></div>
                        <div class="eg-card product-card">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-inner mb-2">
                                        <label><?php echo e(translate('Profession')); ?><span>*</span> </label>
                                        <select name="profession_id" id="profession" class="form-select">
                                            <option disabled selected> <?php echo e(translate('Select Your Profession')); ?></option>
                                            <?php $__currentLoopData = professions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $profession): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($profession->id); ?>" <?php echo e($candidate?->profession_id == $profession->id ? "selected" :" "); ?>> <?php echo e($profession->name); ?> </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <span class="text-danger error-text profession_id_err"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <label><?php echo e(translate('Experience')); ?><span>*</span> </label>
                                        <input type="text"class="username-input"  name="candidate_experience" value="<?php echo e($candidate->getTranslation('candidate_experience', $lang)); ?>" placeholder="<?php echo e(translate('Enter Your Experience')); ?>">
                                        <span class="text-danger error-text candidate_experience_err"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <label><?php echo e(translate('Job Role')); ?><span>*</span></label>
                                        <select name="job_role_id" id="jobRole" class="form-select">
                                            <option disabled selected> <?php echo e(translate('Select Your Job Role')); ?></option>
                                            <?php $__currentLoopData = jobRole(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($role->id); ?>" <?php echo e($candidate?->job_role_id == $role->id ? "selected" :" "); ?> >  <?php echo e($role->name); ?> </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <span class="text-danger error-text job_role_id_err"></span>
                                    </div>
                                </div>


                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <label><?php echo e(translate('Qualification')); ?><span>*</span></label>
                                        <input type="text" class="username-input" name="candidate_qualification" value="<?php echo e($candidate->getTranslation('candidate_qualification', $lang)); ?>" placeholder="<?php echo e(translate('Enter Your Qualification')); ?>"/>
                                        <span class="text-danger error-text candidate_qualification_err"></span>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <label><?php echo e(translate('Date of Birth')); ?><span>*</span> </label>
                                        <input type="datetime-local" class="username-input" name="candidate_dob" value="<?php echo e($candidate->getTranslation('candidate_dob', $lang)); ?>"/>
                                        <span class="text-danger error-text candidate_dob_err"></span>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <label><?php echo e(translate('Marital Status')); ?></label>
                                        <select class="form-select" name="candidate_marital_status" id="candidate_marital_status" aria-label="Default select example">
                                            <option selected disabled> <?php echo e(translate('Select Your Marital Status')); ?></option>
                                            <option value="<?php echo e(translate('Married')); ?>" <?php echo e($candidate->candidate_marital_status == translate('Married') ? "selected" : ""); ?>><?php echo e(translate('Married')); ?></option>
                                            <option value="<?php echo e(translate('Unmarried')); ?>" <?php echo e($candidate->candidate_marital_status == translate('Unmarried') ? "selected" : ""); ?>><?php echo e(translate('Unmarried')); ?></option>
                                        </select>

                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <label><?php echo e(translate('Gender')); ?></label>
                                        <select class="form-select" name="candidate_gender" id="candidate_gender">
                                            <option selected disabled><?php echo e(translate('Select Your Gender')); ?></option>
                                            <option value="<?php echo e(translate('Male')); ?>"<?php echo e($candidate->candidate_gender == translate('Male') ? "selected" : ""); ?>><?php echo e(translate('Male')); ?></option>
                                            <option value="<?php echo e(translate('Female')); ?>"<?php echo e($candidate->candidate_gender == translate('Female') ? "selected" : ""); ?>><?php echo e(translate('Female')); ?></option>
                                            <option value="<?php echo e(translate('Other')); ?>"<?php echo e($candidate->candidate_gender ==translate('Other') ? "selected" : ""); ?>><?php echo e(translate('Other')); ?></option>
                                        </select>

                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <label><?php echo e(translate('Religion')); ?></label>
                                        <select class="form-select" name="candidate_religion" id="candidate_religion">
                                            <option selected disabled> <?php echo e(translate('Select Your Religion')); ?> </option>
                                            <option value="Islam"<?php echo e($candidate->candidate_religion == 'Islam' ? "selected" : ""); ?>>Islam</option>
                                            <option value="Hindu"<?php echo e($candidate->candidate_religion == 'Hindu' ? "selected" : ""); ?>>Hindu</option>
                                            <option value="khristan"<?php echo e($candidate->candidate_religion == 'khristan' ? "selected" : ""); ?>>khristan</option>
                                            <option value="Boddho"<?php echo e($candidate->candidate_religion == 'Boddho' ? "selected" : ""); ?>>Boddho</option>
                                            <option value="Other"<?php echo e($candidate->candidate_religion == 'Other' ? "selected" : ""); ?>>Other</option>
                                        </select>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <div class="col-lg-12">
                        <div class="card-header dashboard-header"><?php echo e(translate('Skill & Bio')); ?></div>
                        <div class="eg-card product-card">
                            <div class="row">
                                <div class="col-6">
                                    <label><?php echo e(translate('Skills')); ?></label>
                                    <div class="form-inner mb-25">
                                        <select class="form-select skill" name="candidate_skills[]" id="candidate_skills" multiple="multiple">
                                            <?php if(  count($candidate->candidateSkills)>0): ?>
                                                    <?php $__currentLoopData = $candidate->candidateSkills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($skill->name); ?>" selected><?php echo e($skill->name); ?></option>
                                                   <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>


                                        </select>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <label><?php echo e(translate('Language')); ?></label>
                                    <div class="form-inner mb-25">
                                        <select class="form-select skill" name="language_skills[]" id="language_skills" multiple="multiple">
                                            <?php if(count($candidate->candidate_language)>1): ?>
                                                <?php $__currentLoopData = $candidate->candidate_language; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($skill); ?>" selected><?php echo e($skill); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>

                                        </select>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <label><?php echo e(translate('Bio')); ?></label>
                                    <div class="form-inner mb-25">
                                        <textarea name="bio" id="summernote"><?php echo clean($candidate->getTranslation('bio', $lang)); ?></textarea>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-3">

                    <div class="card-header dashboard-header"><?php echo e(translate('Password Change Option')); ?> </div>
                    <div class="eg-card">
                        <div class="form-inner mb-25">
                            <label><?php echo e(translate('New Password')); ?> <span>*</span></label>
                            <input type="password" class="username-input" name="password" placeholder="********">

                        </div>

                        <div class="form-inner mb-25">
                            <label> <?php echo e(translate('Confirm Password')); ?> <span>*</span></label>
                            <input type="password" class="username-input" name="confirm_password" placeholder="********">

                            <span class="text-danger error-text confirm_password_err"></span>
                        </div>
                    </div>



                    <div class="card-header dashboard-header"><?php echo e(translate('Image')); ?></div>

                    <div class="eg-card product-card">

                        <div class="form-inner file-upload mb-35">
                            <label class="control-label"><?php echo e(translate('Photo')); ?> <span>*</span></label>
                            <div class="dropzone-wrapper">
                                <div class="dropzone-desc">
                                    <i class="glyphicon glyphicon-download-alt"></i>
                                    <p><?php echo e(translate('Choose an image file or drag it here')); ?></p>
                                </div>
                                <input type="file" name="candidate_image" class="dropzone dropzone-image">
                                <span class="text-danger error-text candidate_image_err"></span>
                            </div>

                            <div class="preview-zone hidden">
                                <div class="box box-solid">
                                    <div class="box-body">
                                        <?php if(fileExists($folder="candidates", $fileName=$candidate->candidate_image) == true  && $candidate->candidate_image !=="" ): ?>
                                        <div class="img-thumb-wrapper card shadow"> <span class="remove text-danger"> <i class="bi bi-trash"></i> </span>
                                             <img src="<?php echo e(asset( "storage/candidates/" .$candidate->candidate_image)); ?>" alt="" width="100">
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="eg-card p-0">
                        <div class="card-header"><?php echo e(translate('Available for hiring')); ?>?</div>
                        <div class="form-check form-switch candidate-option">
                            <input class="form-check-input" name="available_hiring" type="checkbox" <?php echo e($candidate->available_hiring==1? "checked":''); ?> id="available_hiring">
                            <label class="form-check-label ml-1" for="available_hiring"> </label>
                          </div>
                    </div>
                    <div class="eg-card p-0">
                        <div class="card-header"><?php echo e(translate('Public your profile')); ?>?</div>
                        <div class="form-check form-switch candidate-option">
                            <input class="form-check-input" name="is_public_profile" type="checkbox"   <?php echo e($candidate->is_public_profile==1? "checked":''); ?>  id="is_public_profile">
                            <label class="form-check-label ml-1" for="is_public_profile"> </label>
                          </div>
                    </div>

                    <div class="eg-card p-0">
                        <div class="card-header"><?php echo e(translate('Hide your CV')); ?>?</div>
                        <div class="form-check form-switch candidate-option">
                            <input class="form-check-input" name="hide_cv" type="checkbox"  <?php echo e($candidate->hide_cv==1? "checked":''); ?>  id="hide_cv">
                            <label class="form-check-label ml-1" for="hide_cv"> </label>
                          </div>
                    </div>

                        <div class="eg-card product-card">
                            <div class="button-group mt-15">
                                <button type="submit" class="eg-btn btn--green medium-btn me-3"><?php echo e(translate('Update')); ?></button>
                            </div>

                        </div>

                </div>
            </div>
        </form>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('post_scripts'); ?>
   <script src="<?php echo e(asset('backend/assets/js/custom.js')); ?>"></script>
    <?php echo $__env->make('js.custom', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopPush(); ?>


<?php echo $__env->make('admin.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\empzio\resources\views/admin/pages/candidate/edit.blade.php ENDPATH**/ ?>