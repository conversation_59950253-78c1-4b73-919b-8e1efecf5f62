<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\EmailTemplateRequest;
use App\Repositories\Admin\EmailTemplateRepostitory;
use Illuminate\Http\Request;

class EmailTemplateController extends Controller
{
    public function __construct(protected EmailTemplateRepostitory $emailTemplate) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {

        if ($request->ajax()) {
            return $this->emailTemplate->content();
        }
        return view('admin.pages.email-template.index');
    }

    /** new resource create form
     * ======= create =====
     *
     * @return view;
     */
    public function create()
    {
        return view('admin.pages.email-template.create');
    }

    /** new post store
     * ========= store============
     *
     * @param App\Http\Requests\EmailTemplateRequest
     * @return Response
     */
    public function store(EmailTemplateRequest $request)
    {

        $result = $this->emailTemplate->create($request);
        if ($result['status'] !== true) {
            return $this->formatResponse($result);
        }
        return response()->json([
            'status' => true,
            'message' => $result['message'],
            'url' => route('admin.email.template')
        ]);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id, Request $request)
    {

        if (!$request->user()->can('emailtemplate.edit')) {
            toastr()->error(translate('You have no Permission'));
            return redirect()->back();
        }

        $edit = $this->emailTemplate->getById($id);

        $lang = $request->lang; // Use $request instead of Request()
        return view('admin.pages.email-template.edit', compact('edit', 'lang'));



    }

    /** specific resource update by id
     * ========= update ===========
     *
     * @param App\Http\Requests\EmailTemplateRequest
     * @return Response
     */
    public function update(EmailTemplateRequest $request)
    {

        if (!$this->hasPermissions(['emailtemplate.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->emailTemplate->update($request->id, $request);
        return response()->json([
            'status' => $result['status'],
            'message' => $result['message'],
            'url' => route('admin.email.template'),
        ]);


    }

    /** status change
     * ======= statusChange ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id, Request $request)
    {
        if (!$this->hasPermissions(['emailtemplate.status'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->emailTemplate->statusChange($id);
        return $this->formatResponse($result);

    }
}
