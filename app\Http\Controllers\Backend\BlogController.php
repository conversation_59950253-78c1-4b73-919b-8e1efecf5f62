<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\BlogRequest;
use App\Repositories\Admin\BlogRepository;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    public function __construct(protected BlogRepository $blog) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->blog->content();
        }

        return view('admin.pages.blog.index');
    }

    /** new resource create form
     * ======= create =====
     *
     * @return view;
     */
    public function create()
    {
        return view('admin.pages.blog.create');
    }

    /** new post store
     * ========= store============
     *
     * @param App\Http\Requests\BlogRequest
     * @return Response
     */
    public function store(BlogRequest $request)
    {
        if (!$this->hasPermissions(['blog.add'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->blog->create($request);
        return response()->json([
            'status' => $result['status'],
            'message' => $result['message'],
            'url' => $result['status'] ? route('admin.blog') : null
        ]);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {
        $post = $this->blog->getById($id);
        $lang = Request()->lang;

        return view('admin.pages.blog.edit', compact('lang', 'post'));
    }

    /** specific resource update by id
     * ========= update ===========
     *
     * @param App\Http\Requests\BlogRequest
     * @return Response
     */
    public function update(BlogRequest $request)
    {
        if (!$this->hasPermissions(['blog.edit'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->blog->update($request->id, $request);
        return response()->json([
            'status' => $result['status'],
            'message' => $result['message'],
            'url' => $result['status'] ? route('admin.blog') : null
        ]);

    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete(Request $request, $id)
    {
        if (!$this->hasPermissions(['blog.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->blog->destroy($id);
        return $this->formatResponse($result);

    }

    /** status change
     * ======= statusChange ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange(Request $request, $id)
    {
        if (!$this->hasPermissions(['blog.status'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->blog->statusChange($id);
        return $this->formatResponse($result);

    }
}
