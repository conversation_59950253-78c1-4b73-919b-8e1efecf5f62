<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class CandidateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [

            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'required|email|unique:users,email,'.Request()->user_id,
            'password' => isset(Request()->id) ? (isset(Request()->password) ? 'required|min:5|max:12' : '') : 'required|min:5|max:12',
            'confirm_password' => isset(Request()->id) ? (isset(Request()->password) ? 'required|same:password' : '') : 'required|same:password',

            'candidate_experience' => 'required',
            'candidate_qualification' => 'required|not_in:0',
            'candidate_dob' => 'required',
            'candidate_image' => Request()->id ? '' : 'required',
            'profession_id' => 'required',
            'job_role_id' => 'required',

        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [

            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'Email is required.',
            'email.unique' => 'A user with this email address already exists.',
            'password.required' => 'Password Field is required',
            'confirm_password.required' => 'Confirm Password Field is required',
            'confirm_password.same' => 'Password Confirmation should match the Password',
            'password.max.string' => 'Password Maximun length 12',
            'password.min.string' => 'Password Minimun length 5',

            'candidate_experience.required' => 'Candidate Experience is required',
            'candidate_qualification.required' => 'Candidate Qualification is required',
            'candidate_dob.required' => 'Date Of Birth is required',

            'candidate_gender.required' => 'Gender  is required',
            'candidate_religion.required' => 'Religion  is required',
            'candidate_image.required' => 'Profile Image is required',
            'profession_id.required' => 'Profession is required',
            'job_role_id.required' => 'Job Role is required',

        ];
    }

    /** failed Validation
     *  =======  failedValidation =========
     *
     * @param Validator
     * @return Response
     */
    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => false,
            'errors' => $validator->errors(),
        ]));
    }
}
