{"__meta": {"id": "01JY3S3VS4DZ744W78JQ0GHEJ8", "datetime": "2025-06-18 21:42:57", "utime": **********.573551, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.058271, "end": **********.573567, "duration": 0.5152959823608398, "duration_str": "515ms", "measures": [{"label": "Booting", "start": **********.058271, "relative_start": 0, "end": **********.26273, "relative_end": **********.26273, "duration": 0.*****************, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.262743, "relative_start": 0.*****************, "end": **********.573569, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.271714, "relative_start": 0.*****************, "end": **********.276983, "relative_end": **********.276983, "duration": 0.005269050598144531, "duration_str": "5.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.290869, "relative_start": 0.*****************, "end": **********.571749, "relative_end": **********.571749, "duration": 0.****************, "duration_str": "281ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "1x front.pages.eg_job", "param_count": null, "params": [], "start": **********.29347, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/eg_job.blade.phpfront.pages.eg_job", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2Feg_job.blade.php&line=1", "ajax": false, "filename": "eg_job.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.eg_job"}, {"name": "1x front.pages.widgets.hero-section", "param_count": null, "params": [], "start": **********.294205, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.phpfront.pages.widgets.hero-section", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Fhero-section.blade.php&line=1", "ajax": false, "filename": "hero-section.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.hero-section"}, {"name": "1x front.pages.widgets.latest-jobs-category", "param_count": null, "params": [], "start": **********.355444, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.phpfront.pages.widgets.latest-jobs-category", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Flatest-jobs-category.blade.php&line=1", "ajax": false, "filename": "latest-jobs-category.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.latest-jobs-category"}, {"name": "1x front.pages.widgets.featured-jobs", "param_count": null, "params": [], "start": **********.418597, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/featured-jobs.blade.phpfront.pages.widgets.featured-jobs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Ffeatured-jobs.blade.php&line=1", "ajax": false, "filename": "featured-jobs.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.featured-jobs"}, {"name": "1x front.pages.widgets.working-process", "param_count": null, "params": [], "start": **********.47062, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/working-process.blade.phpfront.pages.widgets.working-process", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Fworking-process.blade.php&line=1", "ajax": false, "filename": "working-process.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.working-process"}, {"name": "1x front.pages.widgets.dream-location", "param_count": null, "params": [], "start": **********.48324, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/dream-location.blade.phpfront.pages.widgets.dream-location", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Fdream-location.blade.php&line=1", "ajax": false, "filename": "dream-location.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.dream-location"}, {"name": "1x front.pages.widgets.reviews", "param_count": null, "params": [], "start": **********.498397, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/reviews.blade.phpfront.pages.widgets.reviews", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Freviews.blade.php&line=1", "ajax": false, "filename": "reviews.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.reviews"}, {"name": "1x front.pages.widgets.trusted-company", "param_count": null, "params": [], "start": **********.504416, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/trusted-company.blade.phpfront.pages.widgets.trusted-company", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Ftrusted-company.blade.php&line=1", "ajax": false, "filename": "trusted-company.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.trusted-company"}, {"name": "1x front.pages.widgets.top-recruiters", "param_count": null, "params": [], "start": **********.508559, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/top-recruiters.blade.phpfront.pages.widgets.top-recruiters", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Fpages%2Fwidgets%2Ftop-recruiters.blade.php&line=1", "ajax": false, "filename": "top-recruiters.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.widgets.top-recruiters"}, {"name": "1x front.layouts.master", "param_count": null, "params": [], "start": **********.537528, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/master.blade.phpfront.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.master"}, {"name": "1x front.layouts.includes.head", "param_count": null, "params": [], "start": **********.537896, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/head.blade.phpfront.layouts.includes.head", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.head"}, {"name": "1x css.theme-custom-css", "param_count": null, "params": [], "start": **********.538407, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/css/theme-custom-css.blade.phpcss.theme-custom-css", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Fcss%2Ftheme-custom-css.blade.php&line=1", "ajax": false, "filename": "theme-custom-css.blade.php", "line": "?"}, "render_count": 1, "name_original": "css.theme-custom-css"}, {"name": "1x front.layouts.includes.header", "param_count": null, "params": [], "start": **********.538985, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/header.blade.phpfront.layouts.includes.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.header"}, {"name": "1x front.layouts.includes.top", "param_count": null, "params": [], "start": **********.539562, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/top.blade.phpfront.layouts.includes.top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.top"}, {"name": "1x front.layouts.includes.logo", "param_count": null, "params": [], "start": **********.543763, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/logo.blade.phpfront.layouts.includes.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.logo"}, {"name": "1x front.layouts.includes.menu", "param_count": null, "params": [], "start": **********.544479, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/menu.blade.phpfront.layouts.includes.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.menu"}, {"name": "1x front.layouts.includes.mobile", "param_count": null, "params": [], "start": **********.54944, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/mobile.blade.phpfront.layouts.includes.mobile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.mobile"}, {"name": "1x front.layouts.includes.right-menu", "param_count": null, "params": [], "start": **********.551895, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/right-menu.blade.phpfront.layouts.includes.right-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fright-menu.blade.php&line=1", "ajax": false, "filename": "right-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.right-menu"}, {"name": "1x front.layouts.includes.footer", "param_count": null, "params": [], "start": **********.55425, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/footer.blade.phpfront.layouts.includes.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.footer"}, {"name": "1x front.layouts.includes.script", "param_count": null, "params": [], "start": **********.5561, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/script.blade.phpfront.layouts.includes.script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.layouts.includes.script"}, {"name": "1x js.translate", "param_count": null, "params": [], "start": **********.556448, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/js/translate.blade.phpjs.translate", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Fjs%2Ftranslate.blade.php&line=1", "ajax": false, "filename": "translate.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.translate"}, {"name": "1x js.theme-custom-js", "param_count": null, "params": [], "start": **********.570859, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\jobes-application\\resources\\views/js/theme-custom-js.blade.phpjs.theme-custom-js", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Fjs%2Ftheme-custom-js.blade.php&line=1", "ajax": false, "filename": "theme-custom-js.blade.php", "line": "?"}, "render_count": 1, "name_original": "js.theme-custom-js"}]}, "route": {"uri": "GET /", "middleware": "web, xss", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "home", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:17-33</a>"}, "queries": {"count": 67, "nb_statements": 67, "nb_visible_statements": 67, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.035390000000000005, "accumulated_duration_str": "35.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `pages` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\HomeRepository.php", "line": 16}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.28701, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HomeRepository.php:16", "source": {"index": 16, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\HomeRepository.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FRepositories%2FHomeRepository.php&line=16", "ajax": false, "filename": "HomeRepository.php", "line": "16"}, "connection": "jobes", "explain": null, "start_percent": 0, "width_percent": 1.441}, {"sql": "select * from `widgets_contents` where `widgets_contents`.`page_id` in (1) and `status` = 1 order by `position` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\HomeRepository.php", "line": 16}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.289089, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HomeRepository.php:16", "source": {"index": 21, "namespace": null, "name": "app/Repositories/HomeRepository.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\HomeRepository.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FRepositories%2FHomeRepository.php&line=16", "ajax": false, "filename": "HomeRepository.php", "line": "16"}, "connection": "jobes", "explain": null, "start_percent": 1.441, "width_percent": 1.469}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 3 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 4}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.295258, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "jobes", "explain": null, "start_percent": 2.91, "width_percent": 1.498}, {"sql": "select count(`id`) as aggregate from `jobs` where `job_deadline` >= '2025-06-18 21:42:57'", "type": "query", "params": [], "bindings": ["2025-06-18 21:42:57"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 1228}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.298818, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1228", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 1228}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=1228", "ajax": false, "filename": "Helper.php", "line": "1228"}, "connection": "jobes", "explain": null, "start_percent": 4.408, "width_percent": 1.272}, {"sql": "select count(`id`) as aggregate from `companies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 1235}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.301163, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1235", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 1235}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=1235", "ajax": false, "filename": "Helper.php", "line": "1235"}, "connection": "jobes", "explain": null, "start_percent": 5.68, "width_percent": 1.045}, {"sql": "select count(`id`) as aggregate from `candidates`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 1242}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.302865, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1242", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 1242}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=1242", "ajax": false, "filename": "Helper.php", "line": "1242"}, "connection": "jobes", "explain": null, "start_percent": 6.725, "width_percent": 0.989}, {"sql": "select count(`id`) as aggregate from `jobs` where `job_deadline` >= '2025-06-11 21:42:57'", "type": "query", "params": [], "bindings": ["2025-06-11 21:42:57"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 1249}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.304182, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1249", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 1249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=1249", "ajax": false, "filename": "Helper.php", "line": "1249"}, "connection": "jobes", "explain": null, "start_percent": 7.714, "width_percent": 1.074}, {"sql": "select `lang_value`, `lang_key` from `translations` where `lang` = 'de'", "type": "query", "params": [], "bindings": ["de"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 258}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 18, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 257}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.306061, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "Helper.php:258", "source": {"index": 14, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 258}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=258", "ajax": false, "filename": "Helper.php", "line": "258"}, "connection": "jobes", "explain": null, "start_percent": 8.788, "width_percent": 7.121}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 426}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.325251, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Helper.php:426", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=426", "ajax": false, "filename": "Helper.php", "line": "426"}, "connection": "jobes", "explain": null, "start_percent": 15.908, "width_percent": 1.413}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 1 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.327811, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 17.321, "width_percent": 1.328}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 2 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.329502, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 18.649, "width_percent": 1.243}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 5 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.33093, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 19.893, "width_percent": 1.159}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 6 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.332291, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 21.051, "width_percent": 1.102}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 7 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.333652, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 22.153, "width_percent": 1.13}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 8 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.335003, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 23.283, "width_percent": 1.045}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 9 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.336287, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 24.329, "width_percent": 1.102}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 10 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.33761, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 25.431, "width_percent": 1.045}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 11 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.338888, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 26.476, "width_percent": 1.074}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 12 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.340202, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 27.55, "width_percent": 0.961}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 13 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.341474, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 28.511, "width_percent": 1.017}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 14 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.342878, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 29.528, "width_percent": 1.808}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 15 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.345079, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 31.337, "width_percent": 1.441}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 16 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.3465679, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 32.778, "width_percent": 1.074}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 17 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.347879, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 33.851, "width_percent": 1.045}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 18 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.3491669, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 34.897, "width_percent": 1.045}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 19 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.350435, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 35.942, "width_percent": 1.045}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 20 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.351727, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 36.988, "width_percent": 1.074}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 34 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.hero-section", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/hero-section.blade.php", "line": 103}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.353039, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 38.062, "width_percent": 1.074}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 111 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.355993, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "jobes", "explain": null, "start_percent": 39.135, "width_percent": 1.243}, {"sql": "select `categories`.*, (select count(*) from `jobs` where `categories`.`id` = `jobs`.`category_id`) as `jobs_count` from `categories` where `status` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 616}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3749099, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Helper.php:616", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 616}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=616", "ajax": false, "filename": "Helper.php", "line": "616"}, "connection": "jobes", "explain": null, "start_percent": 40.379, "width_percent": 1.695}, {"sql": "select `id`, `company_id`, `category_id`, `job_deadline`, `status` from `jobs` where `jobs`.`category_id` in (12, 13, 14, 15, 16, 17, 18, 19, 20, 34) and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `status` = 1 and `job_deadline` >= '2025-06-18 21:42:57'", "type": "query", "params": [], "bindings": [1, 1, 1, 1, "2025-06-18 21:42:57"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 616}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.380703, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Helper.php:616", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 616}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=616", "ajax": false, "filename": "Helper.php", "line": "616"}, "connection": "jobes", "explain": null, "start_percent": 42.074, "width_percent": 2.119}, {"sql": "select `id`, `company_name`, `slug`, `status` from `companies` where `companies`.`id` in (26, 35, 39, 43, 44, 46, 47, 55, 63) and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 616}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3826911, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Helper.php:616", "source": {"index": 25, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 616}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=616", "ajax": false, "filename": "Helper.php", "line": "616"}, "connection": "jobes", "explain": null, "start_percent": 44.193, "width_percent": 1.272}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 34 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.392163, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 45.465, "width_percent": 1.356}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 20 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.395472, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 46.821, "width_percent": 1.441}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 19 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.398048, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 48.262, "width_percent": 1.215}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 18 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.400465, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 49.477, "width_percent": 1.187}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 17 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.402868, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 50.664, "width_percent": 1.102}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 16 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.405236, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 51.766, "width_percent": 1.187}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 15 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.407687, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 52.953, "width_percent": 1.159}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 14 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.410953, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 54.111, "width_percent": 2.063}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 13 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.4139042, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 56.174, "width_percent": 1.272}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` = 12 and `category_translations`.`category_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.latest-jobs-category", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/latest-jobs-category.blade.php", "line": 55}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.416319, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Category.php:34", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Category.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Category.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=34", "ajax": false, "filename": "Category.php", "line": "34"}, "connection": "jobes", "explain": null, "start_percent": 57.446, "width_percent": 1.187}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 5 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.41922, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "jobes", "explain": null, "start_percent": 58.632, "width_percent": 1.215}, {"sql": "select `jobs`.*, (select `name` from `countries` where `jobs`.`country_id` = `countries`.`id`) as `country_name`, (select `name` from `cities` where `jobs`.`city_id` = `cities`.`id`) as `city_name` from `jobs` where exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `status` = 1 and `job_deadline` >= '2025-06-18 21:42:57' and `job_featured` = 1 order by `id` desc limit 6", "type": "query", "params": [], "bindings": [1, 1, 1, 1, "2025-06-18 21:42:57", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 650}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4210038, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Helper.php:650", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=650", "ajax": false, "filename": "Helper.php", "line": "650"}, "connection": "jobes", "explain": null, "start_percent": 59.847, "width_percent": 2.232}, {"sql": "select `id`, `company_name`, `slug`, `status` from `companies` where `companies`.`id` in (26, 46, 63, 65) and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 650}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.423002, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Helper.php:650", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=650", "ajax": false, "filename": "Helper.php", "line": "650"}, "connection": "jobes", "explain": null, "start_percent": 62.08, "width_percent": 1.243}, {"sql": "select * from `job_job_types` where `job_job_types`.`job_id` in (21, 24, 25, 27, 29, 32)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 650}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.42486, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Helper.php:650", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 650}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=650", "ajax": false, "filename": "Helper.php", "line": "650"}, "connection": "jobes", "explain": null, "start_percent": 63.323, "width_percent": 1.13}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 32 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.4291391, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "jobes", "explain": null, "start_percent": 64.453, "width_percent": 1.385}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 29 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.4383311, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "jobes", "explain": null, "start_percent": 65.838, "width_percent": 1.3}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 27 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.444732, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "jobes", "explain": null, "start_percent": 67.138, "width_percent": 1.356}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 25 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.450563, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "jobes", "explain": null, "start_percent": 68.494, "width_percent": 1.102}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 24 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.4562109, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "jobes", "explain": null, "start_percent": 69.596, "width_percent": 1.13}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 21 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.featured-jobs", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/featured-jobs.blade.php", "line": 49}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.463682, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "jobes", "explain": null, "start_percent": 70.726, "width_percent": 1.554}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 96 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.working-process", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/working-process.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.471162, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "jobes", "explain": null, "start_percent": 72.28, "width_percent": 1.413}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 105 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [105], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.dream-location", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/dream-location.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.48375, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "jobes", "explain": null, "start_percent": 73.693, "width_percent": 1.272}, {"sql": "select `name`, `location_image`, (select `name` from `countries` where `cities`.`country_id` = `countries`.`id`) as `country_name`, (select count(*) from `jobs` where `cities`.`id` = `jobs`.`city_id`) as `jobs_count` from `cities` where exists (select `id`, `city_id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `cities`.`id` = `jobs`.`city_id` and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `job_deadline` >= '2025-06-18 21:42:57' and `status` = 1) and `status` = 1 limit 6", "type": "query", "params": [], "bindings": [1, 1, 1, "2025-06-18 21:42:57", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 742}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4861681, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Helper.php:742", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=742", "ajax": false, "filename": "Helper.php", "line": "742"}, "connection": "jobes", "explain": null, "start_percent": 74.965, "width_percent": 2.345}, {"sql": "select `id`, `city_id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `jobs`.`city_id` in (0) and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `job_deadline` >= '2025-06-18 21:42:57' and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, 1, "2025-06-18 21:42:57", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 742}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.488379, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Helper.php:742", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=742", "ajax": false, "filename": "Helper.php", "line": "742"}, "connection": "jobes", "explain": null, "start_percent": 77.31, "width_percent": 1.3}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 8 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.reviews", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/reviews.blade.php", "line": 6}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.4990602, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "jobes", "explain": null, "start_percent": 78.61, "width_percent": 1.413}, {"sql": "select * from `testimonials` where `status` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 783}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.501263, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Helper.php:783", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 783}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=783", "ajax": false, "filename": "Helper.php", "line": "783"}, "connection": "jobes", "explain": null, "start_percent": 80.023, "width_percent": 1.159}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 106 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.trusted-company", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/trusted-company.blade.php", "line": 5}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.5049388, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "jobes", "explain": null, "start_percent": 81.181, "width_percent": 1.215}, {"sql": "select * from `trusted_companies` where `status` = 1 order by `id` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 763}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.506588, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Helper.php:763", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 763}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=763", "ajax": false, "filename": "Helper.php", "line": "763"}, "connection": "jobes", "explain": null, "start_percent": 82.396, "width_percent": 1.074}, {"sql": "select * from `widget_content_translations` where `widget_content_translations`.`widget_content_id` = 10 and `widget_content_translations`.`widget_content_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, {"index": 21, "namespace": "view", "name": "front.pages.widgets.top-recruiters", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/pages/widgets/top-recruiters.blade.php", "line": 7}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.5091271, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "WidgetsContents.php:29", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/WidgetsContents.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Models\\Admin\\WidgetsContents.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=29", "ajax": false, "filename": "WidgetsContents.php", "line": "29"}, "connection": "jobes", "explain": null, "start_percent": 83.47, "width_percent": 3.871}, {"sql": "select `id`, `company_name`, `company_type_id`, `user_id`, `company_logo`, `slug`, `status`, (select `name` from `countries` where `companies`.`country_id` = `countries`.`id`) as `country_name`, (select `name` from `cities` where `companies`.`city_id` = `cities`.`id`) as `city_name`, (select `name` from `states` where `companies`.`state_id` = `states`.`id`) as `state_name`, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id`) as `jobs_count`, (select sum(`jobs`.`job_vacancy`) from `jobs` where `companies`.`id` = `jobs`.`company_id`) as `jobs_sum_job_vacancy` from `companies` where exists (select `id`, `status`, `is_email_verified` from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and exists (select `id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `companies`.`id` = `jobs`.`company_id` and `job_deadline` >= '2025-06-18 21:42:57' and `status` = 1) and `status` = 1 order by `jobs_sum_job_vacancy` desc limit 6", "type": "query", "params": [], "bindings": [1, 1, "2025-06-18 21:42:57", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 684}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.514435, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Helper.php:684", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=684", "ajax": false, "filename": "Helper.php", "line": "684"}, "connection": "jobes", "explain": null, "start_percent": 87.341, "width_percent": 3.476}, {"sql": "select `id`, `status`, `is_email_verified` from `users` where `users`.`id` in (213, 214, 217, 225, 264, 271) and `status` = 1 and `is_email_verified` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.51685, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Helper.php:684", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=684", "ajax": false, "filename": "Helper.php", "line": "684"}, "connection": "jobes", "explain": null, "start_percent": 90.817, "width_percent": 1.102}, {"sql": "select `id`, `company_id`, `job_deadline`, `job_vacancy` from `jobs` where `jobs`.`company_id` in (45, 46, 48, 55, 63, 65) and `job_deadline` >= '2025-06-18 21:42:57' and `status` = 1", "type": "query", "params": [], "bindings": ["2025-06-18 21:42:57", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 684}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5183, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Helper.php:684", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 684}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=684", "ajax": false, "filename": "Helper.php", "line": "684"}, "connection": "jobes", "explain": null, "start_percent": 91.919, "width_percent": 1.3}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5414171, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "front.layouts.includes.top:19", "source": {"index": 16, "namespace": "view", "name": "front.layouts.includes.top", "file": "D:\\xampp\\htdocs\\jobes-application\\resources\\views/front/layouts/includes/top.blade.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fresources%2Fviews%2Ffront%2Flayouts%2Fincludes%2Ftop.blade.php&line=19", "ajax": false, "filename": "top.blade.php", "line": "19"}, "connection": "jobes", "explain": null, "start_percent": 93.218, "width_percent": 1.356}, {"sql": "select * from `menu_items` where (`menu_id` = 2 and `parent_id` is null) order by `order` asc", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 580}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5451698, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 15, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "jobes", "explain": null, "start_percent": 94.575, "width_percent": 4.097}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (215, 216, 231, 232, 233, 235) order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 580}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5479221, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Helper.php:580", "source": {"index": 20, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Helper\\Helper.php", "line": 580}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHelper%2FHelper.php&line=580", "ajax": false, "filename": "Helper.php", "line": "580"}, "connection": "jobes", "explain": null, "start_percent": 98.672, "width_percent": 1.328}]}, "models": {"data": {"App\\Models\\Admin\\CategoryTranslation": {"value": 132, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategoryTranslation.php&line=1", "ajax": false, "filename": "CategoryTranslation.php", "line": "?"}}, "App\\Models\\Admin\\WidgetContentTranslation": {"value": 32, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetContentTranslation.php&line=1", "ajax": false, "filename": "WidgetContentTranslation.php", "line": "?"}}, "App\\Models\\Admin\\Category": {"value": 29, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Admin\\Job": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Admin\\Company": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Admin\\TrustedCompany": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FTrustedCompany.php&line=1", "ajax": false, "filename": "TrustedCompany.php", "line": "?"}}, "App\\Models\\Admin\\WidgetsContents": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=1", "ajax": false, "filename": "WidgetsContents.php", "line": "?"}}, "App\\Models\\Admin\\JobJobType": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FJobJobType.php&line=1", "ajax": false, "filename": "JobJobType.php", "line": "?"}}, "App\\Models\\Admin\\City": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FCity.php&line=1", "ajax": false, "filename": "City.php", "line": "?"}}, "App\\Models\\User": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\MenuItem": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Admin\\Testimonial": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FTestimonial.php&line=1", "ajax": false, "filename": "Testimonial.php", "line": "?"}}, "App\\Models\\Admin\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Admin\\Pages": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FPages.php&line=1", "ajax": false, "filename": "Pages.php", "line": "?"}}}, "count": 294, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "locale": "de", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/frontendassets/images/icon/apply-ellipse.svg\"\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8001", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:17-33</a>", "middleware": "web, xss", "duration": "524ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1872154798 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1872154798\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-411120408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-411120408\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1328004467 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6IlgrZElQS1k0YS9nMWorR21JU2hKTmc9PSIsInZhbHVlIjoic2pWdXR1RzFwTlBNZGJ0SExKd3JRemhYbU5HdGhkWEVaNGFnMFdITmhwMTl3L3EvbmxwRnVRTW1sbkZUQWs5YjlsZnZqL2hOczdHek9XS2grbkZMMFlOdkFaaTBEYTcxSE91RTBlUUxjVEhrZ0pBVkk5MlE2a0lzM0I2L08xM3ciLCJtYWMiOiI4NzE5MGM0ODg0MGU5NDBiMDI3Y2FhYWMxY2UxNjU3M2NlOWJmZWE2MTE1MzZkMjc3NzMyM2JmMjFkZjBkYmU5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlYxcE9Ccy91Qy84MWRHREEvbjk4UVE9PSIsInZhbHVlIjoiOW5lbU82U1dYczF2SUs5SDV0MHY5NU84ZFMzUWZJWnN4MzZ6aUJnSzlQVVp5NU1yWFhBOXozakczdWxVdzhKcXpUMk9VSHNYTUkrWTB6ay95d28vNEhkUVV2bERtL1hPYjlPWGtJTXdYbURZMUVIakRKbEF5SmJlMFhvbzZKdEwiLCJtYWMiOiJiNmMzM2NlMjE4YmQ0NmQwMmY3OTEyZDE4NWE4YjIwNDVlYzlhZjJhMWMzODAzNmIxMzA4NjA1OWM3ODY5OGFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328004467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-102466111 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">goYAETE0L476RtcoTEIFmskXRueE29TUnO1QyiU6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102466111\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1101357677 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 09:42:57 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101357677\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1404870459 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">de</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">http://127.0.0.1:8001/frontendassets/images/icon/apply-ellipse.svg</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404870459\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8001", "action_name": "home", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@index"}, "badge": null}}