<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\CompanyTypeRequest;
use App\Models\Admin\CompanyType;
use App\Repositories\Admin\CompanyTypeRepostitory;
use Illuminate\Http\Request;

class CompanyTypeController extends Controller
{
    public function __construct(protected CompanyTypeRepostitory $companyType) {}

    /** Display the resource
     *========= index========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->companyType->content();
        }

        return view('admin.pages.company-type.index');
    }

    /** new resource store
     * ============ store =======
     *
     * @param Request
     * @return Response
     */
    public function store(CompanyTypeRequest $request)
    {
        if (!$this->hasPermissions(['companytype.add', 'companytype.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->companyType->create($request);
        return $this->formatResponse($result);

    }

    /** specific resource edit by id
     *========= edit ========
     *
     * @param int int
     * @return Response
     */
    public function edit($id)
    {
        $companyType = $this->companyType->getById($id);
        $data['company_type_name'] = $companyType->getTranslation('company_type_name', Request()->lang);
        $data['id'] = $companyType->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    public function delete($id)
    {

        if (!$this->hasPermissions(['companytype.delete'])) {
            return $this->permissionDeniedResponse();
        }

        if (!$id) {
            return redirect()->back();
        }
        $data = CompanyType::find($id);
        if (!$data) {
            return response()->json(['success' => false]);
        }
        $data->delete();
        return response()->json(['success' => true]);

    }

    /** company type status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {

        if (!$this->hasPermissions(['companytype.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->companyType->statusChange($id);
        return $this->formatResponse($result);
    }
}
