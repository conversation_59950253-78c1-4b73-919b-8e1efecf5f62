<div class="top-bar">
    <?php
        $header=  getThemeOption('header'.activeLanguage()) ?? 'headeren';
        $header=  $header ? $header:  getThemeOption('headeren');

        dd($header);
    ?>

    <p> <?php echo $header['header-left-text'] ?? null; ?> </p>
    <?php
        if (Session::has('locale')) {
            $locale = Session::get('locale', Config::get('app.locale'));
        } else {
            $locale = env('DEFAULT_LANGUAGE');
        }
    ?>
    <div class="top-bar-right">
        <div class="language-select">
            <img src="<?php echo e(asset('storage/lang/flags/' . $locale . '.png')); ?>" /><span><?php echo e(translate('Language')); ?></span>
            <ul class="topbar-sublist" id="change-lang">
                <?php $__currentLoopData = App\Models\Admin\Language::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <a class="languageTranslate dropdown-item d-flex <?php if($locale == $language->code): ?> active <?php endif; ?>"
                            href="javascript:void(0)" data-action="<?php echo e(route('language.change')); ?>"
                            id="<?php echo e($language->code); ?>" data-code="<?php echo e($language->code); ?>"
                            data-flag="<?php echo e($language->code); ?>">
                            <img src="<?php echo e(asset('storage/lang/flags/' . $language->code . '.png')); ?>">
                            <span><?php echo e($language->name); ?></span>
                        </a>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>

        <div class="social-area">
            <?php if( isset($header['social'])): ?>
                <ul>

                    <?php $__currentLoopData = $header['social']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li>
                            <a href="<?php echo e($social['url']); ?>">
                                <i class="<?php echo e($social['class']); ?>"></i>
                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/layouts/includes/top.blade.php ENDPATH**/ ?>