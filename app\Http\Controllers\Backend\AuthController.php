<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class AuthController extends Controller
{
    /**
     * Show the admin login page.
     * Redirect to dashboard or lock screen if already authenticated.
     */
    public function login()
    {
        if (Session::has('adminLogin') || Auth::guard('admin')->check()) {
            return redirect()->route(Session::has('authEmail') ? 'admin.lock' : 'admin.dashboard');
        }
        return view('admin.pages.auth.login');
    }

    /**
     * Handle admin login authentication.
     * If credentials are valid, store session and redirect to dashboard.
     */
    public function loginDashboard(Request $request)
    {
        if (!Auth::guard('admin')->attempt($request->only('email', 'password'))) {
            toastr()->error('Invalid Credentials');
            return redirect()->back();
        }

        // Store session to mark admin as logged in
        Session::put('adminLogin', 'user_log_in');
        return redirect()->route('admin.dashboard');
    }

    /**
     * Logout the admin user.
     * Clears session and redirects to login page.
     */
    public function logout()
    {
        if (!Auth::guard('admin')->check()) {
            return redirect()->back();
        }

        Auth::guard('admin')->logout();
        Session::forget('adminLogin');

        toastr()->success('Logout Successfully');
        return redirect()->route('login');
    }

    /**
     * Lock the admin screen.
     * Stores admin email in session and shows the lock screen.
     */
    public function lock()
    {
        if (!Auth::guard('admin')->check()) {
            return redirect()->back();
        }

        // Store email in session for unlocking
        Session::put('authEmail', Auth::guard('admin')->user()->email);

        toastr()->warning('Screen Locked', '', ['positionClass' => 'toast-top-right']);
        return view('admin.pages.auth.lock');
    }

    /**
     * Unlock the admin screen by verifying the password.
     * If valid, clear session and redirect to dashboard.
     */
    public function unlock(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        $sessionEmail = Session::get('authEmail');

        if ($sessionEmail === optional($admin)->email && Hash::check($request->password, $admin->password)) {
            Session::forget('authEmail');
            return redirect()->route('admin.dashboard');
        }

        return redirect()->back();
    }
}
