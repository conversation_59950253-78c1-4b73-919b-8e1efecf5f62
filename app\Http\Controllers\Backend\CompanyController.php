<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\CompanyRequest;
use App\Http\Requests\PasswordRequest;
use App\Models\Admin\Company;
use App\Repositories\CompanyRepository;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;

class CompanyController extends Controller
{
    public function __construct(protected CompanyRepository $company, protected UserRepository $user) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->company->content();
        }

        return view('admin.pages.company.index');
    }

    /** new resource create form
     * ======= create =====
     *
     * @return view;
     */
    public function create()
    {
        return view('admin.pages.company.create');
    }

    /** new company store
     * ========= store============
     *
     * @param App\Http\Requests\CompanyRequest
     * @return Response
     */
    public function store(CompanyRequest $request)
    {
        if (!$this->hasPermissions(['company.add'])) {
            return $this->permissionDeniedResponse();
        }

        $user = $this->user->create($request, COMPANY_ROLE);
        $user->assignRole('company');
        $result = $this->company->create($request, $user->id);
        toastr()->success('', $result['message'], ['positionClass' => 'toast-top-right']);
        return response()->json([
            'status' => $result['status'],
            'url' => route('admin.company')
        ]);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $lang = Request()->lang;
        $company = $this->company->getById($id);

        return view('admin.pages.company.edit', compact('company', 'lang'));
    }

    /** specific resource update
     *========= update ==========
     *
     * @param App\Http\Requests\CompanyRequest
     * @return Response
     */
    public function update(CompanyRequest $request)
    {
        if (!$this->hasPermissions(['company.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->company->update($request);
        toastr()->success('', $result['message'], ['positionClass' => 'toast-top-right']);
        return response()->json([
            'status' => $result['status'],
            'url' =>  route('admin.company')
        ]);

    }

    /** company delete by id
     * ========= delete =========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['company.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $data = Company::find($id);

        if (!$data) {
            return response()->json(['success' => false]);
        }

        $data->delete();
        return response()->json(['success' => true]);

    }

    /** company gallery delete by id
     * ========= galleryDelete =========
     *
     * @param int id
     * @return Response
     */
    public function galleryDelete($id)
    {
        if (!$this->hasPermissions(['company.gallery.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->company->galleryImageDelete($id);
        return response()->json(['status' => $result['status']]);

    }

    /** specific resource update
     *========= updatePassword ==========
     *
     * @param App\Http\Requests\PasswordRequest
     * @return Response
     */
    public function updatePassword(PasswordRequest $request)
    {
        $result = $this->user->updatePassword($request);
        toastr()->success('', $result['message'], ['positionClass' => 'toast-top-right']);

        return back();
    }

    /** company  status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {

        if (!$this->hasPermissions(['company.status'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->company->statusChange($id);

        return $this->formatResponse($result);

    }

    /** company details by id
     * =========== companyDetails  =========
     *
     * @param int id
     * @return Response
     */
    public function companyDetails($id)
    {
        $company = $this->company->getById($id);

        return view('admin.pages.company.details', compact('company'));
    }

    /** working area delete by id
     * workingAreaDelete
     *
     * @param  int  $id
     * @return Response
     */
    public function workingAreaDelete($id)
    {
        if (!$this->hasPermissions(['company.work.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->company->areaDelete($id);
        return $this->formatResponse($result);

    }

    /** Email Verified
     * ======= emailVerified ========
     *
     * @param  int id
     * @return Response
     */
    public function emailVerified($id)
    {

        if (!$this->hasPermissions(['company.verify'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->company->emailVerified($id);
        return $this->formatResponse($result);

    }

    /** company location by company id
     * ======= emailVerified ========
     *
     * @param  int id
     * @return Response
     */
    public function companyLocation($id)
    {
        $company = $this->company->companyLocation($id);

        return response()->json(['status' => true, 'company' => $company]);
    }
}
