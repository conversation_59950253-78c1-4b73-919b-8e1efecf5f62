<?php if(@isset($widgetContent)): ?>
    <div class="sortable-item accordion-item allowPrimary" data-code="<?php echo e($widgetContent->ui_card_number); ?>">
        <div class="accordion-header">
            <div class="section-name"> <?php echo e($widgetContent->widget?->widget_name); ?>

                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="<?php echo e(route('admin.pages.widget.status.change', $widgetContent->id)); ?>"
                            <?php echo e($widgetContent->status == 1 ? 'checked' : ''); ?> type="checkbox" role="switch"
                            id="<?php echo e($widgetContent->id); ?>">
                        <label class="form-check-label" for="<?php echo e($widgetContent->id); ?>"> </label>
                    </div>

                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="<?php echo e($widgetContent->id); ?>">
                        <i class="bi bi-trash"></i>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse">
            <div class="accordion-body">
                <?php
                $widgetContents= $widgetContent->getTranslation("widget_content",$lang);
               ?>
                <form enctype="multipart/form-data" data-action="<?php echo e(route('admin.pages.widget.save')); ?>" class="form"
                    method="POST">
                    <?php echo csrf_field(); ?>

                    <input type="hidden" name="ui_card_number" value="<?php echo e($widgetContent->ui_card_number); ?>">
                    <input type="hidden" name="page_id" value="<?php echo e($widgetContent->page_id); ?>">
                    <input type="hidden" name="widget_slug" class="widget-slug"
                        value="<?php echo e($widgetContent->widget_slug); ?>">
                    <div class="row">
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Banner Tag')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Enter Banner Tag (e.g., Your Dream Job)')); ?>" name="content[0][banner_tag]"
                                    value="<?php echo e(isset($widgetContents['banner_tag']) ? $widgetContents['banner_tag'] : ''); ?>">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch"
                                           name="content[0][show_banner_vector]" value="1"
                                           <?php echo e((isset($widgetContents['show_banner_vector']) && $widgetContents['show_banner_vector'] == '1') ? 'checked' : ''); ?>

                                           id="showBannerVector">
                                    <label class="form-check-label" for="showBannerVector">
                                        <?php echo e(translate('Show Banner Vector')); ?>

                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Title (HTML Allowed)')); ?></label>
                                <textarea class="username-input" rows="3"
                                    placeholder="<?php echo e(translate('Enter Main Title with HTML: e.g., Discover Work <strong>That <span>Works for You</span></strong>')); ?>"
                                    name="content[0][title]"><?php echo e(isset($widgetContents['title']) ? $widgetContents['title'] : ''); ?></textarea>
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Description')); ?></label>
                                <textarea class="username-input" rows="3"
                                    placeholder="<?php echo e(translate('Enter Description')); ?>"
                                    name="content[0][description]"><?php echo isset($widgetContents['description']) ? $widgetContents['description'] : ''; ?></textarea>
                            </div>
                        </div>


                        <div class="col-sm-3 mb-2">
                            <div class="form-inner ">
                                <label><?php echo e(translate('Keyword One (Live Jobs)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Live Jobs')); ?>" name="content[0][keyword_one]"
                                    value="<?php echo e(isset($widgetContents['keyword_one']) ? $widgetContents['keyword_one'] : ''); ?>">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner ">
                                <label><?php echo e(translate('Keyword Two (Companies)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Companies')); ?>" name="content[0][keyword_two]"
                                    value="<?php echo e(isset($widgetContents['keyword_two']) ? $widgetContents['keyword_two'] : ''); ?>">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Three (Candidates)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Candidates')); ?>" name="content[0][keyword_three]"
                                    value="<?php echo e(isset($widgetContents['keyword_three']) ? $widgetContents['keyword_three'] : ''); ?>">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Four (New Jobs)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('New Jobs')); ?>" name="content[0][keyword_four] "
                                    value="<?php echo e(isset($widgetContents['keyword_four']) ? $widgetContents['keyword_four'] : ''); ?>">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label"><?php echo e(translate('Person Image')); ?> <b>(600px × 800px)</b></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p><?php echo e(translate('Choose an image file or drag it here')); ?></p>
                                    </div>
                                    <input type="file" name="content[0][person_image]" class="dropzone dropzone-image" accept="image/*">
                                </div>
                                <div class="preview-zone <?php echo e(isset($widgetContents['person_image']) && $widgetContents['person_image'] ? '' : 'hidden'); ?>">
                                    <div class="box box-solid">
                                        <div class="box-body">
                                            <?php if(isset($widgetContents['person_image']) && $widgetContents['person_image']): ?>
                                                <div class="img-thumb-wrapper card shadow">
                                                    <span class="remove text-danger"><i class="bi bi-trash"></i></span>
                                                    <img class="img-thumb" width="100" src="<?php echo e(asset('storage/' . $widgetContents['person_image'])); ?>" alt="Person Image"/>
                                                </div>
                                                <input type="hidden" name="content[0][existing_person_image]" value="<?php echo e($widgetContents['person_image']); ?>">
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label"><?php echo e(translate('Background Image')); ?> <b>(1920px × 1080px)</b></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p><?php echo e(translate('Choose an image file or drag it here')); ?></p>
                                    </div>
                                    <input type="file" name="content[0][background_image]" class="dropzone dropzone-image" accept="image/*">
                                </div>
                                <div class="preview-zone <?php echo e(isset($widgetContents['background_image']) && $widgetContents['background_image'] ? '' : 'hidden'); ?>">
                                    <div class="box box-solid">
                                        <div class="box-body">
                                            <?php if(isset($widgetContents['background_image']) && $widgetContents['background_image']): ?>
                                                <div class="img-thumb-wrapper card shadow">
                                                    <span class="remove text-danger"><i class="bi bi-trash"></i></span>
                                                    <img class="img-thumb" width="100" src="<?php echo e(asset('storage/' . $widgetContents['background_image'])); ?>" alt="Background Image"/>
                                                </div>
                                                <input type="hidden" name="content[0][existing_background_image]" value="<?php echo e($widgetContents['background_image']); ?>">
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-12 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label"><?php echo e(translate('Backgroud Image')); ?> <span>*</span></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p><?php echo e(translate('Choose an image file or drag it here')); ?></p>
                                    </div>
                                    <input type="file" name="image" class="dropzone dropzone-image">

                                </div>


                                <div class="preview-zone hidden">
                                    <div class="box box-solid">
                                        <div class="box-body"></div>
                                    </div>
                                </div>
                            </div>
                        </div>






                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow"><?php echo e(translate('Update')); ?></button>
                    </div>
                </form>
            </div>
        </div>

    </div>
<?php else: ?>
    <div class="sortable-item accordion-item allowPrimary" data-code="<?php echo e($randomId); ?>">
        <div class="accordion-header" id="herosection">
            <div class="section-name"> <?php echo e($widgetName); ?>

                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="<?php echo e(route('admin.pages.widget.status.change', $randomId)); ?>" checked
                            type="checkbox" role="switch" id="<?php echo e($randomId); ?>">
                        <label class="form-check-label" for="<?php echo e($randomId); ?>"> </label>
                    </div>
                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="<?php echo e($randomId); ?>">
                        <i class="bi bi-trash"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse show">
            <div class="accordion-body">
                <form enctype="multipart/form-data" data-action="<?php echo e(route('admin.pages.widget.save')); ?>"
                    class="form" method="POST">
                    <?php echo csrf_field(); ?>

                    <input type="hidden" name="ui_card_number" value="<?php echo e($randomId); ?>">
                    <input type="hidden" name="page_id" value="<?php echo e($pageId); ?>">
                    <input type="hidden" name="widget_slug" class="widget-slug" value="<?php echo e($slug); ?>">

                    <div class="row">
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Banner Tag')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Enter Banner Tag (e.g., Your Dream Job)')); ?>" name="content[0][banner_tag]">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch"
                                           name="content[0][show_banner_vector]" value="1" checked
                                           id="showBannerVectorNew">
                                    <label class="form-check-label" for="showBannerVectorNew">
                                        <?php echo e(translate('Show Banner Vector')); ?>

                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Title (HTML Allowed)')); ?></label>
                                <textarea class="username-input" rows="3"
                                    placeholder="<?php echo e(translate('Enter Main Title with HTML: e.g., Discover Work <strong>That <span>Works for You</span></strong>')); ?>"
                                    name="content[0][title]"></textarea>
                            </div>
                        </div>
                        <div class="col-sm-12 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Description')); ?></label>
                                <textarea class="username-input" rows="3"
                                    placeholder="<?php echo e(translate('Enter Description')); ?>"
                                    name="content[0][description]"></textarea>
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner ">
                                <label><?php echo e(translate('Keyword One (Live Jobs)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Live Jobs')); ?>" name="content[0][keyword_one]">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner ">
                                <label><?php echo e(translate('Keyword Two (Companies)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Companies')); ?>" name="content[0][keyword_two]">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Three (Candidates)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Candidates')); ?>" name="content[0][keyword_three]">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-2">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Four (New Jobs)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('New Jobs')); ?>" name="content[0][keyword_four]">
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label"><?php echo e(translate('Person Image')); ?> <b>(600px × 800px)</b></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p><?php echo e(translate('Choose an image file or drag it here')); ?></p>
                                    </div>
                                    <input type="file" name="content[0][person_image]" class="dropzone dropzone-image" accept="image/*">
                                </div>
                                <div class="preview-zone hidden">
                                    <div class="box box-solid">
                                        <div class="box-body"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="form-inner file-upload mb-35">
                                <label class="control-label"><?php echo e(translate('Background Image')); ?> <b>(1920px × 1080px)</b></label>
                                <div class="dropzone-wrapper">
                                    <div class="dropzone-desc">
                                        <i class="glyphicon glyphicon-download-alt"></i>
                                        <p><?php echo e(translate('Choose an image file or drag it here')); ?></p>
                                    </div>
                                    <input type="file" name="content[0][background_image]" class="dropzone dropzone-image" accept="image/*">
                                </div>
                                <div class="preview-zone hidden">
                                    <div class="box box-solid">
                                        <div class="box-body"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow"><?php echo e(translate('Save')); ?></button>
                    </div>
                </form>
            </div>
        </div>

    </div>
<?php endif; ?>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/admin/pages/widgets/hero-section.blade.php ENDPATH**/ ?>