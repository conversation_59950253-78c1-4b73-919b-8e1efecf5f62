<?php if(@isset($widgetContent)): ?>
    <div class="sortable-item accordion-item allowPrimary" data-code="<?php echo e($widgetContent->ui_card_number); ?>">
        <div class="accordion-header">
            <div class="section-name"> <?php echo e($widgetContent->widget?->widget_name); ?>

                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="<?php echo e(route('admin.pages.widget.status.change', $widgetContent->id)); ?>"
                            <?php echo e($widgetContent->status == 1 ? 'checked' : ''); ?> type="checkbox" role="switch"
                            id="<?php echo e($widgetContent->id); ?>">
                        <label class="form-check-label" for="<?php echo e($widgetContent->id); ?>"> </label>
                    </div>

                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="<?php echo e($widgetContent->id); ?>">
                        <i class="bi bi-trash"></i>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse">
            <div class="accordion-body">
                <?php
                    $widgetContents = $widgetContent->getTranslation('widget_content', $lang);
                ?>
                <form enctype="multipart/form-data" data-action="<?php echo e(route('admin.pages.widget.save')); ?>" class="form"
                    method="POST">
                    <?php echo csrf_field(); ?>

                    <input type="hidden" name="ui_card_number" value="<?php echo e($widgetContent->ui_card_number); ?>">
                    <input type="hidden" name="page_id" value="<?php echo e($widgetContent->page_id); ?>">
                    <input type="hidden" name="widget_slug" class="widget-slug"
                        value="<?php echo e($widgetContent->widget_slug); ?>">
                    <div class="row">
                        <!-- Banner Tag -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Banner Tag')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Your Dream Job')); ?>" name="content[0][banner_tag]"
                                    value="<?php echo e(isset($widgetContents['banner_tag']) ? $widgetContents['banner_tag'] : 'Your Dream Job'); ?>">
                            </div>
                        </div>

                        <!-- Title (HTML Allowed) -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Title (HTML Allowed)')); ?></label>
                                <textarea class="username-input" rows="2"
                                    placeholder="<?php echo e(translate('Discover Work <strong>That <span>Works for You</span></strong>')); ?>"
                                    name="content[0][title]"><?php echo e(isset($widgetContents['title']) ? $widgetContents['title'] : 'Discover Work <strong>That <span>Works for You</span></strong>'); ?></textarea>
                                <small class="text-muted"><?php echo e(translate('HTML tags allowed for styling')); ?></small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Description')); ?></label>
                                <textarea class="username-input" rows="3" placeholder="<?php echo e(translate('Browse, apply, and get hired faster...')); ?>"
                                    name="content[0][description]"><?php echo e(isset($widgetContents['description']) ? $widgetContents['description'] : 'Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!'); ?></textarea>
                            </div>
                        </div>



                        <!-- Keywords Section -->
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword One (Live Jobs)')); ?></label>
                                <input type="text" class="username-input" placeholder="<?php echo e(translate('Live Jobs')); ?>"
                                    name="content[0][keyword_one]"
                                    value="<?php echo e(isset($widgetContents['keyword_one']) ? $widgetContents['keyword_one'] : 'Live Jobs'); ?>">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Two (Companies)')); ?></label>
                                <input type="text" class="username-input" placeholder="<?php echo e(translate('Companies')); ?>"
                                    name="content[0][keyword_two]"
                                    value="<?php echo e(isset($widgetContents['keyword_two']) ? $widgetContents['keyword_two'] : 'Companies'); ?>">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Three (Candidates)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Candidates')); ?>" name="content[0][keyword_three]"
                                    value="<?php echo e(isset($widgetContents['keyword_three']) ? $widgetContents['keyword_three'] : 'Candidates'); ?>">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Four (New Jobs)')); ?></label>
                                <input type="text" class="username-input" placeholder="<?php echo e(translate('New Jobs')); ?>"
                                    name="content[0][keyword_four]"
                                    value="<?php echo e(isset($widgetContents['keyword_four']) ? $widgetContents['keyword_four'] : 'New Jobs'); ?>">
                            </div>
                        </div>


                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow"><?php echo e(translate('Update')); ?></button>
                    </div>
                </form>
            </div>
        </div>

    </div>
<?php else: ?>
    <div class="sortable-item accordion-item allowPrimary" data-code="<?php echo e($randomId); ?>">
        <div class="accordion-header" id="herosection">
            <div class="section-name"> <?php echo e($widgetName); ?>

                <div class="collapsed d-flex">
                    <div class="form-check form-switch me-2">
                        <input class="form-check-input status-change"
                            data-action="<?php echo e(route('admin.pages.widget.status.change', $randomId)); ?>" checked
                            type="checkbox" role="switch" id="<?php echo e($randomId); ?>">
                        <label class="form-check-label" for="<?php echo e($randomId); ?>"> </label>
                    </div>
                    <div class="collapsed-action-btn edit-action action-icon me-2">
                        <i class="bi bi-pencil-square"></i>
                    </div>
                    <div class="action-icon delete-action" data-id="<?php echo e($randomId); ?>">
                        <i class="bi bi-trash"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-collapse collapse show">
            <div class="accordion-body">
                <form enctype="multipart/form-data" data-action="<?php echo e(route('admin.pages.widget.save')); ?>"
                    class="form" method="POST">
                    <?php echo csrf_field(); ?>

                    <input type="hidden" name="ui_card_number" value="<?php echo e($randomId); ?>">
                    <input type="hidden" name="page_id" value="<?php echo e($pageId); ?>">
                    <input type="hidden" name="widget_slug" class="widget-slug" value="<?php echo e($slug); ?>">

                    <div class="row">
                        <!-- Banner Tag -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Banner Tag')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Your Dream Job')); ?>" name="content[0][banner_tag]"
                                    value="Your Dream Job">
                            </div>
                        </div>

                        <!-- Title (HTML Allowed) -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Title (HTML Allowed)')); ?></label>
                                <textarea class="username-input" rows="2"
                                    placeholder="<?php echo e(translate('Discover Work <strong>That <span>Works for You</span></strong>')); ?>"
                                    name="content[0][title]">Discover Work <strong>That <span>Works for You</span></strong></textarea>
                                <small class="text-muted"><?php echo e(translate('HTML tags allowed for styling')); ?></small>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="col-sm-12 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Description')); ?></label>
                                <textarea class="username-input" rows="3"
                                    placeholder="<?php echo e(translate('Browse, apply, and get hired faster...')); ?>" name="content[0][description]">Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!</textarea>
                            </div>
                        </div>



                        <!-- Keywords Section -->
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword One (Live Jobs)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Live Jobs')); ?>" name="content[0][keyword_one]"
                                    value="Live Jobs">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Two (Companies)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Companies')); ?>" name="content[0][keyword_two]"
                                    value="Companies">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Three (Candidates)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('Candidates')); ?>" name="content[0][keyword_three]"
                                    value="Candidates">
                            </div>
                        </div>
                        <div class="col-sm-3 mb-3">
                            <div class="form-inner">
                                <label><?php echo e(translate('Keyword Four (New Jobs)')); ?></label>
                                <input type="text" class="username-input"
                                    placeholder="<?php echo e(translate('New Jobs')); ?>" name="content[0][keyword_four]"
                                    value="New Jobs">
                            </div>
                        </div>
                    </div>

                    <div class="button-area text-end">
                        <button type="submit"
                            class="eg-btn btn--green medium-btn shadow"><?php echo e(translate('Save')); ?></button>
                    </div>
                </form>
            </div>
        </div>

    </div>
<?php endif; ?>


<?php /**PATH D:\xampp\htdocs\empzio\resources\views/admin/pages/widgets/hero-section.blade.php ENDPATH**/ ?>