<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Admin\PackageAddonItem;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PackageAddonItemController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        if ($request->ajax()) {
            $data = PackageAddonItem::latest()->get();
            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn('action', function ($data) {
                    $btn = '<a href="javascript:void(0)" class="eg-btn add--btn"><i class="bi bi-pencil-square editAddonItem" data-id="' . $data->id . '"></i></a>';
                    $btn = $btn . '<a href="javascript:void(0)" class="eg-btn delete--btn btn-gaps"><i class="bi bi-trash deleteAddonItem" data-id="' . $data->id . '"></i></a>';
                    return $btn;
                })

                ->editColumn('Name', function ($data) {
                    return $data->name;
                })
                ->rawColumns(['action', 'Name'])
                ->make(true);
          }
        return view('admin.pages.package.package_addon_item.index');
    }



    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //

        if (! empty($request->addon_item) && count($request->addon_item) > 1) {
            foreach ($request->addon_item as $key => $item) {
                $addonItem = new PackageAddonItem;
                $addonItem->package_addon_id = $request->package_addon_id;
                $addonItem->name = $item;
                $addonItem->save();
            }

            return response()->json(['success' => true]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        return PackageAddonItem::find($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $addonItem = PackageAddonItem::find($request->id);
        $addonItem->package_addon_id = $request->package_addon_id;
        $addonItem->name = $request->addon_name;
        $addonItem->update();
        return response()->json(['success' => true]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $addonItem = PackageAddonItem::find($id);
        if ($addonItem) {
            $addonItem->delete();
            return response()->json(['success' => true]);
        }
        return response()->json(['success' => false]);
    }
}
