<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\JobRoleRequest;
use App\Repositories\Admin\JobRoleRepository;
use Illuminate\Http\Request;

class JobRoleController extends Controller
{
    //

    public function __construct(protected JobRoleRepository $jobRole) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->jobRole->content();
        }

        return view('admin.pages.job-role.index');
    }

    /** new Resource store
     * ========= store============
     *
     * @param JobRoleRequest
     * @return Response
     */
    public function store(JobRoleRequest $request)
    {
       if ($this->hasPermissions(['jobrole.add', 'jobrole.edit']) || auth()->user()->hasRole('company')) {
        $result = $this->jobRole->create($request);
        return $this->formatResponse($result);
        }
        return $this->permissionDeniedResponse();
    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $jobRole = $this->jobRole->getById($id);

        $data['name'] = $jobRole->getTranslation('name', Request()->lang);
        $data['id'] = $jobRole->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['jobrole.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jobRole->destroy($id);
        return $this->formatResponse($result);

    }

    /** Job Type status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['jobrole.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jobRole->statusChange($id);
        return $this->formatResponse($result);

    }

    /**  Job Role search
     * ========= roleSearch=======
     *
     * @param Request
     */
    public function roleSearch(Request $request)
    {

        if ($request->q && $request->q != '') {
            $result = $this->jobRole->roleSearch($request->q);
            if ($result['status'] == true) {
                return response()->json(['items' => $result['roles']]);
            }
        }
    }
}
