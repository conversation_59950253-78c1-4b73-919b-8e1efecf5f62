<script>
    (function($) {
        "use strict";

        function showLoader() {
            $("#eg-overlay").show();
        }

        function hideLoader() {
            $("#eg-overlay").hide();
        }
        $(".clear-applicatin-filter").hide();

        $(document).on('submit', '.search-form', function(e) {
            e.preventDefault();
            filterSearchJob();
            $(".clear-applicatin-filter").show();
        })
        $(document).on("change", '.filter-option', function(e) {
            $(".clear-applicatin-filter").show();
            filterSearchJob();
        })
        $(document).on('click', '.page-item .page-link', function(e) {
            e.preventDefault();
            let page = $(this).attr('href').split('page=')[1];
            filterSearchJob(page);
        });

        function filterSearchJob(page = null) {
            let blog_category = getFilterData('blog-category');
            let search_key = $("#search_key").val();
            let widget_name = $("#widget_name").val();
            let style = $("#style").val();
            let item_show = $("#item_show").val();
            let action = window.location.href + "?search_key=" + search_key + "&widget_name=" + widget_name +
                "&style=" + style + "&blog_category=" + blog_category + "&item_show=" + item_show + "&page=" + page;
            $.ajax({
                type: "GET",
                url: action,
                beforeSend: showLoader,
                success: function(data) {
                    if (data.status == true) {
                        $("#standard-blog").html(`${data.blogView}`);
                        hideLoader()
                    }
                },
            })
        }

        $(document).on('click', '.clear-applicatin-filter', function() {
            location.reload();

        })

        function getFilterData(className) {
            var filter = [];
            $('.' + className + ':checked').each(function() {
                filter.push($(this).val());
            });
            return filter;
        }
    })(jQuery);
</script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/front/blog-search.blade.php ENDPATH**/ ?>