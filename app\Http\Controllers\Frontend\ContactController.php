<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Admin\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    public function contact(Request $request)
    {
        try {
            /*
             * @method array validate(array $rules, ...$params)
             * Validate Your Data from Request as $request
             * */
            $validate = $request->validate([
                'name' => 'required',
                'email' => 'required',
                'phone' => 'required',
                'message' => 'required',
            ]);

            if (!$validate) {
                toastr()->error('', translate('Does not save Contact Us'), ['positionClass' => 'toast-top-right']);
                return redirect()->back();
            }

            /*
                 * App\Models\Admin\Contact model
                 * If validate then save this form .
                 * */
            $contact = Contact::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'message' => $request->message,
                'company_name' => $request->company_name,
            ]);
            /*
                 * after save show the toaster message with success message
                 * Brian2694\Toastr\Facades for Toaster message
                 * */
            if ($contact) {
                toastr()->success('', translate('Contact Us Successfully Stored'), ['positionClass' => 'toast-top-right']);
                return redirect()->back();
            }
        } catch (\Exception $e) {
            Log::error(translate('Does not Contact Us') . " " . $e->getMessage() . ' ' . $e->getFile() . ' ' . $e->getLine());
            toastr()->error('', $e->getMessage(), ['positionClass' => 'toast-top-right']);

            return redirect()->back();
        }
    }
}
