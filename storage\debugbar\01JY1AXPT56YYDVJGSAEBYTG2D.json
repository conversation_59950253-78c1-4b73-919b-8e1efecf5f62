{"__meta": {"id": "01JY1AXPT56YYDVJGSAEBYTG2D", "datetime": "2025-06-18 10:56:26", "utime": **********.950568, "method": "GET", "uri": "/admin/company?draw=1&columns%5B0%5D%5Bdata%5D=Company&columns%5B0%5D%5Bname%5D=Company&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=OwnerName&columns%5B1%5D%5Bname%5D=OwnerName&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=Organization%2FCountry&columns%5B2%5D%5Bname%5D=Organization%2FCountry&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=Establishment+Date&columns%5B3%5D%5Bname%5D=Establishment+Date&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=Verified&columns%5B4%5D%5Bname%5D=Verified&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=true&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=Status&columns%5B5%5D%5Bname%5D=Status&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=true&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=action&columns%5B6%5D%5Bname%5D=action&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=false&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=0&order%5B0%5D%5Bdir%5D=asc&start=0&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&_=1750244184842", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.428695, "end": **********.950582, "duration": 0.5218870639801025, "duration_str": "522ms", "measures": [{"label": "Booting", "start": **********.428695, "relative_start": 0, "end": **********.666859, "relative_end": **********.666859, "duration": 0.*****************, "duration_str": "238ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.666873, "relative_start": 0.*****************, "end": **********.950584, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.676841, "relative_start": 0.*****************, "end": **********.680741, "relative_end": **********.680741, "duration": 0.0039000511169433594, "duration_str": "3.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.948016, "relative_start": 0.****************, "end": **********.94841, "relative_end": **********.94841, "duration": 0.0003941059112548828, "duration_str": "394μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/company", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "controller": "App\\Http\\Controllers\\Backend\\CompanyController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.company", "prefix": "admin/company", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/CompanyController.php:23-30</a>"}, "queries": {"count": 77, "nb_statements": 77, "nb_visible_statements": 77, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.041869999999999984, "accumulated_duration_str": "41.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.697283, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 1.099}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": "middleware", "name": "prevent-back-history", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Middleware\\PreventBackHistory.php", "line": 19}], "start": **********.706932, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 1.099, "width_percent": 1.242}, {"sql": "select `companies`.*, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id` and `job_deadline` >= '2025-06-18 10:56:26' and `status` = 1) as `active_jobs_count` from `companies` order by `id` desc", "type": "query", "params": [], "bindings": ["2025-06-18 10:56:26", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, {"index": 16, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 46}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/CompanyController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CompanyController.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.719275, "duration": 0.00651, "duration_str": "6.51ms", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:35", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=35", "ajax": false, "filename": "CompanyRepository.php", "line": "35"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 2.341, "width_percent": 15.548}, {"sql": "select * from `company_types` where `company_types`.`id` in (1, 2, 3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/CompanyController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CompanyController.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.729165, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:35", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=35", "ajax": false, "filename": "CompanyRepository.php", "line": "35"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 17.889, "width_percent": 1.935}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 65 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.750483, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 19.823, "width_percent": 1.194}, {"sql": "select * from `users` where `users`.`id` = 271 limit 1", "type": "query", "params": [], "bindings": [271], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.752586, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 21.017, "width_percent": 2.245}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 20 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.754844, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 23.262, "width_percent": 1.027}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 64 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7587729, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 24.289, "width_percent": 0.931}, {"sql": "select * from `users` where `users`.`id` = 265 limit 1", "type": "query", "params": [], "bindings": [265], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.760146, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 25.221, "width_percent": 0.597}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 8 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.761946, "duration": 0.00047999999*********, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 25.818, "width_percent": 1.146}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 63 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.765559, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 26.964, "width_percent": 0.979}, {"sql": "select * from `users` where `users`.`id` = 264 limit 1", "type": "query", "params": [], "bindings": [264], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.766988, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 27.944, "width_percent": 0.669}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 1 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.768348, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 28.612, "width_percent": 0.788}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 62 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.772475, "duration": 0.00047999999*********, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 29.401, "width_percent": 1.146}, {"sql": "select * from `users` where `users`.`id` = 263 limit 1", "type": "query", "params": [], "bindings": [263], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.774028, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 30.547, "width_percent": 0.836}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 60 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.778254, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 31.383, "width_percent": 4.132}, {"sql": "select * from `users` where `users`.`id` = 257 limit 1", "type": "query", "params": [], "bindings": [257], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.781187, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 35.515, "width_percent": 0.931}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 15 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.782835, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 36.446, "width_percent": 1.123}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 59 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.787255, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 37.569, "width_percent": 1.6}, {"sql": "select * from `users` where `users`.`id` = 256 limit 1", "type": "query", "params": [], "bindings": [256], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.789138, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 39.169, "width_percent": 1.314}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 12 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7909698, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 40.482, "width_percent": 1.027}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 58 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.796529, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 41.509, "width_percent": 1.935}, {"sql": "select * from `users` where `users`.`id` = 255 limit 1", "type": "query", "params": [], "bindings": [255], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.798684, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 43.444, "width_percent": 0.908}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 16 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.800298, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 44.352, "width_percent": 0.955}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 57 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.804351, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 45.307, "width_percent": 1.194}, {"sql": "select * from `users` where `users`.`id` = 254 limit 1", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8060849, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 46.501, "width_percent": 1.027}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 11 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8079271, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 47.528, "width_percent": 1.075}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 55 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8131192, "duration": 0.00047999999*********, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 48.603, "width_percent": 1.146}, {"sql": "select * from `users` where `users`.`id` = 225 limit 1", "type": "query", "params": [], "bindings": [225], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8148472, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 49.749, "width_percent": 1.051}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 17 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8166351, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 50.8, "width_percent": 1.075}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 54 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8209622, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 51.875, "width_percent": 1.123}, {"sql": "select * from `users` where `users`.`id` = 224 limit 1", "type": "query", "params": [], "bindings": [224], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.822659, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 52.997, "width_percent": 0.955}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 13 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.824302, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 53.953, "width_percent": 0.979}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 53 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.829309, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 54.932, "width_percent": 1.505}, {"sql": "select * from `users` where `users`.`id` = 223 limit 1", "type": "query", "params": [], "bindings": [223], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.831141, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 56.437, "width_percent": 0.931}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 52 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8354652, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 57.368, "width_percent": 1.075}, {"sql": "select * from `users` where `users`.`id` = 222 limit 1", "type": "query", "params": [], "bindings": [222], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.837028, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 58.443, "width_percent": 0.812}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 19 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.838629, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 59.255, "width_percent": 1.075}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 51 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.842537, "duration": 0.00041999999*********, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 60.33, "width_percent": 1.003}, {"sql": "select * from `users` where `users`.`id` = 221 limit 1", "type": "query", "params": [], "bindings": [221], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.8440142, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 61.333, "width_percent": 1.576}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 14 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8463762, "duration": 0.00047999999*********, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 62.909, "width_percent": 1.146}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 50 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [50], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8505569, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 64.055, "width_percent": 1.337}, {"sql": "select * from `users` where `users`.`id` = 219 limit 1", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.85221, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 65.393, "width_percent": 0.74}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 49 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [49], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.855475, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.133, "width_percent": 0.764}, {"sql": "select * from `users` where `users`.`id` = 218 limit 1", "type": "query", "params": [], "bindings": [218], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.85674, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.898, "width_percent": 0.573}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 3 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8580241, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 67.471, "width_percent": 0.74}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 48 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.861783, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 68.211, "width_percent": 1.481}, {"sql": "select * from `users` where `users`.`id` = 217 limit 1", "type": "query", "params": [], "bindings": [217], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.863548, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 69.692, "width_percent": 0.836}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 2 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.865005, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 70.528, "width_percent": 0.764}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 47 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8685448, "duration": 0.00041999999*********, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 71.292, "width_percent": 1.003}, {"sql": "select * from `users` where `users`.`id` = 216 limit 1", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.869957, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 72.295, "width_percent": 0.621}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 9 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8712242, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 72.916, "width_percent": 0.645}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 46 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8748, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 73.561, "width_percent": 0.908}, {"sql": "select * from `users` where `users`.`id` = 214 limit 1", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.876155, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 74.469, "width_percent": 0.573}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 45 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8804069, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 75.042, "width_percent": 0.979}, {"sql": "select * from `users` where `users`.`id` = 213 limit 1", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.881819, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 76.021, "width_percent": 0.597}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 5 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.88321, "duration": 0.00041999999*********, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 76.618, "width_percent": 1.003}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 44 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.886566, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.621, "width_percent": 0.812}, {"sql": "select * from `users` where `users`.`id` = 212 limit 1", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.887861, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 78.433, "width_percent": 0.573}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 4 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8891058, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 79.006, "width_percent": 0.597}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 43 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.89258, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 79.604, "width_percent": 0.74}, {"sql": "select * from `users` where `users`.`id` = 203 limit 1", "type": "query", "params": [], "bindings": [203], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.893839, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.344, "width_percent": 0.549}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 41 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8980129, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.893, "width_percent": 0.979}, {"sql": "select * from `users` where `users`.`id` = 192 limit 1", "type": "query", "params": [], "bindings": [192], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.899397, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 81.872, "width_percent": 0.573}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 10 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.900645, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 82.446, "width_percent": 0.573}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 40 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.904209, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 83.019, "width_percent": 0.955}, {"sql": "select * from `users` where `users`.`id` = 191 limit 1", "type": "query", "params": [], "bindings": [191], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.90557, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 83.974, "width_percent": 0.74}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 39 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.909302, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 84.715, "width_percent": 1.075}, {"sql": "select * from `users` where `users`.`id` = 177 limit 1", "type": "query", "params": [], "bindings": [177], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.911276, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 85.789, "width_percent": 4.012}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 35 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.916977, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 89.802, "width_percent": 1.29}, {"sql": "select * from `users` where `users`.`id` = 167 limit 1", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.918517, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 91.091, "width_percent": 1.075}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 26 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.9229808, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 92.166, "width_percent": 1.457}, {"sql": "select * from `users` where `users`.`id` = 158 limit 1", "type": "query", "params": [], "bindings": [158], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.9247272, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 93.623, "width_percent": 1.075}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 20 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.929621, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 94.698, "width_percent": 1.958}, {"sql": "select * from `users` where `users`.`id` = 152 limit 1", "type": "query", "params": [], "bindings": [152], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.931581, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 96.656, "width_percent": 1.123}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 1 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.935562, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 97.779, "width_percent": 1.242}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.9370098, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 99.021, "width_percent": 0.979}]}, "models": {"data": {"App\\Models\\Admin\\CompanyTypeTranslation": {"value": 70, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyTypeTranslation.php&line=1", "ajax": false, "filename": "CompanyTypeTranslation.php", "line": "?"}}, "App\\Models\\Admin\\Company": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\User": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\CompanyType": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=1", "ajax": false, "filename": "CompanyType.php", "line": "?"}}, "App\\Models\\Admin\\CompanyTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyTranslation.php&line=1", "ajax": false, "filename": "CompanyTranslation.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 148, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/lang/flags/in.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/company?_=1750244184842&columns%5B0%5D%5Bdata%5D=Company&columns%5B0%5D%...", "action_name": "admin.company", "controller_action": "App\\Http\\Controllers\\Backend\\CompanyController@index", "uri": "GET admin/company", "controller": "App\\Http\\Controllers\\Backend\\CompanyController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/company", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/CompanyController.php:23-30</a>", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "duration": "529ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1521728941 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Company</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Company</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"9 characters\">OwnerName</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">OwnerName</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Organization/Country</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Organization/Country</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Establishment Date</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Establishment Date</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Verified</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Verified</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1750244184842</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1521728941\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-989641083 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-989641083\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-957220120 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/admin/company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6InkvMGJ6ZEdWVHBYL3FUaFVkMVBlNGc9PSIsInZhbHVlIjoiZHdSSTM2UzY2WVo4WWZhNDAzRCt6K2EyVkFUdlVVR25ORjFzalpNOUZXcGlhQmZNLzRGWS9zRUZqOUVJZUY0RjhtTXN1TjN5bUREczdHWGxLdWM5QTJhdHBrd1B2TldVTTJJK0Yrbmd4SHYvd2ozMkNQdW96cnQ5MFpCLzY4TysiLCJtYWMiOiIyMjNhN2RmNjVjMzFhZjUxNWZmMWQ5YjlmOTQ0YjJiNjM1MjE1NGEwODNhNWIxNjgzYmI1MDhjM2NlYjg3MTU3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlM1Q2Zpb05aRVRxcmIxWUVyL0pybkE9PSIsInZhbHVlIjoiSWhtZnBsVFlJWldFb1RZZW9qU0liZjk2UVBxRVl1ZlI2WkVQbksxUlVNZXh5aC8zL2NCcHRWdDRzVGZkRExDa0U5MTJPbnpVMWsxL21TNzd3NVdpdnpydXF2eGFEMXF1V3o4WEJrRjcrYkVQOWFkYWlHdE8yRzNYTElKd0VmSWsiLCJtYWMiOiI4NDAwMDRiNmI2ZGIyYmJjMWU5YTFmYmEzOGFmY2JkY2M3MGFmMjRlNGU5ZmIwNWUxZTFmNjJhM2Y2ZjFlOWYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-957220120\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1876486143 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qUd4p9n8BUUDYUEq0qgc1rceEYaWteQGcYoDZANF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876486143\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1198061448 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 10:56:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198061448\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/storage/lang/flags/in.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/company?_=1750244184842&columns%5B0%5D%5Bdata%5D=Company&columns%5B0%5D%...", "action_name": "admin.company", "controller_action": "App\\Http\\Controllers\\Backend\\CompanyController@index"}, "badge": null}}