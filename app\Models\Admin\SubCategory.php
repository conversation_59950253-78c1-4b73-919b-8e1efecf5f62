<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class SubCategory extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function getTranslation($field = '', $lang = false)
    {
        $lang = $lang == false ? App::getLocale() : $lang;
        $subCategoryTranslation = $this->subCategoryTranslation->where('lang', $lang)->first();

        return $subCategoryTranslation != null ? $subCategoryTranslation->$field : $this->$field;
    }

    public function subCategoryTranslation()
    {
        return $this->hasMany(SubCategoryTranslation::class, 'subcategory_id', 'id');
    }
}
