<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class Testimonial extends Model
{
    use HasFactory;

    protected $fillable = [
        'image', 'name', 'title', 'description', 'rating',
    ];

    public function getTranslation($field = '', $lang = false)
    {
        $lang = $lang == false ? App::getLocale() : $lang;
        $testimonial = $this->testimonialTranslation->where('lang', $lang)->first();

        return $testimonial != null ? $testimonial->$field : $this->$field;
    }

    public function testimonialTranslation()
    {
        return $this->hasMany(TestimonialTranslation::class);
    }
}
