{"__meta": {"id": "01JY3S403JGQSVQBD41F1G0K17", "datetime": "2025-06-18 21:43:02", "utime": **********.003523, "method": "POST", "uri": "/dlanguage-change", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.785435, "end": **********.003537, "duration": 0.21810197830200195, "duration_str": "218ms", "measures": [{"label": "Booting", "start": **********.785435, "relative_start": 0, "end": **********.973051, "relative_end": **********.973051, "duration": 0.*****************, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.973061, "relative_start": 0.*****************, "end": **********.003538, "relative_end": 9.5367431640625e-07, "duration": 0.030476808547973633, "duration_str": "30.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.982012, "relative_start": 0.****************, "end": **********.984993, "relative_end": **********.984993, "duration": 0.002980947494506836, "duration_str": "2.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.001293, "relative_start": 0.*****************, "end": **********.001684, "relative_end": **********.001684, "duration": 0.0003910064697265625, "duration_str": "391μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST dlanguage-change", "middleware": "web, xss", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@languageChange<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=35\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "language.change", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=35\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:35-41</a>"}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0005600000000000001, "accumulated_duration_str": "560μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `languages` where `code` = 'in' limit 1", "type": "query", "params": [], "bindings": ["in"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/Admin/LanguageRepostiory.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\Admin\\LanguageRepostiory.php", "line": 240}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/HomeController.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Http\\Controllers\\Frontend\\HomeController.php", "line": 37}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\jobes-application\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9935749, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "LanguageRepostiory.php:240", "source": {"index": 16, "namespace": null, "name": "app/Repositories/Admin/LanguageRepostiory.php", "file": "D:\\xampp\\htdocs\\jobes-application\\app\\Repositories\\Admin\\LanguageRepostiory.php", "line": 240}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FRepositories%2FAdmin%2FLanguageRepostiory.php&line=240", "ajax": false, "filename": "LanguageRepostiory.php", "line": "240"}, "connection": "jobes", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Admin\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FModels%2FAdmin%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "locale": "in", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/frontendassets/images/icon/apply-ellipse.svg\"\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/dlanguage-change", "action_name": "language.change", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@languageChange", "uri": "POST dlanguage-change", "controller": "App\\Http\\Controllers\\Frontend\\HomeController@languageChange<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=35\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fjobes-application%2Fapp%2FHttp%2FControllers%2FFrontend%2FHomeController.php&line=35\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/HomeController.php:35-41</a>", "middleware": "web, xss", "duration": "219ms", "peak_memory": "24MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-345603625 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-345603625\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-494966815 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">in</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494966815\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1080677615 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6IkJvRkNHelVlOThlaldqNkJDYm1Zdmc9PSIsInZhbHVlIjoiV0dFbjF1YklZUW1wK0tsN0ZjTExuWEhQbHZQQWp5STY5OUxXeWJpMUNaYUNOU2hwL1IwakdWRFFuclVkMjgwUWJkcEh3Wncvb3RJUlJJUWYxdWZySkg2L0hCRTdGU2ZGVDJSTjQreVo5QXVSWUl5a1FzcllVVWRBKzB4TUhmOFAiLCJtYWMiOiIzM2U5NTNlMGU3NGQ0NzJhNjA5MzY3MTI3MmNjMWQyNzlhYjdhMTVmYjhmMDA4OTM5ZDIyYzk0M2E5YTVjNmE1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImlWMWJXNW9vZ1MrSGV2akE1VTN1c0E9PSIsInZhbHVlIjoibWFpdnh0MTByL1RQdkJqTlNKdTZzM1plQ3VUYVljTDlOREZpeGlOSjE0VWRybTRscVk3WCt5R1VHOVdOdUhzNWRoLzBhWmtMTEVoVTh5RG03RFFSL2tGZW1hOEZjd3ArSVVxUWp5aWluRTNINVNwT1RVOUNLN2hscEw5S2pqOXQiLCJtYWMiOiIyZWFhYzkwMmVkYTg1YmRiZGM0MmYyNWIxZjAwNjM5Y2Y4ZDBkZDNjOGY1OThiZjI1MzBjZjRhZWEyNTJiMjUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080677615\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1335288080 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">goYAETE0L476RtcoTEIFmskXRueE29TUnO1QyiU6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335288080\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-592733733 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 19 Jun 2025 09:43:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592733733\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-533300135 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cXtNxYYqziWz4MJYg1w91ZNt601mCpmHfsCpStXa</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"66 characters\">http://127.0.0.1:8001/frontendassets/images/icon/apply-ellipse.svg</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533300135\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8001/dlanguage-change", "action_name": "language.change", "controller_action": "App\\Http\\Controllers\\Frontend\\HomeController@languageChange"}, "badge": null}}