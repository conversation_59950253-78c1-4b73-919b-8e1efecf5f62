<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Admin\PackageItem;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PackageItemController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = PackageItem::latest()->get();
            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn('action', function ($data) {
                    $btn = '<a href="javascript:void(0)" class="eg-btn add--btn"><i class="bi bi-pencil-square editPackageItem" data-id="' . $data->id . '"></i></a>';
                    $btn = $btn . '<a href="javascript:void(0)" class="eg-btn delete--btn btn-gaps"><i class="bi bi-trash deletePackageItem" data-id="' . $data->id . '"></i></a>';

                    return $btn;
                })

                ->editColumn('Name', function ($data) {
                    return $data->name;
                })
                ->rawColumns(['action', 'Name'])
                ->make(true);
        }
        return view('admin.pages.package.package_item.index');
    }


    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        if (! empty($request->item_name) && count($request->item_name) > 1) {
            foreach ($request->item_name as $key => $itemName) {
                $packageItem = new PackageItem;
                $packageItem->package_id = $request->package_id;
                $packageItem->name = $itemName;
                $packageItem->save();
            }
        }
        return response()->json(['success' => true]);
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        return PackageItem::find($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $packageItem = PackageItem::where('id', $request->id)->first();
        $packageItem->name = $request->item_name;
        $packageItem->update();
        if ($packageItem) {
            return response()->json(['success' => true]);
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $packageItem = PackageItem::find($id);
        if ($packageItem) {
            $packageItem->delete();
            return response()->json(['success' => true]);
        }
        return response()->json(['success' => false]);
    }
}
