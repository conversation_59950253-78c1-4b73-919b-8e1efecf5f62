@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Mulish:ital,wght@0,200..1000;1,200..1000&display=swap");
:root {
  --font-inter: "Inter", sans-serif;
  --font-mulish: "Mulish", sans-serif;
  --white-color: #ffffff;
  --primary-color1: #022c1f;
  --primary-color1-opc: 2, 44, 31;
  --primary-color2: #059669;
  --primary-color2-opc: 5, 150, 105;
  --primary-color3: #061421;
  --title-color: #1f1f1f;
  --title-color2: #022c1f;
  --border-color: #eeeeee;
  --paragraph-color: #585858;
}

/*================================================
02. Mixins Css
=================================================*/
/*================================================
  03. Global Css
  =================================================*/
html {
  font-size: 100%;
  scroll-behavior: smooth;
}

input {
  border: none;
  outline: none;
}

button {
  outline: none;
  border: none;
}

i.bx {
  vertical-align: middle;
}

img {
  max-width: 100%;
  height: auto;
}

a {
  text-decoration: none;
}

p {
  font-family: var(--font-inter);
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  color: var(--paragraph-color);
}

.pt-120 {
  padding-top: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .pt-120 {
    padding-top: 100px;
  }
}
@media (max-width: 991px) {
  .pt-120 {
    padding-top: 90px;
  }
}

.pb-120 {
  padding-bottom: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .pb-120 {
    padding-bottom: 100px;
  }
}
@media (max-width: 991px) {
  .pb-120 {
    padding-bottom: 90px;
  }
}

.pt-100 {
  padding-top: 100px;
}
@media (max-width: 991px) {
  .pt-100 {
    padding-top: 80px;
  }
}

.pb-100 {
  padding-bottom: 100px;
}
@media (max-width: 991px) {
  .pb-100 {
    padding-bottom: 80px;
  }
}

.pt-90 {
  padding-top: 90px;
}
@media (max-width: 991px) {
  .pt-90 {
    padding-top: 80px;
  }
}
@media (max-width: 767px) {
  .pt-90 {
    padding-top: 70px;
  }
}

.pb-90 {
  padding-bottom: 90px;
}
@media (max-width: 991px) {
  .pb-90 {
    padding-bottom: 80px;
  }
}
@media (max-width: 767px) {
  .pb-90 {
    padding-bottom: 70px;
  }
}

.pb-80 {
  padding-bottom: 80px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .pb-80 {
    padding-bottom: 60px;
  }
}

.pb-65 {
  padding-bottom: 65px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-10 {
  padding-bottom: 10px;
}

.mt-120 {
  margin-top: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .mt-120 {
    margin-top: 100px;
  }
}
@media (max-width: 991px) {
  .mt-120 {
    margin-top: 90px;
  }
}

.mb-120 {
  margin-bottom: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .mb-120 {
    margin-bottom: 100px;
  }
}
@media (max-width: 991px) {
  .mb-120 {
    margin-bottom: 90px;
  }
}
@media (max-width: 767px) {
  .mb-120 {
    margin-bottom: 70px;
  }
}

.mb-100 {
  margin-bottom: 100px;
}
@media (max-width: 991px) {
  .mb-100 {
    margin-bottom: 80px;
  }
}

.mt-100 {
  margin-top: 100px !important;
}
@media (max-width: 991px) {
  .mt-100 {
    margin-top: 80px !important;
  }
}

.mb-90 {
  margin-bottom: 90px;
}
@media (max-width: 991px) {
  .mb-90 {
    margin-bottom: 70px;
  }
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-10 {
  margin-bottom: 10px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-110 {
  padding-top: 110px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pe-80 {
  padding-right: 80px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pl-110 {
  padding-left: 110px;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .pl-110 {
    padding-left: 70px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .pl-110 {
    padding-left: 40px;
  }
}
@media (max-width: 1199px) {
  .pl-110 {
    padding-left: unset;
  }
}

.mb-60 {
  margin-bottom: 60px;
}
@media (max-width: 991px) {
  .mb-60 {
    margin-bottom: 50px;
  }
}
@media (max-width: 767px) {
  .mb-60 {
    margin-bottom: 40px;
  }
}

.mb-70 {
  margin-bottom: 70px;
}
@media (max-width: 991px) {
  .mb-70 {
    margin-bottom: 50px;
  }
}
@media (max-width: 767px) {
  .mb-70 {
    margin-bottom: 40px;
  }
}

.mb-80 {
  margin-bottom: 80px;
}
@media (max-width: 991px) {
  .mb-80 {
    margin-bottom: 70px;
  }
}
@media (max-width: 767px) {
  .mb-80 {
    margin-bottom: 60px;
  }
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-44 {
  margin-bottom: 44px;
}
@media (max-width: 991px) {
  .mb-44 {
    margin-bottom: 0px;
  }
}

.mb-35 {
  margin-bottom: 35px;
}
@media (max-width: 767px) {
  .mb-35 {
    margin-bottom: 30px;
  }
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-35 {
  margin-top: 35px;
}

.mb-40 {
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .mb-40 {
    margin-bottom: 30px;
  }
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mt-60 {
  margin-top: 60px;
}
@media (max-width: 767px) {
  .mt-60 {
    margin-top: 40px;
  }
}

.mt-70 {
  margin-top: 70px;
}
@media (max-width: 767px) {
  .mt-70 {
    margin-top: 40px;
  }
}

body {
  background: #fbfbfb;
}

.primary-btn-1 {
  font-family: var(--font-inter);
  display: inline-flex;
  align-items: center;
  border-radius: 5px;
  background: transparent;
  border: 1px solid var(--primary-color2);
  position: relative;
  transition: 0.5s all ease;
  z-index: 1;
  padding: 9px 16px;
  color: var(--primary-color2);
}
.primary-btn-1 svg {
  fill: var(--primary-color2);
  margin-right: 8px;
}
.primary-btn-1::before {
  transition: 0.5s all ease;
  position: absolute;
  border-radius: 5px;
  top: 0;
  left: 50%;
  right: 50%;
  bottom: 0;
  opacity: 0;
  content: "";
  background-color: var(--primary-color2);
  z-index: -1;
}
.primary-btn-1:hover {
  color: var(--white-color);
}
.primary-btn-1:hover svg {
  fill: var(--white-color);
}
.primary-btn-1:hover:before {
  transition: 0.5s all ease;
  left: 0;
  right: 0;
  opacity: 1;
}

.primary-btn-2 {
  font-family: var(--font-inter);
  display: inline-flex;
  align-items: center;
  border-radius: 5px;
  background-color: var(--primary-color2);
  white-space: nowrap;
  position: relative;
  transition: 0.5s all ease;
  z-index: 1;
  padding: 10px 21px;
  color: var(--white-color);
}
.primary-btn-2 svg {
  fill: var(--white-color);
  margin-right: 8px;
}
.primary-btn-2::before {
  transition: 0.5s all ease;
  position: absolute;
  border-radius: 5px;
  top: 0;
  left: 50%;
  right: 50%;
  bottom: 0;
  opacity: 0;
  content: "";
  background-color: var(--title-color);
  z-index: -1;
}
.primary-btn-2:hover {
  color: var(--white-color);
}
.primary-btn-2:hover svg {
  fill: var(--white-color);
}
.primary-btn-2:hover:before {
  transition: 0.5s all ease;
  left: 0;
  right: 0;
  opacity: 1;
}

.primary-btn-3 {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 16px;
  line-height: 1;
  color: var(--primary-color2);
  white-space: nowrap;
  position: relative;
  display: inline-block;
  background: linear-gradient(to bottom, var(--primary-color2) 0%, var(--primary-color2) 98%);
  background-repeat: no-repeat;
  background-size: 100% 1px;
  background-position: left 100%;
  transition: background-size 0.75s;
  padding-bottom: 1px;
}
.primary-btn-3:hover {
  background-size: 0 1px;
  background-position: 0% 100%;
}

@keyframes fade-up {
  0% {
    opacity: 0;
    transform: translateY(10px);
    visibility: hidden;
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
    visibility: visible;
  }
}
.primary-btn-4 {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: var(--title-color2);
  transition: 0.35s;
}
.primary-btn-4 svg {
  fill: var(--title-color2);
}
.primary-btn-4:hover {
  color: var(--primary-color2);
}
.primary-btn-4:hover svg {
  fill: var(--primary-color2);
}

.section-title h3 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 34px;
  line-height: 1.2;
  color: var(--title-color2);
  margin-bottom: 0;
}
@media (max-width: 576px) {
  .section-title h3 {
    font-size: 28px;
  }
}

.slider-btn-grp {
  display: flex;
  align-items: center;
  gap: 50px;
}
.slider-btn-grp .slider-btn {
  min-width: 30px;
  max-width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  background-color: var(--white-color);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.5s;
}
.slider-btn-grp .slider-btn svg {
  fill: none;
  stroke: var(--title-color2);
  transition: 0.5s;
}
.slider-btn-grp .slider-btn:hover {
  background-color: var(--primary-color2);
  border-color: var(--primary-color2);
}
.slider-btn-grp .slider-btn:hover svg {
  stroke: var(--white-color);
}
.slider-btn-grp .slider-btn.swiper-button-disabled {
  opacity: 0.2;
}
.slider-btn-grp.two .slider-btn {
  min-width: 35px;
  max-width: 35px;
  height: 35px;
}
.slider-btn-grp.two .slider-btn svg {
  stroke: var(--primary-color2);
}
.slider-btn-grp.two .slider-btn:hover {
  background-color: var(--primary-color2);
  border-color: var(--primary-color2);
}
.slider-btn-grp.two .slider-btn:hover svg {
  stroke: var(--white-color);
}

/*================================================
03. topbar area
=================================================*/
.topbar {
  background: #fcf2b1;
  padding: 18px 12%;
}
@media (max-width: 1699px) {
  .topbar {
    padding: 18px 5%;
  }
}
@media (max-width: 1399px) {
  .topbar {
    padding: 15px 4%;
  }
}
@media (max-width: 1199px) {
  .topbar {
    padding: 15px 3%;
  }
}
@media (max-width: 767px) {
  .topbar {
    display: none;
  }
}
.topbar .topbar-left p {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  color: #411c06;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 9px;
}
.topbar .topbar-right {
  display: flex;
  align-items: center;
  gap: 37px;
}
.topbar .topbar-right .language-area {
  position: relative;
  cursor: pointer;
}
.topbar .topbar-right .language-area a {
  font-family: var(--font-inter);
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  text-align: left;
  color: #411c06;
}
.topbar .topbar-right .language-area .lang-bar {
  position: relative;
}
.topbar .topbar-right .language-area .lang-bar .lang-btn {
  font-family: var(--font-inter);
  font-size: 14px;
  font-weight: 500;
  color: #411c06;
  display: flex;
  align-items: center;
  gap: 8px;
}
.topbar .topbar-right .language-area .lang-bar .lang-card {
  margin: 0;
  padding: 0;
  list-style: none;
  position: absolute;
  top: 40px;
  right: -2px;
  z-index: 999;
  width: 180px;
  background-color: var(--primary-color2);
  box-shadow: 0px 30px 80px rgba(8, 0, 42, 0.08);
  display: none;
  transform: scaleY(0);
  transform-origin: top;
  cursor: pointer;
}
.topbar .topbar-right .language-area .lang-bar .lang-card.active {
  display: block;
  transform: scaleY(1);
  animation: fade-up 0.5s linear;
}
.topbar .topbar-right .language-area .lang-bar .lang-card li a {
  font-family: var(--font-inter);
  color: var(--white-color);
  font-weight: 500;
  text-transform: capitalize;
  font-size: 14px;
  padding: 10px 20px;
  width: 100%;
  display: flex;
  align-items: center;
  transition: 0.5s;
}
.topbar .topbar-right .language-area .lang-bar .lang-card li a:hover {
  background-color: var(--primary-color3);
  color: var(--white-color);
}
.topbar .topbar-right .social-area {
  position: relative;
}
.topbar .topbar-right .social-area::after {
  content: "";
  position: absolute;
  top: 53%;
  transform: translateY(-50%);
  left: -18px;
  width: 1px;
  height: 14px;
  background-color: #a6a6a6;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .topbar .topbar-right .social-area::after {
    left: -19px;
  }
}
@media (max-width: 767px) {
  .topbar .topbar-right .social-area::after {
    left: -22px;
  }
}
@media (max-width: 576px) {
  .topbar .topbar-right .social-area::after {
    display: none;
  }
}
.topbar .topbar-right .social-area:first-child::after {
  display: none;
}
.topbar .topbar-right .social-area ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}
.topbar .topbar-right .social-area ul li a {
  height: 21px;
  width: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--white-color);
  border-radius: 50%;
  transition: 0.35s;
}
.topbar .topbar-right .social-area ul li a i {
  color: var(--primary-color2);
  transition: 0.35s;
  position: relative;
  left: 0;
  top: 0;
  margin-left: 0;
  font-size: 14px;
}
.topbar .topbar-right .social-area ul li a i.bi-twitter-x {
  font-size: 11px;
}
.topbar .topbar-right .social-area ul li a:hover {
  background-color: var(--primary-color2);
}
.topbar .topbar-right .social-area ul li a:hover i {
  color: var(--white-color);
}

/*================================================
04. header area
=================================================*/
header.header-area {
  position: relative;
  width: 100%;
  z-index: 9;
  display: flex;
  align-items: center;
  background-color: var(--white-color);
}
@media (max-width: 991px) {
  header.header-area {
    padding: 8px 0;
  }
}
@media (max-width: 767px) {
  header.header-area {
    padding: 0;
  }
}
header.header-area.sticky {
  position: fixed;
  top: 0px;
  left: 0;
  z-index: 99999;
  background-color: var(--white-color);
  border-bottom: 1px solid #f1f1f1;
}
@keyframes smooth-header {
  0% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0px);
  }
}
header.header-area .menu-close-btn {
  height: 32px;
  width: 32px;
  border-radius: 10px;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: 0.35s;
}
header.header-area .menu-close-btn i {
  font-size: 16px;
  line-height: 1;
  color: var(--white-color);
  transition: 0.35s;
}
header.header-area .menu-close-btn:hover {
  background-color: var(--white-color);
}
header.header-area .menu-close-btn:hover i {
  color: var(--title-color);
}
@media (max-width: 991px) {
  header.header-area .header-logo {
    padding: 20px 0;
  }
}
header.header-area .header-logo a img {
  width: 160px;
}
@media (max-width: 1399px) {
  header.header-area .header-logo a img {
    width: 140px;
  }
}
header.header-area .mobile-logo-wrap img {
  max-width: 120px;
}
header.header-area .menu-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 12%;
  width: 100%;
}
@media (max-width: 1699px) {
  header.header-area .menu-area {
    padding: 0px 5%;
  }
}
@media (max-width: 1399px) {
  header.header-area .menu-area {
    padding: 0px 4%;
  }
}
@media (max-width: 1199px) {
  header.header-area .menu-area {
    padding: 0px 3%;
  }
}
@media (max-width: 576px) {
  header.header-area .menu-area {
    padding: 0 10px;
  }
}
header.header-area .menu-area .nav-right {
  gap: 24px;
}
@media (max-width: 1499px) {
  header.header-area .menu-area .nav-right {
    gap: 15px;
  }
}
@media (max-width: 991px) {
  header.header-area .menu-area .nav-right {
    justify-content: end !important;
  }
}
@media (max-width: 1199px) {
  header.header-area .menu-area .nav-right .lg-btn {
    padding: 9px 20px;
  }
}
header.header-area .menu-area .nav-right ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 25px;
}
@media (max-width: 1399px) {
  header.header-area .menu-area .nav-right ul {
    gap: 15px;
  }
}
header.header-area .menu-area .nav-right .mobile-menu-btn {
  display: none;
  visibility: hidden;
}
header.header-area .menu-area .nav-right .mobile-menu-btn svg {
  fill: var(--title-color);
}
header.header-area .menu-area .nav-right .mobile-menu-btn:hover svg {
  animation: qode-draw 0.75s cubic-bezier(0.57, 0.39, 0, 0.86) 1 forwards;
}
@media (max-width: 991px) {
  header.header-area .menu-area .nav-right .mobile-menu-btn {
    display: block;
    visibility: visible;
  }
}
@keyframes qode-draw {
  0%, 100% {
    -webkit-clip-path: inset(-2px -2px);
    clip-path: inset(-2px -2px);
  }
  42% {
    -webkit-clip-path: inset(-2px -2px -2px 100%);
    clip-path: inset(-2px -2px -2px 100%);
  }
  43% {
    -webkit-clip-path: inset(-2px 100% -3px -2px);
    clip-path: inset(-2px 100% -3px -2px);
  }
}
header.header-area .menu-area .nav-right .notifacion-card {
  top: 30px !important;
}
header.header-area .main-menu {
  display: inline-block;
}
header.header-area .main-menu .mobile-menu-logo {
  display: none;
}
header.header-area .main-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
header.header-area .main-menu ul > li {
  display: inline-block;
  position: relative;
  padding: 0 15px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  header.header-area .main-menu ul > li {
    padding: 0px 4px;
  }
}
header.header-area .main-menu ul > li:hover i {
  color: var(--title-color);
  font-size: 20px;
}
header.header-area .main-menu ul > li a {
  font-size: 16px;
  color: var(--primary-color3);
  font-weight: 500;
  display: block;
  text-transform: capitalize;
  padding: 31px 8px;
  position: relative;
  font-family: var(--font-inter);
  transition: all 0.5s ease-out 0s;
  position: relative;
}
@media (max-width: 1499px) {
  header.header-area .main-menu ul > li a {
    padding: 31px 7px;
  }
}
@media (max-width: 1399px) {
  header.header-area .main-menu ul > li a {
    padding: 28px 5px;
  }
}
@media (max-width: 1199px) {
  header.header-area .main-menu ul > li a {
    padding: 25px 5px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  header.header-area .main-menu ul > li a {
    font-size: 15px;
  }
}
@media (max-width: 991px) {
  header.header-area .main-menu ul > li a {
    padding: 25px 10px;
  }
}
header.header-area .main-menu ul > li i {
  font-size: 20px;
  text-align: center;
  color: var(--title-color);
  font-style: normal;
  position: absolute;
  right: -5px;
  top: 35px;
  z-index: 999;
  cursor: pointer;
  display: none;
  transition: all 0.5s ease-out 0s;
  opacity: 0;
}
header.header-area .main-menu ul > li i.active {
  color: var(--title-color2);
}
header.header-area .main-menu ul > li i.active::before {
  content: "\f2ea";
}
@media (max-width: 991px) {
  header.header-area .main-menu ul > li i {
    opacity: 1;
  }
}
header.header-area .main-menu ul > li:hover > a {
  color: var(--primary-color2);
}
header.header-area .main-menu ul > li.active > a {
  color: var(--primary-color2);
}
header.header-area .main-menu ul li.menu-item-has-children > i {
  display: block;
}
@media only screen and (max-width: 991px) {
  header.header-area .main-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    padding: 30px 20px !important;
    z-index: 99999;
    height: 100%;
    overflow: auto;
    background: var(--primary-color1);
    transform: translateX(-100%);
    transition: transform 0.3s ease-in;
    box-shadow: 0px 2px 20px rgba(0, 0, 0, 0.03);
  }
  header.header-area .main-menu.show-menu {
    transform: translateX(0);
  }
  header.header-area .main-menu .mobile-menu-logo {
    text-align: left;
    padding-top: 20px;
    display: block;
    padding-bottom: 8px;
  }
  header.header-area .main-menu ul {
    float: none;
    text-align: left;
    padding: 50px 10px 35px 0;
  }
  header.header-area .main-menu ul li {
    display: block;
    position: relative;
    padding: 0 5px;
  }
  header.header-area .main-menu ul li i {
    display: block;
  }
  header.header-area .main-menu ul li a {
    padding: 10px 0;
    display: block;
    font-weight: 500;
    font-size: 16px;
    color: var(--white-color);
  }
  header.header-area .main-menu ul li .bi {
    top: 8px;
    font-size: 20px;
  }
  header.header-area .mobile-menu {
    position: relative;
    top: 2px;
    padding: 0 5px;
    border-radius: 50%;
    display: inline-block;
  }
  header.header-area .social-area ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
  }
  header.header-area .social-area ul li a {
    height: 30px;
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color2);
    border-radius: 50%;
    transition: 0.35s;
  }
  header.header-area .social-area ul li a i {
    color: var(--white-color);
    transition: 0.35s;
    position: relative;
    left: 0;
    top: 0;
    font-size: 15px;
    margin-left: 0;
  }
  header.header-area .social-area ul li:hover i {
    color: var(--primary-color3);
  }
  header.header-area .primary-btn-1 {
    justify-content: center;
    width: 100%;
  }
  header.header-area .primary-btn-2 {
    justify-content: center;
    width: 100%;
  }
}

/*================================================
05. banner wrapper
=================================================*/
.banner-wrapper {
  background: url(../image/banner-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  overflow: hidden;
  position: relative;
}
@media (max-width: 1199px) {
  .banner-wrapper {
    padding: 100px 0;
  }
}
@media (max-width: 991px) {
  .banner-wrapper {
    padding: 90px 0;
  }
}
@media (max-width: 576px) {
  .banner-wrapper {
    padding: 70px 0;
  }
}
.banner-wrapper .banner-content {
  max-width: 672px;
  width: 100%;
  padding: 120px 0;
}
@media (max-width: 1199px) {
  .banner-wrapper .banner-content {
    max-width: 780px;
    padding: 0;
  }
}
.banner-wrapper .banner-content .banner-tag {
  font-family: var(--font-mulish);
  font-weight: 800;
  font-size: 14px;
  line-height: 1;
  color: var(--white-color);
  background: #bf3030;
  padding: 5px 10px;
  border-radius: 14px;
  display: inline-block;
  margin-bottom: 10px;
}
.banner-wrapper .banner-content h1 {
  font-family: var(--font-mulish);
  font-weight: 300;
  font-style: italic;
  font-size: 68px;
  line-height: 1.2;
  color: var(--white-color);
  margin-bottom: 20px;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .banner-wrapper .banner-content h1 {
    font-size: 65px;
  }
}
@media (max-width: 1399px) {
  .banner-wrapper .banner-content h1 {
    font-size: 58px;
  }
}
@media (max-width: 991px) {
  .banner-wrapper .banner-content h1 {
    font-size: 54px;
  }
}
@media (max-width: 767px) {
  .banner-wrapper .banner-content h1 {
    font-size: 50px;
  }
}
@media (max-width: 576px) {
  .banner-wrapper .banner-content h1 {
    font-size: 43px;
    margin-bottom: 15px;
  }
}
.banner-wrapper .banner-content h1 strong {
  font-weight: 700;
  font-style: normal;
}
.banner-wrapper .banner-content h1 strong span {
  position: relative;
  display: inline-block;
}
@media (max-width: 767px) {
  .banner-wrapper .banner-content h1 strong span {
    display: inline;
  }
}
.banner-wrapper .banner-content h1 strong span::before {
  content: "";
  position: absolute;
  bottom: -4px;
  right: 0;
  width: 100%;
  background: url(../image/vector/vector.png) no-repeat center center;
  height: 21px;
}
@media (max-width: 767px) {
  .banner-wrapper .banner-content h1 strong span::before {
    display: none;
  }
}
.banner-wrapper .banner-content p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 18px;
  line-height: 1.5;
  color: var(--white-color);
  margin-bottom: 15px;
}
@media (max-width: 991px) {
  .banner-wrapper .banner-content p {
    font-size: 17px;
  }
}
@media (max-width: 767px) {
  .banner-wrapper .banner-content p {
    font-size: 16px;
  }
}
@media (max-width: 576px) {
  .banner-wrapper .banner-content p {
    font-size: 15px;
    line-height: 1.7;
  }
}
.banner-wrapper .banner-content .job-search-area {
  background: #ffffff;
  border-radius: 10px;
  max-width: 880px;
  margin: 60px 0 20px;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .banner-wrapper .banner-content .job-search-area {
    margin: 50px 0 20px;
  }
}
@media (max-width: 1399px) {
  .banner-wrapper .banner-content .job-search-area {
    margin: 50px 0 20px;
  }
}
@media (max-width: 991px) {
  .banner-wrapper .banner-content .job-search-area {
    margin: 35px 0 20px;
  }
}
@media (max-width: 767px) {
  .banner-wrapper .banner-content .job-search-area {
    margin: 25px 0 10px;
  }
}
.banner-wrapper .banner-content .job-search-area form {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 26px 15px 20px;
  gap: 20px;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .banner-wrapper .banner-content .job-search-area form {
    padding: 15px 10px;
    gap: 10px;
  }
}
@media (max-width: 991px) {
  .banner-wrapper .banner-content .job-search-area form {
    padding: 10px 26px 10px 20px;
  }
}
@media (max-width: 767px) {
  .banner-wrapper .banner-content .job-search-area form {
    flex-wrap: wrap;
    gap: 15px;
  }
}
@media (max-width: 576px) {
  .banner-wrapper .banner-content .job-search-area form {
    padding: 15px 20px;
  }
}
@media (max-width: 576px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner {
    width: 100%;
  }
}
.banner-wrapper .banner-content .job-search-area form .form-inner.category {
  border-radius: 5px;
  padding-left: 60px;
  position: relative;
  width: 100%;
  max-width: 210px;
}
@media (max-width: 1399px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner.category {
    padding-left: 20px;
    max-width: 150px;
  }
}
@media (max-width: 991px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner.category {
    padding-left: 40px;
    max-width: 180px;
  }
}
@media (max-width: 767px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner.category {
    max-width: 200px;
    padding-left: 30px;
  }
}
@media (max-width: 576px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner.category {
    padding-left: 0;
    max-width: unset;
  }
}
.banner-wrapper .banner-content .job-search-area form .form-inner.category::before {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  width: 1px;
  height: 36px;
  background-color: #D9D9D9;
}
@media (max-width: 576px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner.category::before {
    display: none;
  }
}
.banner-wrapper .banner-content .job-search-area form .form-inner input {
  height: 30px;
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 15px;
  line-height: 1;
  color: var(--title-color);
  padding: 20px 20px 20px 0px;
  line-height: 1;
  width: 100%;
  border-bottom: none;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner input {
    padding: 20px 0;
  }
}
@media (max-width: 576px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner input {
    padding: 15px 0;
    height: 27px;
  }
}
.banner-wrapper .banner-content .job-search-area form .form-inner input::-moz-placeholder {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  color: #a6a6a6;
}
.banner-wrapper .banner-content .job-search-area form .form-inner input::placeholder {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  color: #a6a6a6;
}
.banner-wrapper .banner-content .job-search-area form .form-inner .nice-select {
  height: 30px;
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  line-height: 18px;
  color: var(--title-color);
  width: 100%;
  display: flex;
  align-items: center;
  border: unset;
  padding-left: 0;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner .nice-select {
    font-size: 15px;
  }
}
.banner-wrapper .banner-content .job-search-area form .form-inner .nice-select .option {
  min-height: 35px;
}
.banner-wrapper .banner-content .job-search-area form .form-inner .nice-select .list {
  width: 100%;
}
.banner-wrapper .banner-content .job-search-area form .form-inner .nice-select .list li {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: var(--title-color);
  display: flex;
  align-items: center;
}
.banner-wrapper .banner-content .job-search-area form .form-inner .nice-select::after {
  border-bottom: 1px solid var(--primary-color3);
  border-right: 1px solid var(--primary-color3);
  margin-top: -6px;
  right: 5px;
  top: 50%;
  height: 8px;
  width: 8px;
}
.banner-wrapper .banner-content .job-search-area form .form-inner .nice-select .option:hover,
.banner-wrapper .banner-content .job-search-area form .form-inner .nice-select .option.focus,
.banner-wrapper .banner-content .job-search-area form .form-inner .nice-select .option.selected.focus {
  background-color: rgba(0, 167, 172, 0.1);
}
.banner-wrapper .banner-content .job-search-area form .form-inner .primry-btn-2 {
  height: 100%;
  padding: 12px 31px;
}
.banner-wrapper .banner-content .job-search-area form .form-inner .primry-btn-2 img {
  margin-right: 5px;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .banner-wrapper .banner-content .job-search-area form .form-inner button {
    padding: 14px 33px;
  }
}
.banner-wrapper .banner-content .suggest-tag ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 20px;
}
@media (max-width: 767px) {
  .banner-wrapper .banner-content .suggest-tag ul {
    display: inline-flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}
.banner-wrapper .banner-content .suggest-tag ul li a {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  color: var(--white-color);
  transition: 0.35s;
}
.banner-wrapper .banner-content .suggest-tag ul li a:hover {
  color: #fcf2b1;
}
.banner-wrapper .banner-img-wrap {
  min-width: 610px;
  position: relative;
}
@media (max-width: 1699px) {
  .banner-wrapper .banner-img-wrap {
    min-width: 555px;
  }
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .banner-wrapper .banner-img-wrap {
    min-width: unset;
  }
}
@media (max-width: 1399px) {
  .banner-wrapper .banner-img-wrap {
    min-width: unset;
  }
}
.banner-wrapper .banner-img-wrap .banner-img {
  display: flex;
  align-items: end;
  justify-content: end;
}
@media (max-width: 1199px) {
  .banner-wrapper .banner-img-wrap .banner-img {
    display: none;
  }
}
.banner-wrapper .banner-img-wrap .banner-img img {
  animation: fadeInRight 1s ease-out forwards;
  opacity: 0;
  animation-delay: 0.5s;
}
@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.banner-wrapper .banner-img-wrap .counter-item {
  padding: 0;
  margin: 0;
  list-style: none;
}
@media (max-width: 1199px) {
  .banner-wrapper .banner-img-wrap .counter-item {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 30px;
  }
}
@media (max-width: 767px) {
  .banner-wrapper .banner-img-wrap .counter-item {
    gap: 15px;
  }
}
@media (max-width: 576px) {
  .banner-wrapper .banner-img-wrap .counter-item {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    margin-top: 10px;
  }
}
.banner-wrapper .banner-img-wrap .counter-item .single-counter {
  padding: 13px 25px;
  background-color: #FFFFFF;
  border-radius: 10px;
  text-align: center;
  position: absolute;
  top: 55px;
  left: 30%;
}
.banner-wrapper .banner-img-wrap .counter-item .single-counter span {
  color: var(--paragraph-color);
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 14px;
  line-height: 1;
  padding: 10px 0;
  display: block;
}
.banner-wrapper .banner-img-wrap .counter-item .single-counter h6 {
  color: var(--title-color);
  font-family: var(--font-inter);
  font-weight: 700;
  font-size: 16px;
  line-height: 1;
  margin-bottom: 0;
}
.banner-wrapper .banner-img-wrap .counter-item .single-counter h6 strong {
  font-weight: 700;
}
.banner-wrapper .banner-img-wrap .counter-item .single-counter:nth-child(2) {
  top: 20%;
  left: 5px;
}
@media (max-width: 1199px) {
  .banner-wrapper .banner-img-wrap .counter-item .single-counter:nth-child(2) {
    top: unset;
    left: unset;
  }
}
.banner-wrapper .banner-img-wrap .counter-item .single-counter:nth-child(3) {
  top: unset;
  bottom: 13%;
  left: 5px;
}
@media (max-width: 1199px) {
  .banner-wrapper .banner-img-wrap .counter-item .single-counter:nth-child(3) {
    bottom: unset;
    left: unset;
  }
}
.banner-wrapper .banner-img-wrap .counter-item .single-counter:nth-child(4) {
  top: unset;
  bottom: 35px;
  left: 45%;
}
@media (max-width: 1199px) {
  .banner-wrapper .banner-img-wrap .counter-item .single-counter:nth-child(4) {
    bottom: unset;
    left: unset;
  }
}
@media (max-width: 1199px) {
  .banner-wrapper .banner-img-wrap .counter-item .single-counter {
    position: relative;
    top: unset;
    left: unset;
  }
}
@media (max-width: 576px) {
  .banner-wrapper .banner-img-wrap .counter-item .single-counter {
    width: 100%;
  }
}
.banner-wrapper .vector {
  position: absolute;
  bottom: 0;
  right: 0;
  height: 504px;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (max-width: 1699px) {
  .banner-wrapper .vector {
    width: 230px;
  }
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .banner-wrapper .vector {
    width: 180px;
    height: 385px;
  }
}
@media (max-width: 1399px) {
  .banner-wrapper .vector {
    width: 180px;
    height: 385px;
  }
}
@media (max-width: 1199px) {
  .banner-wrapper .vector {
    display: none;
  }
}

/*================================================
06. category section
=================================================*/
.category-section .single-category {
  position: relative;
}
.category-section .single-category .category-wrap {
  -webkit-mask-image: url(../image/category-bg.png);
          mask-image: url(../image/category-bg.png);
  -webkit-mask-size: cover;
          mask-size: cover;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  height: 100%;
  width: 100%;
  -webkit-mask-position: right;
          mask-position: right;
  border-radius: 20px;
  position: relative;
  z-index: 1;
  padding: 27px 10px;
  border-radius: 10px;
  background: #EEF8F9;
  transition: 0.35s;
}
.category-section .single-category .category-wrap .category-icon {
  margin-bottom: 18px;
}
.category-section .single-category .category-wrap .category-content h5 a {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 20px;
  line-height: 1;
  text-align: center;
  color: var(--title-color2);
  margin-bottom: 13px;
  transition: 0.5s;
}
@media (max-width: 1399px) {
  .category-section .single-category .category-wrap .category-content h5 a {
    font-size: 18px;
  }
}
.category-section .single-category .category-wrap .category-content h5 a:hover {
  color: var(--primary-color2);
}
.category-section .single-category .category-wrap .category-content p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 14px;
  line-height: 1;
  color: var(--paragraph-color);
  margin-bottom: 0;
}
.category-section .single-category .category-wrap .category-content p span {
  font-weight: 600;
  color: var(--title-color2);
}
.category-section .single-category .button-area .icon {
  position: absolute;
  top: 80%;
  right: 2px;
  z-index: 999;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 1px solid var(--primary-color2);
  transition: 0.5s;
}
.category-section .single-category .button-area .icon svg {
  fill: none;
  stroke: var(--primary-color2);
  transition: 0.5s;
}
.category-section .single-category:hover .category-wrap {
  background: #e7f8f2;
}
.category-section .single-category:hover .button-area .icon {
  background: var(--primary-color2);
  border: 1px solid var(--primary-color2);
}
.category-section .single-category:hover .button-area .icon svg {
  stroke: var(--white-color);
}

/*================================================
07.  trusted company
=================================================*/
.trusted-company .section-title {
  margin-bottom: 20px;
}
.trusted-company .section-title h6 {
  font-family: var(--font-mulish);
  font-weight: 600;
  font-size: 18px;
  line-height: 1;
  color: var(--title-color2);
  margin-bottom: 0;
  position: relative;
  display: inline-block;
}
.trusted-company .section-title h6::after {
  content: "";
  width: 114px;
  height: 1px;
  background-color: rgba(var(--primary-color2-opc), 0.5);
  border-radius: 5px;
  position: absolute;
  top: 55%;
  right: -128px;
  transform: translateY(-50%);
}
@media (max-width: 576px) {
  .trusted-company .section-title h6::after {
    width: 60px;
    right: -70px;
  }
}
.trusted-company .marquee {
  display: flex;
  gap: 80px;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
@media (max-width: 1199px) {
  .trusted-company .marquee {
    gap: 40px;
  }
}
@media (max-width: 576px) {
  .trusted-company .marquee {
    gap: 30px;
  }
}
.trusted-company .marquee .marquee__group {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-around;
  gap: 80px;
  min-width: 100%;
  animation: scroll-x 30s linear infinite;
}
@media (max-width: 1199px) {
  .trusted-company .marquee .marquee__group {
    gap: 40px;
  }
}
@media (max-width: 576px) {
  .trusted-company .marquee .marquee__group {
    gap: 30px;
  }
}
.trusted-company .marquee .marquee__group a img {
  width: 160px;
}
@media (max-width: 1199px) {
  .trusted-company .marquee .marquee__group a img {
    width: 145px;
  }
}
@keyframes scroll-x {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

/*================================================
08. Rescuiters section
=================================================*/
.rescuiters-section {
  padding: 0 30px;
}
@media (max-width: 767px) {
  .rescuiters-section {
    padding: 0px 20px;
  }
}
@media (max-width: 576px) {
  .rescuiters-section {
    padding: 0px 10px;
  }
}
.rescuiters-section .rescuiters-section-wrap {
  background: #f2f2f2;
  padding: 75px 20px;
  border-radius: 30px;
}
@media (max-width: 991px) {
  .rescuiters-section .rescuiters-section-wrap {
    padding: 60px 10px;
  }
}
@media (max-width: 767px) {
  .rescuiters-section .rescuiters-section-wrap {
    padding: 50px 10px;
  }
}
@media (max-width: 576px) {
  .rescuiters-section .rescuiters-section-wrap {
    padding: 50px 5px;
    border-radius: 20px;
  }
}

.rescuiters-card {
  background: var(--white-color);
  border-radius: 20px;
  padding: 0 25px;
  transition: 0.5s;
  border: 1px solid var(--border-color);
}
.rescuiters-card:hover {
  border: 1px solid var(--primary-color2);
}
@media (max-width: 1399px) {
  .rescuiters-card {
    padding: 0 20px;
  }
}
@media (max-width: 991px) {
  .rescuiters-card {
    padding: 0 15px;
  }
}
@media (max-width: 576px) {
  .rescuiters-card {
    border-radius: 15px;
  }
}
.rescuiters-card .company-area {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 25px 0px;
  margin-bottom: 25px;
  border-bottom: 1px solid var(--border-color);
}
@media (max-width: 991px) {
  .rescuiters-card .company-area {
    gap: 10px;
  }
}
@media (max-width: 576px) {
  .rescuiters-card .company-area .icon img {
    width: 60px;
  }
}
.rescuiters-card .company-area .name-location h5 {
  margin-bottom: 10px;
}
.rescuiters-card .company-area .name-location h5 a {
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 20px;
  line-height: 1;
  color: var(--primary-color2);
  transition: 0.5s;
}
.rescuiters-card .company-area .name-location h5 a:hover {
  color: var(--title-color);
}
@media (max-width: 991px) {
  .rescuiters-card .company-area .name-location h5 a {
    font-size: 19px;
  }
}
@media (max-width: 576px) {
  .rescuiters-card .company-area .name-location h5 a {
    font-size: 18px;
  }
}
.rescuiters-card .company-area .name-location .info {
  padding: 0;
  margin: 0;
  list-style: none;
}
.rescuiters-card .company-area .name-location .info li {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 15px;
  line-height: 24px;
  color: var(--paragraph-color);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}
.rescuiters-card .company-area .name-location .info li svg {
  fill: var(--paragraph-color);
}
.rescuiters-card .company-area .name-location .info li:last-child {
  margin-bottom: 0;
}
@media (max-width: 576px) {
  .rescuiters-card .company-area .name-location .info li {
    font-size: 14px;
    gap: 5px;
  }
}
.rescuiters-card .job-details-vacancies {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  padding: 0px 0px 30px;
}
.rescuiters-card .job-details-vacancies .vacancies p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: var(--paragraph-color);
  margin-bottom: 0;
}
.rescuiters-card .job-details-vacancies .vacancies p span {
  font-weight: 500;
  color: var(--title-color2);
}

/*================================================
09. feature section 
=================================================*/
.feature-card {
  position: relative;
}
.feature-card .feature-wrape {
  -webkit-mask-image: url(../image/featured-bg.png);
          mask-image: url(../image/featured-bg.png);
  -webkit-mask-size: cover;
          mask-size: cover;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  height: 100%;
  width: 100%;
  -webkit-mask-position: right;
          mask-position: right;
  border-radius: 20px;
  position: relative;
  z-index: 1;
  padding: 30px 30px;
  border-radius: 20px;
  background: var(--white-color);
  border: 1px solid transparent;
  transition: 0.35s;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .feature-card .feature-wrape {
    padding: 25px 20px;
  }
}
@media (max-width: 1199px) {
  .feature-card .feature-wrape {
    -webkit-mask-position: right bottom;
            mask-position: right bottom;
  }
}
@media (max-width: 991px) {
  .feature-card .feature-wrape {
    padding: 25px 18px;
  }
}
.feature-card .feature-wrape .company-area {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}
@media (max-width: 1399px) {
  .feature-card .feature-wrape .company-area {
    margin-bottom: 25px;
  }
}
@media (max-width: 991px) {
  .feature-card .feature-wrape .company-area {
    gap: 10px;
  }
}
.feature-card .feature-wrape .company-area .company-details {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 12px;
}
.feature-card .feature-wrape .company-area .company-details .name-location h5 {
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 20px;
  line-height: 1.2;
  color: var(--primary-color2);
  margin-bottom: 7px;
}
@media (max-width: 991px) {
  .feature-card .feature-wrape .company-area .company-details .name-location h5 {
    font-size: 18px;
  }
}
.feature-card .feature-wrape .company-area .company-details .name-location p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
  color: var(--paragraph-color);
  margin-bottom: 0;
}
@media (max-width: 991px) {
  .feature-card .feature-wrape .company-area .company-details .name-location p {
    font-size: 15px;
  }
}
@media (max-width: 576px) {
  .feature-card .feature-wrape .company-area .company-details .name-location p {
    font-size: 14px;
  }
}
.feature-card .feature-wrape .company-area .company-details .name-location p span {
  color: var(--title-color2);
  font-weight: 500;
}
.feature-card .feature-wrape .company-area .company-details .bookmark {
  cursor: pointer;
}
.feature-card .feature-wrape .company-area .company-details .bookmark i {
  color: var(--paragraph-color);
  transition: 0.5s;
}
.feature-card .feature-wrape .company-area .company-details .bookmark i:hover {
  color: #ee4f4f;
}
.feature-card .feature-wrape .company-area .company-details .bookmark i:hover::before {
  content: "\f415";
}
.feature-card .feature-wrape .job-discription ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 14px;
  margin-bottom: 35px;
}
@media (max-width: 991px) {
  .feature-card .feature-wrape .job-discription ul {
    margin-bottom: 30px;
  }
}
@media (max-width: 576px) {
  .feature-card .feature-wrape .job-discription ul {
    gap: 8px;
    row-gap: 12px;
  }
}
.feature-card .feature-wrape .job-discription ul li {
  background: #faf4db;
  padding: 5px 15px;
  border-radius: 5px;
  display: flex;
  gap: 4px;
  align-items: center;
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #8e5e0f;
  text-align: center;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .feature-card .feature-wrape .job-discription ul li {
    font-size: 13px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .feature-card .feature-wrape .job-discription ul li {
    font-size: 12px;
  }
}
@media (max-width: 576px) {
  .feature-card .feature-wrape .job-discription ul li {
    font-size: 12px;
    padding: 5px 10px;
  }
}
.feature-card .feature-wrape .job-discription ul li.style-2 {
  background: #e4f7ff;
  color: #0e658c;
}
.feature-card .feature-wrape .job-discription ul li.style-2 svg {
  fill: #0e658c;
}
.feature-card .feature-wrape .job-discription ul li.style-3 {
  background: #fff0f5;
  color: #6d1e38;
}
.feature-card .feature-wrape .job-discription ul li.style-4 {
  background: #edfedd;
  color: #243f0b;
}
.feature-card .feature-wrape .job-discription ul li.style-5 {
  background: #f3edfb;
  color: #322052;
}
.feature-card .feature-wrape .date p {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 15px;
  line-height: 1;
  color: var(--paragraph-color);
  margin-bottom: 0;
}
@media (max-width: 576px) {
  .feature-card .feature-wrape .date p {
    font-size: 13px;
  }
}
.feature-card .button-area {
  position: absolute;
  top: 84%;
  right: -5px;
  z-index: 99;
}
@media (max-width: 991px) {
  .feature-card .button-area {
    right: 0;
  }
}
@media (max-width: 991px) {
  .feature-card .button-area .primary-btn-4 {
    font-size: 15px;
  }
}
@media (max-width: 576px) {
  .feature-card .button-area .primary-btn-4 {
    font-size: 14px;
  }
}
.feature-card.two .feature-wrape {
  background-color: #F4F4F4;
}

.featured-section {
  padding: 0 30px;
}
@media (max-width: 1399px) {
  .featured-section {
    padding: 0 20px;
  }
}
@media (max-width: 767px) {
  .featured-section {
    padding: 0 10px;
  }
}
.featured-section .featured-section-wrap {
  background: #f2f2f2;
  padding: 75px 0;
  border-radius: 30px;
}
@media (max-width: 767px) {
  .featured-section .featured-section-wrap {
    padding: 60px 0;
    border-radius: 20px;
  }
}
@media (max-width: 576px) {
  .featured-section .featured-section-wrap {
    padding: 50px 0;
  }
}

/*================================================
10. feature section 
=================================================*/
.testimonial-card2 .testimonial-img {
  background-color: #f0f0f0;
  padding: 15px;
  border-radius: 20px;
  height: 100%;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-card2 .testimonial-img {
    padding: 10px;
  }
}
@media (max-width: 576px) {
  .testimonial-card2 .testimonial-img {
    padding: 10px;
    border-radius: 15px;
    height: unset;
  }
}
.testimonial-card2 .testimonial-img img {
  border-radius: 10px;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (max-width: 576px) {
  .testimonial-card2 .testimonial-img img {
    height: unset;
  }
}
.testimonial-card2 .testimonial-content-wrap {
  background-image: url(../image/testimonial-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  padding: 55px 65px;
  border-radius: 20px;
  position: relative;
}
@media (max-width: 1399px) {
  .testimonial-card2 .testimonial-content-wrap {
    padding: 55px 40px;
    background-position: right;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-card2 .testimonial-content-wrap {
    padding: 45px 25px;
  }
}
@media (max-width: 991px) {
  .testimonial-card2 .testimonial-content-wrap {
    padding: 45px 28px;
  }
}
@media (max-width: 576px) {
  .testimonial-card2 .testimonial-content-wrap {
    padding: 35px 15px;
    border-radius: 15px;
  }
}
.testimonial-card2 .testimonial-content-wrap .company-logo {
  margin-bottom: 40px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-card2 .testimonial-content-wrap .company-logo {
    margin-bottom: 30px;
  }
}
@media (max-width: 767px) {
  .testimonial-card2 .testimonial-content-wrap .company-logo {
    margin-bottom: 30px;
  }
}
.testimonial-card2 .testimonial-content-wrap .company-logo img {
  width: 144px;
}
.testimonial-card2 .testimonial-content-wrap .company-logo img.dark {
  display: none;
}
.testimonial-card2 .testimonial-content-wrap .testimonial-content {
  margin-bottom: 45px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-card2 .testimonial-content-wrap .testimonial-content {
    margin-bottom: 30px;
  }
}
@media (max-width: 767px) {
  .testimonial-card2 .testimonial-content-wrap .testimonial-content {
    margin-bottom: 30px;
  }
}
@media (max-width: 576px) {
  .testimonial-card2 .testimonial-content-wrap .testimonial-content {
    margin-bottom: 25px;
  }
}
.testimonial-card2 .testimonial-content-wrap .testimonial-content .rating-area {
  padding: 0;
  margin: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 5px;
  line-height: 1;
  margin-bottom: 15px;
}
@media (max-width: 576px) {
  .testimonial-card2 .testimonial-content-wrap .testimonial-content .rating-area {
    margin-bottom: 10px;
  }
}
.testimonial-card2 .testimonial-content-wrap .testimonial-content .rating-area li i {
  color: #e4c40c;
  font-size: 14px;
}
.testimonial-card2 .testimonial-content-wrap .testimonial-content p {
  color: var(--paragraph-color);
  font-family: var(--font-mulish);
  font-size: 28px;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 0;
}
@media (max-width: 1399px) {
  .testimonial-card2 .testimonial-content-wrap .testimonial-content p {
    font-size: 25px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-card2 .testimonial-content-wrap .testimonial-content p {
    font-size: 22px;
    line-height: 1.4;
  }
}
@media (max-width: 991px) {
  .testimonial-card2 .testimonial-content-wrap .testimonial-content p {
    font-size: 24px;
    line-height: 1.4;
  }
}
@media (max-width: 576px) {
  .testimonial-card2 .testimonial-content-wrap .testimonial-content p {
    font-size: 18px;
    line-height: 1.7;
  }
}
.testimonial-card2 .testimonial-content-wrap .testimonial-content p span {
  color: var(--title-color);
}
.testimonial-card2 .testimonial-content-wrap .author-area {
  max-width: 270px;
  width: 100%;
}
@media (max-width: 576px) {
  .testimonial-card2 .testimonial-content-wrap .author-area {
    max-width: unset;
  }
}
.testimonial-card2 .testimonial-content-wrap .author-area h5 {
  color: var(--title-color2);
  font-family: var(--font-mulish);
  font-size: 22px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 5px;
}
.testimonial-card2 .testimonial-content-wrap .author-area span {
  color: var(--paragraph-color);
  font-family: var(--font-inter);
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
}
.testimonial-card2 .testimonial-content-wrap .quote {
  fill: var(--white-color);
  stroke: var(--border-color);
  position: absolute;
  top: 60px;
  right: 110px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-card2 .testimonial-content-wrap .quote {
    top: 40px;
    right: 80px;
  }
}
@media (max-width: 991px) {
  .testimonial-card2 .testimonial-content-wrap .quote {
    top: 40px;
  }
}
@media (max-width: 767px) {
  .testimonial-card2 .testimonial-content-wrap .quote {
    right: 60px;
  }
}
@media (max-width: 576px) {
  .testimonial-card2 .testimonial-content-wrap .quote {
    top: 30px;
    right: 30px;
    width: 80px;
  }
}
.testimonial-card2 .testimonial-content-wrap .joint1 {
  fill: #f0f0f0;
  position: absolute;
  top: 80px;
  left: -24px;
}
@media (max-width: 991px) {
  .testimonial-card2 .testimonial-content-wrap .joint1 {
    display: none;
  }
}
.testimonial-card2 .testimonial-content-wrap .joint2 {
  fill: #f0f0f0;
  position: absolute;
  bottom: 80px;
  left: -24px;
}
@media (max-width: 991px) {
  .testimonial-card2 .testimonial-content-wrap .joint2 {
    display: none;
  }
}
.testimonial-card2.two .testimonial-img {
  padding: 0;
  position: relative;
  border-radius: 5px;
}
.testimonial-card2.two .testimonial-img img {
  border-radius: 5px;
}
.testimonial-card2.two .testimonial-content-wrap {
  background-image: unset;
  background-color: rgba(240, 240, 240, 0.8);
  border-radius: 10px;
  padding: 35px 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 25px;
  min-height: 396px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-card2.two .testimonial-content-wrap {
    padding: 25px 18px;
    min-height: 348px;
  }
}
@media (max-width: 991px) {
  .testimonial-card2.two .testimonial-content-wrap {
    min-height: 348px;
  }
}
@media (max-width: 767px) {
  .testimonial-card2.two .testimonial-content-wrap {
    min-height: 315px;
  }
}
@media (max-width: 576px) {
  .testimonial-card2.two .testimonial-content-wrap {
    padding: 25px 15px;
    gap: 15px;
    min-height: 306px;
  }
}
.testimonial-card2.two .testimonial-content-wrap .testimonial-content {
  margin-bottom: 0;
}
.testimonial-card2.two .testimonial-content-wrap .testimonial-content .testimonial-content-top {
  margin-bottom: 15px;
}
@media (max-width: 576px) {
  .testimonial-card2.two .testimonial-content-wrap .testimonial-content .testimonial-content-top {
    margin-bottom: 10px;
  }
}
.testimonial-card2.two .testimonial-content-wrap .testimonial-content .testimonial-content-top .rating-area {
  margin-bottom: 2px;
}
.testimonial-card2.two .testimonial-content-wrap .testimonial-content .testimonial-content-top .rating-area li i {
  color: #dda701;
}
.testimonial-card2.two .testimonial-content-wrap .testimonial-content .testimonial-content-top span {
  color: var(--title-color);
  font-family: var(--font-inter);
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
}
.testimonial-card2.two .testimonial-content-wrap .author-area h5 {
  margin-bottom: 0;
}
.testimonial-card2.two .testimonial-content-wrap .quote {
  top: unset;
  bottom: 45px;
  right: 30px;
}
@media (max-width: 576px) {
  .testimonial-card2.two .testimonial-content-wrap .quote {
    bottom: 40px;
    right: 25px;
  }
}
@media (max-width: 991px) {
  .testimonial-card2.two .testimonial-content-wrap .joint1,
  .testimonial-card2.two .testimonial-content-wrap .joint2 {
    display: block;
  }
}
@media (max-width: 767px) {
  .testimonial-card2.two .testimonial-content-wrap .joint1,
  .testimonial-card2.two .testimonial-content-wrap .joint2 {
    display: none;
  }
}

.testimonial-section .testimonial-slider-area {
  position: relative;
}
.testimonial-section .testimonial-slider-area .slider-btn-area {
  display: flex;
  align-items: center;
  gap: 30px;
  justify-content: end;
  position: absolute;
  bottom: 72px;
  right: 65px;
  z-index: 1;
}
@media (max-width: 1399px) {
  .testimonial-section .testimonial-slider-area .slider-btn-area {
    right: 40px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonial-section .testimonial-slider-area .slider-btn-area {
    right: 25px;
    bottom: 62px;
  }
}
@media (max-width: 991px) {
  .testimonial-section .testimonial-slider-area .slider-btn-area {
    right: 28px;
    bottom: 62px;
  }
}
@media (max-width: 576px) {
  .testimonial-section .testimonial-slider-area .slider-btn-area {
    position: relative;
    margin-top: 20px;
    bottom: unset;
    right: unset;
    justify-content: space-between;
  }
}
.testimonial-section .testimonial-slider-area .slider-btn-area .slider-btn-grp {
  gap: 15px;
}
.testimonial-section .testimonial-slider-area .slider-btn-area .franctional-pagi {
  color: var(--title-color2);
  font-family: var(--font-inter);
  font-size: 22px;
  font-weight: 600;
  width: unset;
}
.testimonial-section .testimonial-slider-area .slider-btn-area .franctional-pagi .swiper-pagination-total {
  color: var(--paragraph-color);
  font-size: 18px;
  font-weight: 600;
  font-family: var(--font-inter);
}
.testimonial-section .testimonial-slider .swiper-slide-active .company-logo {
  animation: fadeInDown 1.7s;
}
.testimonial-section .testimonial-slider .swiper-slide-active .testimonial-content,
.testimonial-section .testimonial-slider .swiper-slide-active .author-area {
  animation: fadeInUp 1.7s;
}

/*================================================
11. latest news
=================================================*/
.blog-section .blog-card .blog-img-wrap {
  position: relative;
}
.blog-section .blog-card .blog-img-wrap .blog-img {
  position: relative;
  display: block;
  overflow: hidden;
  border-radius: 30px;
}
@media (max-width: 991px) {
  .blog-section .blog-card .blog-img-wrap .blog-img {
    border-radius: 20px;
  }
}
@media (max-width: 576px) {
  .blog-section .blog-card .blog-img-wrap .blog-img {
    border-radius: 15px;
  }
}
.blog-section .blog-card .blog-img-wrap .blog-img img {
  border-radius: 30px;
  transition: 0.5s ease-out;
}
@media (max-width: 991px) {
  .blog-section .blog-card .blog-img-wrap .blog-img img {
    border-radius: 20px;
  }
}
@media (max-width: 576px) {
  .blog-section .blog-card .blog-img-wrap .blog-img img {
    border-radius: 15px;
  }
}
.blog-section .blog-card .blog-img-wrap .batch {
  -webkit-mask-image: url(../image/blog-mask.png);
          mask-image: url(../image/blog-mask.png);
  -webkit-mask-position: left;
          mask-position: left;
  -webkit-mask-size: contain;
          mask-size: contain;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background: #fbfbfb;
  padding: 48px 48px 18px 0px;
  position: absolute;
  bottom: 0;
  left: 0;
}
@media (min-width: 768px) and (max-width: 991px) {
  .blog-section .blog-card .blog-img-wrap .batch {
    padding: 46px 40px 16px 0px;
  }
}
@media (max-width: 576px) {
  .blog-section .blog-card .blog-img-wrap .batch {
    padding: 48px 48px 12px 0px;
  }
}
.blog-section .blog-card .blog-img-wrap .batch ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 12px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .blog-section .blog-card .blog-img-wrap .batch ul {
    gap: 8px;
  }
}
.blog-section .blog-card .blog-img-wrap .batch ul li {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 14px;
  line-height: 1;
  color: var(--paragraph-color);
  position: relative;
  padding-left: 12px;
}
.blog-section .blog-card .blog-img-wrap .batch ul li::after {
  content: "";
  position: absolute;
  top: 53%;
  transform: translateY(-50%);
  left: 0;
  width: 1px;
  height: 14px;
  background-color: #d9d9d9;
}
.blog-section .blog-card .blog-img-wrap .batch ul li a {
  color: var(--paragraph-color);
  transition: 0.5s;
}
.blog-section .blog-card .blog-img-wrap .batch ul li a:hover {
  color: var(--primary-color2);
}
@media (min-width: 768px) and (max-width: 991px) {
  .blog-section .blog-card .blog-img-wrap .batch ul li {
    padding-left: 8px;
  }
}
@media (max-width: 576px) {
  .blog-section .blog-card .blog-img-wrap .batch ul li {
    font-size: 13px;
  }
}
.blog-section .blog-card .blog-img-wrap .batch ul li:first-child {
  padding-left: 0;
}
.blog-section .blog-card .blog-img-wrap .batch ul li:first-child::after {
  display: none;
}
.blog-section .blog-card .blog-content {
  padding-top: 15px;
}
@media (max-width: 991px) {
  .blog-section .blog-card .blog-content {
    padding-top: 10px;
  }
}
.blog-section .blog-card .blog-content h5 {
  margin-bottom: 15px;
}
.blog-section .blog-card .blog-content h5 a {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 20px;
  line-height: 1.2;
  color: var(--title-color2);
  text-transform: capitalize;
  transition: 0.35s;
}
.blog-section .blog-card .blog-content h5 a:hover {
  color: var(--primary-color2);
}
.blog-section .blog-card:hover .blog-img-wrap .blog-img img {
  transform: scale(1.1);
}

/*================================================
11. Empzio work section
=================================================*/
.empzio-work-section .single-work-section {
  position: relative;
}
.empzio-work-section .single-work-section .single-work-wrap h2 {
  font-family: var(--font-inter);
  font-weight: 800;
  font-size: 60px;
  line-height: 1;
  text-align: center;
  color: #bf9264;
  margin-bottom: 10px;
}
@media (max-width: 1399px) {
  .empzio-work-section .single-work-section .single-work-wrap h2 {
    font-size: 55px;
  }
}
@media (max-width: 1199px) {
  .empzio-work-section .single-work-section .single-work-wrap h2 {
    font-size: 50px;
  }
}
@media (max-width: 767px) {
  .empzio-work-section .single-work-section .single-work-wrap h2 {
    font-size: 45px;
  }
}
@media (max-width: 576px) {
  .empzio-work-section .single-work-section .single-work-wrap h2 {
    font-size: 42px;
  }
}
.empzio-work-section .single-work-section .single-work-wrap h4 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 24px;
  line-height: 1.2;
  text-align: center;
  color: var(--title-color2);
  margin-bottom: 14px;
  text-transform: capitalize;
}
@media (max-width: 1199px) {
  .empzio-work-section .single-work-section .single-work-wrap h4 {
    font-size: 22px;
  }
}
@media (max-width: 991px) {
  .empzio-work-section .single-work-section .single-work-wrap h4 {
    margin-bottom: 10px;
  }
}
.empzio-work-section .single-work-section .single-work-wrap p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  line-height: 1.3;
  color: var(--paragraph-color);
  text-align: center;
  margin-bottom: 0;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .empzio-work-section .single-work-section .single-work-wrap p {
    font-size: 14px;
  }
}
@media (max-width: 767px) {
  .empzio-work-section .single-work-section .single-work-wrap p {
    font-size: 14px;
  }
}
@media (max-width: 576px) {
  .empzio-work-section .single-work-section .single-work-wrap p {
    font-size: 15px;
  }
}
.empzio-work-section .single-work-section .arrow {
  fill: #BF9264;
  position: absolute;
  top: 10px;
  right: -28%;
}
@media (max-width: 1399px) {
  .empzio-work-section .single-work-section .arrow {
    width: 110px;
    right: -26%;
  }
}
@media (max-width: 1199px) {
  .empzio-work-section .single-work-section .arrow {
    width: 100px;
  }
}
@media (max-width: 991px) {
  .empzio-work-section .single-work-section .arrow {
    display: none;
  }
}
.empzio-work-section .single-work-section.style-2 .single-work-wrap h2 {
  color: #6f826a;
}
.empzio-work-section .single-work-section.style-2 .arrow {
  fill: #6F826A;
  top: unset;
  bottom: -24px;
}
@media (max-width: 1199px) {
  .empzio-work-section .single-work-section.style-2 .arrow {
    bottom: -28px;
  }
}
.empzio-work-section .single-work-section.style-3 .single-work-wrap h2 {
  color: #a6ca88;
}
.empzio-work-section .single-work-section.style-3 .arrow {
  fill: #A6CA88;
}
.empzio-work-section .single-work-section.style-4 .single-work-wrap h2 {
  color: #b2b464;
}

/*================================================
12. latest news
=================================================*/
.location-section {
  background-color: #F3F0EA;
  padding: 80px 0;
  margin-left: 30px;
  margin-right: 30px;
  border-radius: 30px;
}
@media (max-width: 991px) {
  .location-section {
    padding: 60px 0;
  }
}
@media (max-width: 767px) {
  .location-section {
    margin-left: 10px;
    margin-right: 10px;
    border-radius: 20px;
    padding: 50px 0;
  }
}
.location-section .location-card {
  position: relative;
}
.location-section .location-card .location-image {
  position: relative;
  z-index: 2;
  border-radius: 15px;
}
.location-section .location-card .location-image img {
  border-radius: 15px;
}
.location-section .location-card .location-content-wrap {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  max-width: 240px;
  width: 100%;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .location-section .location-card .location-content-wrap {
    max-width: 220px;
  }
}
.location-section .location-card .location-content {
  background-color: #F3F0EA;
  padding: 15px 10px;
  border-radius: 10px;
  text-align: center;
}
.location-section .location-card .location-content h6 {
  margin-bottom: 5px;
}
.location-section .location-card .location-content h6 a {
  color: var(--title-color2);
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 18px;
  line-height: 1.5;
  transition: 0.5s;
}
.location-section .location-card .location-content h6 a:hover {
  color: var(--primary-color2);
}
.location-section .location-card .location-content span {
  color: var(--primary-color2);
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 16px;
  line-height: 1;
}
.location-section .swiper-pagination1 {
  display: inline-flex;
  align-items: center;
  width: unset;
  z-index: 9;
  gap: 58px;
  position: relative;
}
@media (max-width: 991px) {
  .location-section .swiper-pagination1 {
    gap: 40px;
  }
}
@media (max-width: 576px) {
  .location-section .swiper-pagination1 {
    gap: 20px;
  }
}
.location-section .swiper-pagination1::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 95%;
  height: 1px;
  background-color: #A6A6A6;
}
@media (max-width: 576px) {
  .location-section .swiper-pagination1 {
    justify-content: center;
  }
}
.location-section .swiper-pagination1 .swiper-pagination-bullet {
  height: 8px;
  width: 8px;
  background-color: #A6A6A6;
  border-radius: 50%;
  opacity: 1;
  position: relative;
}
.location-section .swiper-pagination1 .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 10px;
  height: 10px;
  background-color: var(--primary-color2);
}

/*================================================
13. footer section
=================================================*/
.footer-section {
  background: var(--title-color);
}
.footer-section .footer-top {
  padding: 70px 0;
}
.footer-section .footer-top .footer-widget .footer-logo {
  display: inline-block;
  margin-bottom: 23px;
}
.footer-section .footer-top .footer-widget .footer-logo img {
  width: 160px;
}
@media (max-width: 576px) {
  .footer-section .footer-top .footer-widget .footer-logo img {
    width: 140px;
  }
}
.footer-section .footer-top .footer-widget p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  line-height: 1.4;
  color: #939393;
  margin-bottom: 45px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .footer-section .footer-top .footer-widget p {
    font-size: 14px;
    line-height: 1.6;
  }
}
@media (max-width: 991px) {
  .footer-section .footer-top .footer-widget p {
    margin-bottom: 35px;
  }
}
.footer-section .footer-top .footer-widget .social-area h5 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 22px;
  line-height: 1;
  color: var(--white-color);
  margin-bottom: 25px;
}
.footer-section .footer-top .footer-widget .social-area ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 15px;
}
.footer-section .footer-top .footer-widget .social-area ul li a {
  height: 26px;
  width: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--white-color);
  border-radius: 50%;
  transition: 0.35s;
}
.footer-section .footer-top .footer-widget .social-area ul li a i {
  color: var(--paragraph-color);
  transition: 0.35s;
  position: relative;
  left: 0;
  top: 0;
  margin-left: 0;
  font-size: 14px;
}
.footer-section .footer-top .footer-widget .social-area ul li a i.bi-twitter-x {
  font-size: 12px;
}
.footer-section .footer-top .footer-widget .social-area ul li a:hover i {
  color: var(--primary-color2);
}
.footer-section .footer-top .footer-widget .footer-title h5 {
  font-family: var(--font-mulish);
  font-size: 22px;
  font-weight: 700;
  line-height: 1;
  text-align: left;
  color: var(--white-color);
  margin-bottom: 25px;
}
.footer-section .footer-top .footer-widget .widget-list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.footer-section .footer-top .footer-widget .widget-list li {
  margin-bottom: 18px;
}
.footer-section .footer-top .footer-widget .widget-list li:last-child {
  margin-bottom: 0;
}
@media (max-width: 576px) {
  .footer-section .footer-top .footer-widget .widget-list li {
    margin-bottom: 15px;
  }
}
.footer-section .footer-top .footer-widget .widget-list li a {
  font-family: var(--font-inter);
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  text-align: left;
  color: #a5a5a5;
  transition: 0.35s;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .footer-section .footer-top .footer-widget .widget-list li a {
    font-size: 14px;
  }
}
@media (max-width: 991px) {
  .footer-section .footer-top .footer-widget .widget-list li a {
    font-size: 15px;
  }
}
.footer-section .footer-top .footer-widget .widget-list li a:hover {
  color: var(--primary-color2);
}
.footer-section .footer-top .footer-widget .app-list ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.footer-section .footer-top .footer-widget .app-list ul li {
  margin-bottom: 20px;
}
.footer-section .footer-top .footer-widget .app-list ul li:last-child {
  margin-bottom: 0;
}
.footer-section .footer-bottom-wrap {
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  padding: 30px 0;
}
@media (max-width: 767px) {
  .footer-section .footer-bottom-wrap {
    padding: 25px 0;
  }
}
.footer-section .footer-bottom-wrap .footer-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  flex-wrap: wrap;
}
@media (max-width: 767px) {
  .footer-section .footer-bottom-wrap .footer-bottom {
    justify-content: center;
  }
}
.footer-section .footer-bottom-wrap .footer-bottom .copyright-area p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 15px;
  line-height: 1;
  color: #d3d3d3;
  margin-bottom: 0;
}
.footer-section .footer-bottom-wrap .footer-bottom .copyright-area p a {
  color: var(--white-color);
  font-weight: 500;
  transition: 0.5s;
}
.footer-section .footer-bottom-wrap .footer-bottom .copyright-area p a:hover {
  color: var(--primary-color2);
}
.footer-section .footer-bottom-wrap .footer-bottom .contact-area h6 {
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 15px;
  line-height: 1;
  color: var(--white-color);
  margin-bottom: 0;
}
.footer-section .footer-bottom-wrap .footer-bottom .contact-area h6 a {
  color: var(--white-color);
  transition: 0.5s;
}
.footer-section .footer-bottom-wrap .footer-bottom .contact-area h6 a:hover {
  color: var(--primary-color2);
}

/*================================================
14. breadcrcum area
=================================================*/
.breadcrumb-section {
  padding: 0 26px;
}
@media (max-width: 991px) {
  .breadcrumb-section {
    padding: 0 20px;
  }
}
@media (max-width: 576px) {
  .breadcrumb-section {
    padding: 0 10px;
  }
}
.breadcrumb-section .breadcrumb-wrap {
  background: #f1f1f1;
  border-radius: 20px;
  padding: 90px 0;
}
@media (max-width: 991px) {
  .breadcrumb-section .breadcrumb-wrap {
    padding: 60px 0;
  }
}
@media (max-width: 767px) {
  .breadcrumb-section .breadcrumb-wrap {
    padding: 60px 0;
  }
}
@media (max-width: 576px) {
  .breadcrumb-section .breadcrumb-wrap {
    border-radius: 10px;
  }
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content h1 {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 50px;
  line-height: 1.2;
  color: var(--title-color2);
  margin-bottom: 0;
}
@media (max-width: 1399px) {
  .breadcrumb-section .breadcrumb-wrap .breadcrumb-content h1 {
    font-size: 47px;
  }
}
@media (max-width: 1199px) {
  .breadcrumb-section .breadcrumb-wrap .breadcrumb-content h1 {
    font-size: 43px;
  }
}
@media (max-width: 991px) {
  .breadcrumb-section .breadcrumb-wrap .breadcrumb-content h1 {
    font-size: 38px;
  }
}
@media (max-width: 767px) {
  .breadcrumb-section .breadcrumb-wrap .breadcrumb-content h1 {
    font-size: 35px;
  }
}
@media (max-width: 576px) {
  .breadcrumb-section .breadcrumb-wrap .breadcrumb-content h1 {
    font-size: 30px;
  }
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content > ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  gap: 8px;
  margin-bottom: 25px;
}
@media (max-width: 991px) {
  .breadcrumb-section .breadcrumb-wrap .breadcrumb-content > ul {
    margin-bottom: 20px;
  }
}
@media (max-width: 576px) {
  .breadcrumb-section .breadcrumb-wrap .breadcrumb-content > ul {
    margin-bottom: 15px;
  }
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content > ul li {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  color: var(--title-color2);
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content > ul li::before {
  float: left;
  padding-right: 8px;
  content: url(../image/icon/star.svg);
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content > ul li:first-child::before {
  display: none;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content > ul li a {
  color: #595959;
  transition: 0.5s;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content > ul li a:hover {
  color: var(--primary-color2);
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-left {
  display: flex;
  gap: 18px;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-left .company-info h5 {
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 20px;
  line-height: 1.2;
  color: var(--primary-color2);
  margin-bottom: 18px;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-left .company-info p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  line-height: 1.3;
  color: var(--paragraph-color);
  margin-bottom: 0;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-left .company-info p span {
  font-weight: 500;
  color: var(--title-color2);
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-left .company-info .location {
  margin: 0;
  padding: 0;
  list-style: none;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-left .company-info .location li {
  margin-bottom: 15px;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-left .company-info .location li:last-child {
  margin-bottom: 0;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-left .company-info .location li a {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 15px;
  line-height: 1;
  color: var(--paragraph-color);
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-left .company-info .location li a svg {
  fill: var(--paragraph-color);
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .social-area {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 25px;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .social-area h6 {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: var(--title-color2);
  margin-bottom: 0;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .social-area ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .social-area ul li a {
  height: 20px;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--white-color);
  border-radius: 50%;
  transition: 0.35s;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .social-area ul li a i {
  color: var(--primary-color2);
  transition: 0.35s;
  position: relative;
  left: 0;
  top: 0;
  margin-left: 0;
  font-size: 14px;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .social-area ul li a i.bi-twitter-x {
  font-size: 11px;
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .social-area ul li a:hover {
  background-color: var(--primary-color2);
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .social-area ul li a:hover i {
  color: var(--white-color);
}
.breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .button {
  display: flex;
  justify-content: end;
}
@media (max-width: 991px) {
  .breadcrumb-section .breadcrumb-wrap .breadcrumb-content-right .button {
    justify-content: start;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap {
  background: #e8f2d6;
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content {
  display: grid;
  grid-template-columns: 38% 35% 20%;
  gap: 60px;
  justify-content: space-between;
}
@media (max-width: 1399px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content {
    gap: 20px;
    grid-template-columns: 35% 39% 20%;
  }
}
@media (max-width: 1199px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content {
    grid-template-columns: 39% 32% 23%;
  }
}
@media (max-width: 991px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content {
    grid-template-columns: repeat(2, 1fr);
    row-gap: 30px;
  }
}
@media (max-width: 767px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (max-width: 576px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content {
    row-gap: 20px;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area {
  display: flex;
  align-items: center;
  gap: 18px;
  position: relative;
  padding-right: 66px;
}
@media (max-width: 1399px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area {
    padding-right: 20px;
    gap: 12px;
  }
}
@media (max-width: 991px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area {
    padding-right: 0;
  }
}
@media (max-width: 767px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area {
    display: inline-flex;
    flex-wrap: wrap;
  }
}
@media (max-width: 576px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area {
    gap: 10px;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area::after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  width: 1px;
  height: 67px;
  background-color: #BDBDBD;
}
@media (max-width: 991px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area::after {
    display: none;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area .icon img {
  min-width: 60px;
}
@media (max-width: 576px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area .icon img {
    min-width: unset;
    width: 55px;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area .content-area h5 {
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 20px;
  line-height: 1.5;
  color: var(--primary-color2);
  margin-bottom: 5px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area .content-area h5 {
    font-size: 18px;
  }
}
@media (max-width: 576px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area .content-area h5 {
    font-size: 18px;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area .content-area p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  line-height: 1.4;
  color: var(--paragraph-color);
  margin-bottom: 0;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area .content-area p {
    font-size: 14px;
  }
}
@media (max-width: 576px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area .content-area p {
    font-size: 14px;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .company-icon-area .content-area p span {
  font-weight: 500;
  color: var(--title-color2);
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription {
  position: relative;
  padding-right: 66px;
}
@media (max-width: 1399px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription {
    padding-right: 20px;
  }
}
@media (max-width: 991px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription {
    padding-right: 0;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription::after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  width: 1px;
  height: 67px;
  background-color: #BDBDBD;
}
@media (max-width: 991px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription::after {
    display: none;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 14px;
  max-width: 418px;
  width: 100%;
}
@media (max-width: 576px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription ul {
    gap: 8px;
    row-gap: 14px;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription ul li {
  background: #faf4db;
  padding: 5px 15px;
  border-radius: 5px;
  display: flex;
  gap: 4px;
  align-items: center;
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #8e5e0f;
  text-align: center;
}
@media (max-width: 576px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription ul li {
    font-size: 13px;
    padding: 5px 12px;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription ul li.style-2 {
  background: #e4f7ff;
  color: #0e658c;
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription ul li.style-2 svg {
  fill: #0e658c;
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription ul li.style-3 {
  background: #fff0f5;
  color: #6d1e38;
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription ul li.style-4 {
  background: #edfedd;
  color: #243f0b;
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .job-discription ul li.style-5 {
  background: #f3edfb;
  color: #322052;
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .button-area {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 30px;
}
@media (max-width: 991px) {
  .breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .button-area {
    justify-content: start;
  }
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .button-area .icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #c5c5c5;
  display: flex;
  align-items: center;
  justify-content: center;
}
.breadcrumb-section.style-2 .breadcrumb-wrap .breadcrumb-content .button-area .icon svg {
  fill: var(--paragraph-color);
  margin-top: 2px;
}

/*================================================
15. sidebar area
=================================================*/
.range-wrap {
  padding-top: 10px;
}
.range-wrap .slider-labels {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.range-wrap .slider-labels .caption {
  color: var(--paragraph-color);
  font-family: var(--font-inter);
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
  letter-spacing: 0.3px;
}
.range-wrap .noUi-target,
.range-wrap .range-wrap .noUi-target * {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  touch-action: none;
  -moz-user-select: none;
  user-select: none;
  box-sizing: border-box;
}
.range-wrap .noUi-target {
  position: relative;
  direction: ltr;
}
.range-wrap .noUi-base {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  /* Fix 401 */
}
.range-wrap .noUi-origin {
  position: absolute;
  right: 0;
  top: 0;
  left: 0;
  bottom: 0;
}
.range-wrap .noUi-handle {
  position: relative;
  z-index: 1;
}
.range-wrap .noUi-stacking .noUi-handle {
  /* This class is applied to the lower origin when
     its values is > 50%. */
  z-index: 10;
}
.range-wrap .noUi-state-tap .noUi-origin {
  transition: left 0.3s, top 0.3s;
}
.range-wrap .noUi-state-drag * {
  cursor: inherit !important;
}
.range-wrap .noUi-base,
.range-wrap .range-wrap .noUi-handle {
  transform: translate3d(0, 0, 0);
}
.range-wrap .noUi-horizontal {
  height: 4px;
}
.range-wrap .noUi-horizontal .noUi-handle {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  left: 0px;
  right: 0px;
  top: -4px;
  background-color: var(--primary-color2);
  cursor: pointer;
}
.range-wrap .noUi-background {
  background: #ececec;
  height: 5px;
  border-radius: 10px;
}
.range-wrap .noUi-connect {
  background: var(--title-color);
  transition: background 450ms;
  border-radius: 10px;
  height: 5px;
}
.range-wrap .noUi-target {
  border-radius: 10px;
}

.left-sidebar .widget-search {
  border: none;
  padding: 0;
}
.left-sidebar .widget-search .wp-block-search__inside-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  background: var(--white-color);
}
.left-sidebar .widget-search .wp-block-search__inside-wrapper input {
  background-color: transparent;
  color: #a6a6a6;
  font-family: var(--font-inter);
  font-size: 14px;
  font-weight: 400;
  border: none;
  outline: none;
  width: 100%;
  padding: 0px 25px;
  height: 52px;
  border-radius: unset;
}
.left-sidebar .widget-search .wp-block-search__inside-wrapper button {
  min-width: 52px;
  max-width: 52px;
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  background-color: var(--title-color);
  overflow: hidden;
  position: relative;
  z-index: 1;
  border-radius: 0 12px 12px 0;
}
.left-sidebar .widget-search .wp-block-search__inside-wrapper button svg {
  fill: var(--white-color);
}
.left-sidebar .widget-search .wp-block-search__inside-wrapper button::after {
  position: absolute;
  content: "";
  display: block;
  left: 15%;
  right: -20%;
  top: -4%;
  height: 150%;
  width: 150%;
  bottom: 0;
  border-radius: 2px;
  background-color: var(--primary-color2);
  transform: skewX(45deg) scale(0, 1);
  z-index: -1;
  transition: all 0.5s ease-out 0s;
}
.left-sidebar .widget-search .wp-block-search__inside-wrapper button:hover svg {
  fill: var(--white-color);
}
.left-sidebar .widget-search .wp-block-search__inside-wrapper button:hover::after {
  transform: skewX(45deg) scale(1, 1);
}
.left-sidebar .left-sidebar-wrapper {
  background: #f2f2f2;
  border-radius: 10px;
  padding: 30px;
}
@media (max-width: 1399px) {
  .left-sidebar .left-sidebar-wrapper {
    padding: 30px 25px;
  }
}
@media (max-width: 576px) {
  .left-sidebar .left-sidebar-wrapper {
    padding: 25px 20px;
  }
}
.left-sidebar .left-sidebar-wrapper.style-2 {
  padding: 15px;
}
.left-sidebar .left-sidebar-wrapper.style-2 .single-widgets {
  padding: 25px;
}
.left-sidebar .left-sidebar-wrapper .single-widgets {
  border-bottom: 1px solid var(--white-color2);
  padding-bottom: 35px;
  background: var(--white-color);
  padding: 30px;
  border-radius: 12px;
}
@media (max-width: 576px) {
  .left-sidebar .left-sidebar-wrapper .single-widgets {
    padding: 25px 20px;
  }
}
.left-sidebar .left-sidebar-wrapper .single-widgets:last-child {
  border-bottom: none;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .widget-title {
  margin-bottom: 25px;
}
@media (max-width: 576px) {
  .left-sidebar .left-sidebar-wrapper .single-widgets .widget-title {
    margin-bottom: 20px;
  }
}
.left-sidebar .left-sidebar-wrapper .single-widgets .widget-title h5 {
  color: var(--title-color2);
  font-family: var(--font-mulish);
  text-transform: capitalize;
  font-size: 22px;
  line-height: 1;
  margin-bottom: 0;
  font-weight: 700;
  text-align: left;
}
@media (max-width: 576px) {
  .left-sidebar .left-sidebar-wrapper .single-widgets .widget-title h5 {
    font-size: 20px;
  }
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container {
  max-height: 300px;
  overflow: hidden;
  overflow-y: auto;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container::-webkit-scrollbar {
  display: none;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li {
  margin-bottom: 20px;
  position: relative;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li:last-child {
  margin-bottom: 0;
}
@media (max-width: 576px) {
  .left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li {
    margin-bottom: 15px;
  }
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .containerss {
  display: flex;
  gap: 5px;
  width: 100%;
  position: relative;
  padding-left: 25px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
@media (max-width: 576px) {
  .left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .containerss {
    padding-left: 20px;
  }
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .containerss span {
  color: var(--paragraph-color);
  line-height: 1.2;
  text-transform: capitalize;
  font-family: var(--font-inter);
  font-size: 16px;
  font-weight: 400;
}
@media (max-width: 576px) {
  .left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .containerss span {
    font-size: 15px;
  }
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .containerss input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .containerss input:checked ~ .checkmark {
  background-color: transparent;
  border-color: var(--primary-color2);
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .containerss input:checked ~ .checkmark::after {
  content: "";
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 1px;
  background: var(--primary-color2);
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .containerss input:checked ~ span {
  color: var(--primary-color2);
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .containerss .checkmark {
  position: absolute;
  top: 1px;
  left: 0;
  height: 12px;
  width: 12px;
  background-color: transparent;
  border: 1px solid var(--paragraph-color);
  border-radius: 2px;
  margin-top: 2px;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li i {
  position: absolute;
  top: 1px;
  right: 0;
  font-size: 13px;
  line-height: 1;
  cursor: pointer;
  transition: 0.5s ease;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li i.active {
  transform: rotate(90deg);
}
.left-sidebar .left-sidebar-wrapper .single-widgets .checkbox-container ul li .sub-category {
  position: static;
  min-width: 200px;
  border: none;
  opacity: 1;
  visibility: visible;
  transform: translateY(0px);
  padding: 20px 24px 0px;
  display: none;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .recent-post-section {
  display: flex;
  align-items: center;
  gap: 14px;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .recent-post-section .recent-post-image img {
  min-width: 59px;
  border-radius: 4px;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .recent-post-section .recent-post-content h6 {
  margin-bottom: 8px;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .recent-post-section .recent-post-content h6 a {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 16px;
  line-height: 1.3;
  color: var(--title-color2);
  text-transform: capitalize;
  transition: 0.5s;
}
.left-sidebar .left-sidebar-wrapper .single-widgets .recent-post-section .recent-post-content h6 a:hover {
  color: var(--primary-color2);
}
.left-sidebar .left-sidebar-wrapper .single-widgets .recent-post-section .recent-post-content p {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 14px;
  line-height: 100%;
  color: var(--paragraph-color);
  margin-bottom: 0;
}
.left-sidebar .left-sidebar-wrapper .single-widgets.style-2 {
  padding: 30px;
  border: 1px solid var(--border-color);
}
@media (max-width: 1399px) {
  .left-sidebar .left-sidebar-wrapper .single-widgets.style-2 {
    padding: 30px 20px;
  }
}

.sidebar-banner {
  position: relative;
}
.sidebar-banner .sidebar-banner-image {
  position: relative;
}
.sidebar-banner .sidebar-banner-image img {
  border-radius: 20px;
}
.sidebar-banner .banner-content {
  position: absolute;
  top: 0;
  left: 0;
  padding: 25px 30px;
}
@media (min-width: 1400px) and (max-width: 1599px) {
  .sidebar-banner .banner-content {
    padding: 15px 25px;
  }
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .sidebar-banner .banner-content {
    padding: 15px 25px;
  }
}
.sidebar-banner .banner-content h5 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 22px;
  line-height: 1.2;
  color: var(--title-color2);
  text-transform: capitalize;
  margin-bottom: 15px;
}
.sidebar-banner .banner-content .primary-btn-2 {
  padding: 6px 25px;
}

.pagination-area .pagination {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  gap: 10px;
}
.pagination-area .pagination .page-item a {
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 16px;
  line-height: 1;
  text-align: center;
  color: var(--title-color2);
  width: 27px;
  height: 28px;
  background: transparent;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.35s;
}
.pagination-area .pagination .page-item a svg {
  fill: var(--title-color2);
}
.pagination-area .pagination .page-item a:hover {
  background: var(--primary-color2);
  color: var(--white-color);
}
.pagination-area .pagination .page-item.active a {
  background: var(--primary-color2);
  color: var(--white-color);
}
.pagination-area .pagination .arrow a {
  width: 27px;
  height: 28px;
  border: 1px solid #a6a6a6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.5s;
}
.pagination-area .pagination .arrow a svg {
  fill: #a6a6a6;
  transition: 0.5s;
}
.pagination-area .pagination .arrow a:hover {
  border: 1px solid var(--primary-color2);
}
.pagination-area .pagination .arrow a:hover svg {
  fill: var(--primary-color2);
}

.product-card-top-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 25px;
}
@media (max-width: 576px) {
  .product-card-top-area {
    gap: 8px;
    flex-direction: column;
    align-items: start;
  }
}
.product-card-top-area .left-content h6 {
  font-family: var(--font-inter);
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  letter-spacing: 0.02em;
  text-align: left;
  color: var(--paragraph-color);
  margin-bottom: 0;
}
@media (max-width: 576px) {
  .product-card-top-area .left-content h6 {
    font-size: 14px;
  }
}
.product-card-top-area .left-content h6 span {
  font-weight: 600;
  color: var(--title-color2);
}
.product-card-top-area .grid-view {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 10px;
}
.product-card-top-area .grid-view li {
  border: 1px solid #eeeeee;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  transition: 0.35s;
  min-height: 24px;
  min-width: 24px;
  cursor: pointer;
  border-radius: 4px;
}
.product-card-top-area .grid-view li svg path {
  stroke: #a0a0a0;
}
.product-card-top-area .grid-view li:hover {
  background: var(--primary-color2);
}
.product-card-top-area .grid-view li:hover svg path {
  stroke: var(--white-color);
}
.product-card-top-area .grid-view li.active {
  background: var(--primary-color2);
}
.product-card-top-area .grid-view li.active svg path {
  stroke: var(--white-color);
}
.product-card-top-area .right-content {
  display: flex;
  align-items: center;
  gap: 40px;
}
@media (max-width: 767px) {
  .product-card-top-area .right-content {
    display: inline-flex;
    flex-wrap: wrap;
  }
}
.product-card-top-area .right-content.style-2 {
  gap: 42px;
}
@media (max-width: 767px) {
  .product-card-top-area .right-content.style-2 {
    gap: 24px;
  }
}
.product-card-top-area .right-content .filter {
  display: flex;
  align-items: center;
  gap: 10px;
  line-height: 1;
  cursor: pointer;
  border: 1px solid var(--white-color2);
  padding: 5px 8px;
}
.product-card-top-area .right-content .filter .filter-icon svg {
  fill: var(--title-color);
}
.product-card-top-area .right-content .filter span {
  color: var(--title-color);
  font-family: var(--font-inter);
  font-size: 14px;
  font-weight: 400;
  line-height: 1;
  letter-spacing: 0.02em;
  text-align: left;
}
.product-card-top-area .right-content .category-area .nice-select {
  color: var(--title-color2);
  font-family: var(--font-inter);
  font-size: 14px;
  font-weight: 400;
  display: flex;
  gap: 20px;
  width: 168px;
  background: transparent;
  padding-left: 15px;
  padding-right: 15px;
  border-radius: 0;
  border: none;
  padding-left: 0;
}
@media (max-width: 767px) {
  .product-card-top-area .right-content .category-area .nice-select {
    width: 160px;
  }
}
.product-card-top-area .right-content .category-area .nice-select .current {
  color: var(--title-color2);
  font-family: var(--font-inter);
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
}
.product-card-top-area .right-content .category-area .nice-select:after {
  border-bottom: 1px solid var(--title-color);
  border-right: 1px solid var(--title-color);
  content: "";
  display: block;
  height: 9px;
  margin-top: -5px;
  pointer-events: none;
  position: absolute;
  right: 7px;
  top: 46%;
  width: 9px;
}
.product-card-top-area .right-content .category-area .nice-select .option {
  font-family: var(--font-inter);
  font-size: 13px;
  text-align: left;
  text-transform: capitalize;
  min-height: 35px;
  color: var(--title-color);
  padding-left: 25px;
}
@media (max-width: 576px) {
  .product-card-top-area .right-content .category-area .nice-select .option {
    padding-left: 15px;
  }
}
.product-card-top-area .right-content .category-area .nice-select .list {
  background-color: var(--white-color);
  box-shadow: 0px 4px 32px 0px rgba(129, 129, 129, 0.2509803922);
  border: 1px solid #eeeeee;
  width: 230px;
  border-radius: unset;
}
@media (max-width: 1699px) {
  .product-card-top-area .right-content .category-area .nice-select .list {
    left: -25px;
  }
}
@media (max-width: 1399px) {
  .product-card-top-area .right-content .category-area .nice-select .list {
    left: -50px;
  }
}
@media (max-width: 767px) {
  .product-card-top-area .right-content .category-area .nice-select .list {
    left: unset;
    right: 0;
  }
}
@media (max-width: 576px) {
  .product-card-top-area .right-content .category-area .nice-select .list {
    left: 0;
    width: 200px;
  }
}
.product-card-top-area .right-content .category-area .nice-select .option:hover,
.product-card-top-area .right-content .category-area .nice-select .option.focus,
.product-card-top-area .right-content .category-area .nice-select .option.selected.focus {
  background-color: #f6f6f6;
}
.product-card-top-area .right-content .filter-content h6 {
  font-family: var(--font-inter);
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  letter-spacing: 0.02em;
  text-align: left;
  color: var(--title-color);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.list-grid-product-wrap.column-2-wrapper .item {
  width: 50%;
}
@media (max-width: 767px) {
  .list-grid-product-wrap.column-2-wrapper .item {
    display: none;
  }
}
.list-grid-product-wrap.column-1-wrapper .item {
  width: 100%;
}
.list-grid-product-wrap.column-1-wrapper .feature-card .feature-wrape {
  -webkit-mask-image: url(../image/featured-bg2.png);
          mask-image: url(../image/featured-bg2.png);
  -webkit-mask-position: right bottom;
          mask-position: right bottom;
}
.list-grid-product-wrap.column-1-wrapper .feature-card .feature-wrape .job-discription ul {
  margin-bottom: 45px;
}

/*================================================
16. faq section
=================================================*/
.faq-section .faq-item h4 {
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 28px;
  line-height: 1;
  color: var(--title-color);
  margin-bottom: 0;
}
.faq-section .faq-wrap .accordion .accordion-item {
  border: none;
  background-color: transparent;
  border-bottom: 1px solid var(--border-color);
}
.faq-section .faq-wrap .accordion .accordion-item:first-child .accordion-header .accordion-button {
  padding-top: 0;
}
.faq-section .faq-wrap .accordion .accordion-item:last-child {
  border-bottom: unset;
}
.faq-section .faq-wrap .accordion .accordion-item:last-child .accordion-header .accordion-button {
  padding-bottom: 0;
}
.faq-section .faq-wrap .accordion .accordion-item .accordion-header .accordion-button {
  border: none;
  border-radius: unset;
  box-shadow: none;
  color: var(--title-color);
  font-family: var(--font-inter);
  font-size: 18px;
  font-weight: 500;
  line-height: 1.4;
  text-transform: capitalize;
  padding: 17px 0;
  background-color: transparent;
}
@media (max-width: 1399px) {
  .faq-section .faq-wrap .accordion .accordion-item .accordion-header .accordion-button {
    font-size: 19px;
  }
}
@media (max-width: 991px) {
  .faq-section .faq-wrap .accordion .accordion-item .accordion-header .accordion-button {
    font-size: 18px;
  }
}
.faq-section .faq-wrap .accordion .accordion-item .accordion-header .accordion-button::after {
  width: unset;
  height: unset;
  content: "\f4fe";
  font-family: bootstrap-icons;
  background-image: none;
  font-weight: 800;
  font-size: 22px;
  color: var(--primary-color2);
}
.faq-section .faq-wrap .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed) {
  background-color: unset;
}
.faq-section .faq-wrap .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed)::after {
  content: "\f2ea";
}
@media (max-width: 576px) {
  .faq-section .faq-wrap .accordion .accordion-item .accordion-header .accordion-button {
    font-size: 16px;
  }
}
.faq-section .faq-wrap .accordion .accordion-item .accordion-body {
  padding: 15px 0 20px;
  color: var(--paragraph-color);
  font-family: var(--font-inter);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0.48px;
  border-top: 1px solid var(--primary-color2);
}
@media (max-width: 991px) {
  .faq-section .faq-wrap .accordion .accordion-item .accordion-body {
    font-size: 16px;
  }
}

/*================================================
17. blog section2
=================================================*/
.blog-section2 .blog-card2 .blog-card-image {
  width: 100%;
}
.blog-section2 .blog-card2 .blog-card-image img {
  width: 100%;
  border-radius: 10px;
}
.blog-section2 .blog-card2 .blog-card-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 29px;
  margin-bottom: 10px;
}
.blog-section2 .blog-card2 .blog-card-content ul li {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 14px;
  line-height: 1;
  color: #595959;
  position: relative;
}
.blog-section2 .blog-card2 .blog-card-content ul li a {
  color: #595959;
  transition: 0.5s;
}
.blog-section2 .blog-card2 .blog-card-content ul li a:hover {
  color: var(--primary-color2);
}
.blog-section2 .blog-card2 .blog-card-content ul li::after {
  content: "";
  height: 14px;
  width: 1px;
  background-color: #d9d9d9;
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
}
@media (max-width: 576px) {
  .blog-section2 .blog-card2 .blog-card-content ul li::after {
    display: none;
    visibility: hidden;
  }
}
.blog-section2 .blog-card2 .blog-card-content ul li:first-child::after {
  display: none;
}
.blog-section2 .blog-card2 .blog-card-content h3 {
  margin-bottom: 23px;
}
@media (max-width: 576px) {
  .blog-section2 .blog-card2 .blog-card-content h3 {
    margin-bottom: 15px;
  }
}
.blog-section2 .blog-card2 .blog-card-content h3 a {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 30px;
  line-height: 1.3;
  color: var(--title-color2);
  text-transform: capitalize;
  transition: 0.35s;
}
@media (max-width: 1399px) {
  .blog-section2 .blog-card2 .blog-card-content h3 a {
    font-size: 26px;
  }
}
@media (max-width: 576px) {
  .blog-section2 .blog-card2 .blog-card-content h3 a {
    font-size: 21px;
  }
}
.blog-section2 .blog-card2 .blog-card-content h3 a:hover {
  color: var(--primary-color2);
}
.blog-section2 .blog-card2 .blog-card-content p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 18px;
  line-height: 1.6;
  color: var(--paragraph-color);
  margin-bottom: 28px;
}
@media (max-width: 576px) {
  .blog-section2 .blog-card2 .blog-card-content p {
    font-size: 16px;
    margin-bottom: 22px;
  }
}

/*================================================
18. contact us section
=================================================*/
.contact-us-section .inquiry-form {
  border: 1px solid var(--white-color2);
  padding: 40px;
}
@media (max-width: 576px) {
  .contact-us-section .inquiry-form {
    padding: 25px;
  }
}
.contact-us-section .contact-info {
  background: #f2f2f2;
  padding: 35px 30px;
  border-radius: 20px;
}
@media (max-width: 1399px) {
  .contact-us-section .contact-info {
    padding: 25px 26px;
  }
}
@media (max-width: 1199px) {
  .contact-us-section .contact-info {
    padding: 25px 26px;
  }
}
@media (max-width: 767px) {
  .contact-us-section .contact-info {
    padding: 23px;
  }
}
.contact-us-section .contact-info ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 24px;
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .contact-us-section .contact-info ul {
    gap: 15px;
    margin-bottom: 20px;
  }
}
.contact-us-section .contact-info ul li {
  display: flex;
  align-items: center;
  gap: 10px;
}
.contact-us-section .contact-info ul li .icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid var(--primary-color2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.35s;
}
.contact-us-section .contact-info ul li .icon svg {
  fill: var(--primary-color2);
}
.contact-us-section .contact-info ul li .icon:hover {
  background: var(--primary-color2);
}
.contact-us-section .contact-info ul li .icon:hover svg {
  fill: var(--white-color);
}
.contact-us-section .contact-info ul li .content {
  display: flex;
  flex-direction: column;
  max-width: 230px;
  gap: 5px;
}
.contact-us-section .contact-info ul li .content a {
  color: #595959;
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  transition: 0.35s;
  line-height: 1.5;
}
.contact-us-section .contact-info ul li .content a:hover {
  color: var(--primary-color2);
}
.contact-us-section .contact-info h5 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 20px;
  line-height: 1;
  color: var(--title-color2);
  text-transform: capitalize;
  margin-bottom: 20px;
}
.contact-us-section .contact-info .service-available {
  display: flex;
  align-items: center;
  gap: 16px;
}
.contact-us-section .contact-info .service-available p {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 18px;
  line-height: 30px;
  color: var(--paragraph-color);
  margin-bottom: 0;
}
.contact-us-section .contact-info .service-available span {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #595959;
}
.contact-us-section .social-area {
  margin-top: 37px;
}
.contact-us-section .social-area h5 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 22px;
  line-height: 1;
  color: var(--title-color2);
  text-transform: capitalize;
  margin-bottom: 25px;
}
.contact-us-section .social-area .social-link {
  padding: 0;
  margin: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 12px;
}
.contact-us-section .social-area .social-link li {
  margin-bottom: 0;
}
.contact-us-section .social-area .social-link li a {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  transition: 0.5s;
  border: 1px solid var(--primary-color2);
}
.contact-us-section .social-area .social-link li a i {
  color: var(--primary-color2);
  font-size: 14px;
  transition: 0.5s;
}
.contact-us-section .social-area .social-link li a i.bi-twitter-x {
  font-size: 12px;
}
.contact-us-section .social-area .social-link li a:hover {
  background-color: var(--primary-color2);
  color: var(--white-color);
}
.contact-us-section .social-area .social-link li a:hover i {
  color: var(--white-color);
}

/*================================================
19. blog details section
=================================================*/
.contact-map-section {
  overflow: hidden;
  line-height: 1;
  margin-bottom: -2px;
  padding: 0 80px;
  border-radius: 30px;
}
.contact-map-section iframe {
  width: 100%;
  height: 100%;
  min-height: 620px;
  filter: invert(1) hue-rotate(180deg) brightness(0.8) grayscale(1);
  border-radius: 30px 30px 0 0;
}
@media (max-width: 1399px) {
  .contact-map-section iframe {
    min-height: 550px;
  }
}
@media (max-width: 991px) {
  .contact-map-section iframe {
    min-height: 500px;
  }
}
@media (max-width: 576px) {
  .contact-map-section iframe {
    min-height: 450px;
  }
}

/*================================================
20. blog details section
=================================================*/
.blog-details-wrapper .line-break {
  height: 20px;
  display: block;
}
@media (max-width: 1199px) {
  .blog-details-wrapper .line-break {
    height: 15px;
  }
}
.blog-details-wrapper .post-author-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  flex-wrap: wrap;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .blog-details-wrapper .post-author-meta .author-and-date {
    max-width: 420px;
  }
}
@media (max-width: 991px) {
  .blog-details-wrapper .post-author-meta .author-and-date {
    max-width: 400px;
  }
}
@media (max-width: 767px) {
  .blog-details-wrapper .post-author-meta .author-and-date {
    max-width: unset;
  }
}
@media (max-width: 576px) {
  .blog-details-wrapper .post-author-meta .author-and-date {
    gap: 10px;
  }
}
.blog-details-wrapper .post-author-meta .author-and-date .author-area {
  display: flex;
  align-items: center;
  gap: 14px;
}
@media (max-width: 576px) {
  .blog-details-wrapper .post-author-meta .author-and-date .author-area {
    gap: 10px;
  }
}
.blog-details-wrapper .post-author-meta .author-and-date .author-area .author-img img {
  min-width: 60px;
  max-width: 60px;
  height: 60px;
  border-radius: 50%;
}
@media (max-width: 576px) {
  .blog-details-wrapper .post-author-meta .author-and-date .author-area .author-img img {
    min-width: 40px;
    max-width: 40px;
    height: 40px;
  }
}
.blog-details-wrapper .post-author-meta .author-and-date .author-area .author-name-desig span {
  color: #545454;
  font-family: var(--font-inter);
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  display: inline-block;
  margin-bottom: 10px;
}
@media (max-width: 576px) {
  .blog-details-wrapper .post-author-meta .author-and-date .author-area .author-name-desig span {
    font-size: 11px;
    margin-bottom: 5px;
  }
}
.blog-details-wrapper .post-author-meta .author-and-date .author-area .author-name-desig h6 {
  margin-bottom: 0;
}
.blog-details-wrapper .post-author-meta .author-and-date .author-area .author-name-desig h6 a {
  color: var(--title-color2);
  font-family: var(--font-inter);
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.2;
}
@media (max-width: 576px) {
  .blog-details-wrapper .post-author-meta .author-and-date .author-area .author-name-desig h6 a {
    font-size: 16px;
  }
}
.blog-details-wrapper .post-author-meta .date span {
  color: #545454;
  font-family: var(--font-inter);
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  display: inline-block;
  margin-bottom: 10px;
}
@media (max-width: 576px) {
  .blog-details-wrapper .post-author-meta .date span {
    font-size: 11px;
    margin-bottom: 5px;
  }
}
.blog-details-wrapper .post-author-meta .date h6 {
  color: var(--title-color2);
  font-family: var(--font-inter);
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 0;
}
@media (max-width: 576px) {
  .blog-details-wrapper .post-author-meta .date h6 {
    font-size: 16px;
  }
}
.blog-details-wrapper .post-author-meta .social-area {
  display: flex;
  align-items: center;
  gap: 20px;
}
@media (max-width: 576px) {
  .blog-details-wrapper .post-author-meta .social-area {
    gap: 15px;
  }
}
.blog-details-wrapper .post-author-meta .social-area h6 {
  color: var(--title-color);
  font-family: var(--font-inter);
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  margin-bottom: 0;
}
.blog-details-wrapper .post-author-meta .social-area .social-link {
  padding: 0;
  margin: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 12px;
}
.blog-details-wrapper .post-author-meta .social-area .social-link li {
  margin-bottom: 0;
}
.blog-details-wrapper .post-author-meta .social-area .social-link li a {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #595959;
  transition: 0.5s;
}
.blog-details-wrapper .post-author-meta .social-area .social-link li a i {
  color: #595959;
  font-size: 14px;
  transition: 0.5s;
}
.blog-details-wrapper .post-author-meta .social-area .social-link li a i.bi-twitter-x {
  font-size: 12px;
}
.blog-details-wrapper .post-author-meta .social-area .social-link li a:hover {
  background-color: var(--primary-color2);
  color: var(--white-color);
}
.blog-details-wrapper .post-author-meta .social-area .social-link li a:hover i {
  color: var(--white-color);
}
.blog-details-wrapper .post-thumb img {
  width: 100%;
  border-radius: 30px;
}
.blog-details-wrapper .blog-details-wrape .blog-details-content h3 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 34px;
  line-height: 1;
  color: var(--title-color2);
  margin-bottom: 0;
}
.blog-details-wrapper .blog-details-wrape .blog-details-content h5 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 22px;
  line-height: 1;
  color: var(--title-color2);
  margin-bottom: 0;
}
.blog-details-wrapper .blog-details-wrape .blog-details-content h4 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 28px;
  line-height: 1;
  color: var(--title-color2);
  margin-bottom: 0;
}
.blog-details-wrapper .blog-details-wrape .blog-details-content p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 18px;
  line-height: 1.5;
  color: var(--paragraph-color);
  margin-bottom: 0;
}

/*================================================
21. form inner
=================================================*/
.form-inner input {
  width: 100%;
  height: 52px;
  background-color: transparent !important;
  font-family: var(--font-inter);
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  color: var(--title-color2);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-bottom: 1px solid #d9d9d9;
}
@media (max-width: 576px) {
  .form-inner input {
    padding: 15px 20px;
  }
}
.form-inner input:focus {
  background-color: var(--white-color);
  color: var(--paragraph-color);
}
.form-inner textarea {
  width: 100%;
  background-color: transparent !important;
  font-family: var(--font-inter);
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  color: var(--title-color2);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: none;
  border-bottom: 1px solid #d9d9d9;
}
.form-inner textarea:focus {
  color: var(--paragraph-color);
  outline: none;
  border: none;
}
.form-inner button {
  padding: 14px 45px;
  border-radius: 6px;
}
.form-inner button::after {
  background-color: var(--primary-color);
}
.form-inner button:hover {
  color: var(--white-color);
}

/*================================================
22. inquiry form
=================================================*/
.inquiry-form {
  background: #f0f0f0;
  border-radius: 30px;
  padding: 45px;
}
.inquiry-form.contact-inquiry {
  background: rgba(238, 242, 229, 0.5490196078);
}
.inquiry-form.contact-inquiry button {
  padding: 14px 61px;
}
@media (max-width: 1399px) {
  .inquiry-form {
    padding: 60px;
  }
}
@media (max-width: 1199px) {
  .inquiry-form {
    padding: 50px 40px;
  }
}
@media (max-width: 767px) {
  .inquiry-form {
    padding: 40px 25px;
  }
}
@media (max-width: 576px) {
  .inquiry-form {
    padding: 35px 15px;
  }
}
.inquiry-form .title {
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .inquiry-form .title {
    margin-bottom: 20px;
  }
}
.inquiry-form .title h4 {
  color: var(--title-color2);
  text-transform: capitalize;
  margin-bottom: 0;
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 24px;
  line-height: 1;
}
@media (max-width: 991px) {
  .inquiry-form .title h4 {
    font-size: 30px;
  }
}
@media (max-width: 576px) {
  .inquiry-form .title h4 {
    font-size: 28px;
  }
}
.inquiry-form .form-inner input {
  background-color: var(--white-color);
}
.inquiry-form .form-inner textarea {
  color: var(--title-color2);
}
.inquiry-form .form-inner textarea:focus {
  outline: none;
  border: none;
}

/*================================================
23.  company details section
=================================================*/
.company-details-section .company-left-content h5 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 22px;
  line-height: 1;
  color: var(--title-color2);
  margin-bottom: 23px;
}
.company-details-section .company-left-content p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 18px;
  line-height: 1.6;
  color: var(--paragraph-color);
  margin-bottom: 40px;
}
@media (max-width: 576px) {
  .company-details-section .company-left-content p {
    font-size: 15px;
    line-height: 1.8;
  }
}
.company-details-section .company-left-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-bottom: 40px;
}
.company-details-section .company-left-content ul:last-child {
  margin-bottom: 0;
}
.company-details-section .company-left-content ul li {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 18px;
  line-height: 1.5;
  color: var(--paragraph-color);
  padding-left: 15px;
  position: relative;
  margin-bottom: 8px;
}
@media (max-width: 576px) {
  .company-details-section .company-left-content ul li {
    font-size: 15px;
    line-height: 1.8;
  }
}
.company-details-section .company-left-content ul li::before {
  content: "";
  height: 5px;
  width: 5px;
  border-radius: 50%;
  background-color: var(--paragraph-color);
  position: absolute;
  left: 0;
  top: 10px;
}
.company-details-section .company-left-content ul li:last-child {
  margin-bottom: 0;
}
.company-details-section .company-left-content h6 {
  font-family: var(--font-inter);
  font-weight: 600;
  font-size: 18px;
  line-height: 1;
  color: var(--paragraph-color);
  margin-bottom: 10px;
}
.company-details-section .company-left-content .working-details p {
  margin-bottom: 15px;
}
.company-details-section .company-left-content .working-details ul {
  margin-bottom: 13px;
}
.company-details-section .company-right-content .job-summary-area {
  background: var(--white-color);
  border: 1px solid var(--primary-color2);
  border-radius: 30px;
  padding: 50px 40px;
  margin-top: -150px;
}
@media (max-width: 1199px) {
  .company-details-section .company-right-content .job-summary-area {
    margin-top: 0;
  }
}
@media (max-width: 767px) {
  .company-details-section .company-right-content .job-summary-area {
    padding: 40px 30px;
  }
}
@media (max-width: 576px) {
  .company-details-section .company-right-content .job-summary-area {
    padding: 20px;
  }
}
.company-details-section .company-right-content .job-summary-area ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.company-details-section .company-right-content .job-summary-area ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
}
.company-details-section .company-right-content .job-summary-area ul li:last-child {
  margin-bottom: 0;
}
.company-details-section .company-right-content .job-summary-area ul li .left-content {
  display: flex;
  align-items: center;
  gap: 16px;
}
.company-details-section .company-right-content .job-summary-area ul li .left-content svg {
  fill: var(--paragraph-color);
}
.company-details-section .company-right-content .job-summary-area ul li .left-content p {
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 16px;
  line-height: 1;
  margin-bottom: 0;
  color: var(--paragraph-color);
}
.company-details-section .company-right-content .job-summary-area ul li .right-content p {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 16px;
  line-height: 1;
  color: var(--title-color);
  margin-bottom: 0;
}
.company-details-section .company-right-content .location-area {
  margin-top: 60px;
}
.company-details-section .company-right-content .location-area h5 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 22px;
  line-height: 0;
  color: var(--title-color2);
  margin-bottom: 40px;
}
.company-details-section .company-right-content .location-area iframe {
  width: 100%;
  height: 264px;
  border-radius: 20px;
}
.company-details-section .company-right-content .view-job-btn {
  margin-top: 40px;
}
.company-details-section .company-right-content .view-job-btn a {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 16px;
  line-height: 1.3;
  color: var(--title-color2);
}
.company-details-section .company-right-content .view-job-btn a svg {
  fill: var(--primary-color2);
}
.company-details-section .company-right-content .social-area {
  margin-top: 45px;
}
.company-details-section .company-right-content .social-area h5 {
  font-family: var(--font-inter);
  font-weight: 700;
  font-size: 22px;
  line-height: 1;
  color: var(--title-color2);
  margin-bottom: 23px;
}
.company-details-section .company-right-content .social-area ul {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 15px;
}
.company-details-section .company-right-content .social-area ul li a {
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--white-color);
  border: 1px solid var(--primary-color2);
  border-radius: 50%;
  transition: 0.35s;
}
.company-details-section .company-right-content .social-area ul li a i {
  color: var(--primary-color2);
  transition: 0.35s;
  position: relative;
  left: 0;
  top: 0;
  margin-left: 0;
  font-size: 14px;
}
.company-details-section .company-right-content .social-area ul li a i.bi-twitter-x {
  font-size: 11px;
}
.company-details-section .company-right-content .social-area ul li a:hover {
  background-color: var(--primary-color2);
}
.company-details-section .company-right-content .social-area ul li a:hover i {
  color: var(--white-color);
}

/*================================================
24. gallery view section
=================================================*/
.gallery-view-section .gallery-image img {
  width: 100%;
  border-radius: 20px;
}
@media (max-width: 576px) {
  .gallery-view-section .gallery-image img {
    border-radius: 10px;
  }
}

/*================================================
25. pricing plan area
=================================================*/
.pricing-plan-area .pt-50 {
  padding-top: 50px;
}
@media (max-width: 1199px) {
  .pricing-plan-area .pt-50 {
    padding-top: 0;
  }
}
.pricing-plan-area .pricing-plan-area-wrap {
  position: relative;
  z-index: 1;
}
@media (max-width: 1399px) {
  .pricing-plan-area .pricing-plan-area-wrap {
    height: 100%;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 {
  -webkit-mask-image: url(../image/inner-page/pricing-plan-bg.png);
          mask-image: url(../image/inner-page/pricing-plan-bg.png);
  -webkit-mask-size: cover;
          mask-size: cover;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  height: 100%;
  width: 100%;
  position: relative;
  z-index: 1;
  padding: 90px 30px 35px;
  border-radius: 30px;
  background: #fdf3e9;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 {
    padding: 90px 20px 35px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 {
    padding: 90px 20px 35px;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 {
    padding: 80px 15px 35px;
    border-radius: 15px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area {
  margin-bottom: 25px;
  border-bottom: 1px solid #A6A6A6;
}
@media (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area {
    margin-bottom: 20px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area h2 {
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 40px;
  line-height: 1.3;
  color: var(--primary-color2);
  margin-bottom: 20px;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area h2 {
    font-size: 34px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area h2 {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area h2 {
    font-size: 35px;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area h2 {
    font-size: 30px;
    line-height: 1.1;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area h2 span {
  font-family: var(--font-mulish);
  font-weight: 500;
  font-size: 18px;
  line-height: 1.3;
  color: var(--paragraph-color);
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area h2 span {
    font-size: 16px;
  }
}
@media (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area h2 span {
    font-size: 16px;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .price-area h2 span {
    font-size: 15px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .pricing-list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .pricing-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--paragraph-color);
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 17px;
  line-height: 1.2;
  margin-bottom: 20px;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .pricing-list li {
    font-size: 16px;
    gap: 10px;
  }
}
@media (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .pricing-list li {
    font-size: 16px;
    gap: 10px;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .pricing-list li {
    font-size: 15px;
    gap: 8px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .pricing-list li:last-child {
  margin-bottom: 0;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list {
  padding: 0;
  margin: 0;
  list-style: none;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li {
  margin-bottom: 32px;
}
@media (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li {
    margin-bottom: 27px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li:last-child {
  margin-bottom: 0;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .single-item {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .content h6 {
  color: var(--title-color2);
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 20px;
  line-height: 1;
  margin-bottom: 12px;
}
@media (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .content h6 {
    font-size: 18px;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .content h6 {
    margin-bottom: 8px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .content span {
  color: var(--paragraph-color);
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 18px;
  line-height: 1;
}
@media (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .content span {
    font-size: 16px;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .content span {
    font-size: 15px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .form-check {
  padding: 0;
  min-height: unset;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .form-check .form-check-input {
  width: 47px;
  height: 22px;
  background-color: transparent;
  border: 1px solid var(--primary-color2);
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .form-check .form-check-input:checked {
  background-color: var(--primary-color2);
  border-color: var(--primary-color2);
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .form-check .form-check-input:focus {
  border-color: var(--primary-color2);
  box-shadow: unset;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .pricing-list {
  margin-top: 20px;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .pricing-list li {
  margin-bottom: 15px;
  font-size: 16px;
}
@media (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .pricing-list li {
    font-size: 15px;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .pricing-list li {
    font-size: 14px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .add-on-list li .pricing-list li:last-child {
  margin-bottom: 0;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .title-and-quantity {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 25px;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .title-and-quantity h5 {
  color: var(--title-color2);
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 20px;
  line-height: 1;
  margin-bottom: 0;
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .title-and-quantity h5 {
    font-size: 18px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .title-and-quantity .quantity-area .quantity {
  display: flex;
  gap: 5px;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .title-and-quantity .quantity-area .quantity a {
  height: 24px;
  width: 34px;
  border-radius: 4px;
  background: var(--white-color);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--title-color);
  font-weight: 600;
  cursor: pointer;
  transition: 0.35s;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .title-and-quantity .quantity-area .quantity a i {
  color: var(--title-color);
  transition: 0.35s;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .title-and-quantity .quantity-area .quantity a:hover {
  background: var(--primary-color2);
  border-color: var(--primary-color2);
  color: var(--white-color);
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .title-and-quantity .quantity-area .quantity a:hover i {
  color: var(--white-color);
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .title-and-quantity .quantity-area .quantity input {
  height: 24px;
  width: 34px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--white-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-dmsans);
  font-size: 14px;
  color: var(--title-color);
  font-weight: 600;
  text-align: center;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap table {
  width: 100%;
  margin-bottom: 25px;
}
@media (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap table {
    margin-bottom: 20px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap table tbody tr td {
  color: var(--paragraph-color);
  font-family: var(--font-inter);
  font-weight: 400;
  font-size: 17px;
  line-height: 30px;
  padding-bottom: 10px;
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap table tbody tr td {
    font-size: 15px;
    padding-bottom: 8px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap table tbody tr td:last-child {
  text-align: end;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap table tbody tr:last-child {
  border-top: 1px solid #D9D9D9;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap table tbody tr:last-child td {
  padding-bottom: 0;
  padding-top: 15px;
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap table tbody tr:last-child td {
    padding-top: 10px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .amount-and-btn-area {
  text-align: center;
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .amount-and-btn-area h2 {
  color: var(--title-color2);
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 40px;
  line-height: 1;
  margin-bottom: 35px;
}
@media (max-width: 1399px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .amount-and-btn-area h2 {
    font-size: 38px;
    margin-bottom: 30px;
  }
}
@media (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .amount-and-btn-area h2 {
    font-size: 35px;
    margin-bottom: 25px;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .amount-and-btn-area h2 {
    font-size: 33px;
    margin-bottom: 22px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .pricing-plan-card1 .order-summary-wrap .amount-and-btn-area .primary-btn-2 {
  max-width: 245px;
  width: 100%;
  justify-content: center;
  padding: 12px 21px;
}
.pricing-plan-area .pricing-plan-area-wrap .batch {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 99;
}
@media (max-width: 1399px) {
  .pricing-plan-area .pricing-plan-area-wrap .batch {
    top: 5px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .batch {
    top: 7px;
  }
}
@media (max-width: 767px) {
  .pricing-plan-area .pricing-plan-area-wrap .batch {
    width: 100%;
    padding-left: 120px;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .batch {
    padding-left: 100px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap .batch span {
  background: #ac7a50;
  padding: 13px 25px;
  font-family: var(--font-mulish);
  font-weight: 700;
  font-size: 24px;
  line-height: 100%;
  color: var(--white-color);
  text-transform: capitalize;
  border-radius: 27px 30px 0px 27px;
  display: inline-block;
  min-width: 325px;
  text-align: center;
}
@media (min-width: 1200px) and (max-width: 1399px) {
  .pricing-plan-area .pricing-plan-area-wrap .batch span {
    min-width: 255px;
    font-size: 22px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .pricing-plan-area .pricing-plan-area-wrap .batch span {
    min-width: 230px;
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .pricing-plan-area .pricing-plan-area-wrap .batch span {
    min-width: unset;
    width: 100%;
  }
}
@media (max-width: 576px) {
  .pricing-plan-area .pricing-plan-area-wrap .batch span {
    font-size: 20px;
  }
}
.pricing-plan-area .pricing-plan-area-wrap.two .pricing-plan-card1 {
  background-color: #E4EEFE;
}
.pricing-plan-area .pricing-plan-area-wrap.two .batch span {
  background-color: #7887BF;
}
.pricing-plan-area .pricing-plan-area-wrap.three .pricing-plan-card1 {
  background-color: #EAF4F4;
}
.pricing-plan-area .pricing-plan-area-wrap.three .batch span {
  background-color: #61947E;
}/*# sourceMappingURL=style.css.map */