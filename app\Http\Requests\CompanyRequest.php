<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class CompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,'.Request()->user_id,
            'password' => isset(Request()->id) ? (isset(Request()->password) ? 'required|min:5|max:12' : '') : 'required|min:5|max:12',
            'confirm_password' => isset(Request()->id) ? (isset(Request()->password) ? 'required|same:password' : '') : 'required|same:password',

            'company_name' => 'required|string|unique:companies,company_name,'.Request()->id,
            'company_email' => 'required|email|unique:companies,company_email,'.Request()->id,
            'company_phone_number' => 'required',

            'company_size' => 'required',
            'company_type' => 'required',

            'country_id' => 'required',
            'state_id' => 'required',
            'city_id' => 'required',
            'postal_code' => 'required',
            'address' => 'required',
            'iframe_link' => 'required',

            'company_details' => 'required',
            'company_vision' => 'required',

            'company_logo' => isset(Request()->id) ? (isset(Request()->company_logo) ? 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2048' : '') : 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2048',
            'company_cover_photo' => isset(Request()->company_cover_photo) ? 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:5120' : '',

        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [

            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'Owner Email is required.',
            'username.unique' => 'A user with this User Name  already exists.',
            'email.unique' => 'A user with this email address already exists.',
            'password.required' => 'Password Field is required',
            'confirm_password.required' => 'Confirm Password Field is required',
            'confirm_password.same' => 'Password Confirmation should match the Password',
            'password.max.string' => 'Password Maximun length 12',
            'password.min.string' => 'Password Minimun length 5',

            'company_name.required' => 'Company Name is required',
            'company_email.required' => 'Company Email is required',
            'company_phone_number.required' => 'Company Phone is required',
            'company_size.required' => 'Comapny Size is required',
            'company_type.required' => 'Comapny type is required',

            'country_id.required' => 'Country Name is required',
            'state_id.required' => 'State Name is required',
            'city_id.required' => 'City Name is required',
            'postal_code.required' => 'Postal Code is required',
            'address.required' => 'Address is required',
            'iframe_link.required' => 'Location Iframe link is required',

            'company_details.required' => 'Company Details is required',
            'company_vision.required' => 'Company Vision is required',

            'terms_condition_two.required' => 'Please checked company terms & conditions',

            'company_logo.required' => 'Company Logo is required',
            'company_logo.image' => 'Company Logo will be must image',
            'company_logo.mimes' => 'Company Logo Image type will be jpeg,png,jpg,gif,svg',
            'company_logo.max' => 'Company Logo Image will be less than 2 MB',

            'company_cover_photo.required' => 'Company Cover Photo is required',
            'company_cover_photo.image' => 'Company Cover Photo will be must image',
            'company_cover_photo.mimes' => 'Company Cover Photo  type will be jpeg,png,jpg,gif,svg',
            'company_cover_photo.max' => 'Company Cover Photo  will be less than 5 MB',

        ];
    }

    /** failed Validation
     *  =======  failedValidation =========
     *
     * @param Validator
     * @return Response
     */
    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => false,
            'errors' => $validator->errors(),
        ]));
    }
}
