<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Admin\Blog;
use App\Models\Admin\BlogComment;
use App\Models\Admin\BlogTag;
use App\Models\Admin\CommentReply;
use http\Client\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BlogController extends Controller
{
    /**
     * blogTag
     *
     * @param  string  $slug
     * @return View
     */
    public function blogTag($slug)
    {

        try {
            $tagName = BlogTag::where('slug', $slug)->select('name')->firstOrFail();
            $blogId = BlogTag::where('slug', $slug)->pluck('blog_id')->toArray();
            $blogs = Blog::authorName()->whereIn('id', $blogId)->where('status', 1)->orderBy('id', 'DESC')->paginate(10);

            return view('front.pages.blog.tag-post', compact('blogs', 'tagName'));

        } catch (\Throwable $th) {
            return view('front.pages.404');
        }

    }

    /**
     * details
     *
     * @param  string  $slug
     * @return View
     */
    public function details($slug)
    {
        try {
            $blog = Blog::with('blogcategory', 'comments.replies', 'blogTags')
                ->authorName()
                ->withCount('comments')
                ->where('slug', $slug)->firstOrFail();

            $blogIds = Blog::pluck('slug')->toArray();

            $currentBlogIndex = array_search($blog->slug, $blogIds);
            $nextBlogId = null;
            if (isset($blogIds[$currentBlogIndex + 1])) {
                $nextBlogId = $blogIds[$currentBlogIndex + 1];
            }

            $previousBlogId = null;
            if (isset($blogIds[$currentBlogIndex - 1])) {
                $previousBlogId = $blogIds[$currentBlogIndex - 1];
            }

            return view('front.pages.blog.blog-details', compact('blog', 'nextBlogId', 'previousBlogId'));
        } catch (\Throwable $th) {
            return view('front.pages.404');
        }
    }

    /**
     * nextBlogDetails
     *
     * @param  int  $currentPostId
     * @return view
     */
    public function nextBlogDetails($currentPostId)
    {
        try {
            $currentPostCreatedAt = Blog::where('id', $currentPostId)->value('created_at');
            $blog = Blog::where('created_at', '>', $currentPostCreatedAt)->orderBy('created_at', 'asc')->firstOrFail();

            return view('front.pages.blog.blog-details', compact('blog'));
        } catch (\Throwable $th) {
            //throw $th;
            return view('front.pages.404');
        }
    }

    /**
     * commentStore
     *
     * @param  mixed  $request
     * @return Response
     */
    public function blogComment(Request $request)
    {

        try {

            // return $request->all();
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'email' => 'required|email',
                'comment' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'errors' => $validator->errors(),
                ]);
            }

            if (isset($request->comment_id) && ! empty($request->comment_id)) {
                CommentReply::create([
                    'blog_comment_id' => $request->comment_id,
                    'name' => $request->name,
                    'email' => $request->email,
                    'comment' => $request->comment,
                ]);
            } else {
                BlogComment::create([
                    'blog_id' => $request->blog_id,
                    'name' => $request->name,
                    'email' => $request->email,
                    'comment' => $request->comment,
                ]);

            }

            return response()->json(['status' => true, 'message' => 'Comment Post Successfully', 'comemnt' => 'blog']);

        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'error_message' => 'Something Wrong!']);
        }

    }
}
