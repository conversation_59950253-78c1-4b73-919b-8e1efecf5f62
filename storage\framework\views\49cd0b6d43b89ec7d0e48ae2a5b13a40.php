
<?php

    $singleWidgetData=[];
    if (!empty($singleWidgetDataShow)) 
        $singleWidgetData= $singleWidgetDataShow->getTranslation('widget_content');
    $pageSetting= getThemeOption('page_setting') ?? [];
   $jobListUrl = $pageSetting['job_list_url'] ?? '';
    
?>

<!-- ========== Home1 Category Start============= -->
<div class="home1-category pt-120">
    <div class="container">
        <div class="row mb-70">
            <div class="col-12 d-flex flex-wrap align-items-end justify-content-md-between jsutify-content-start gap-3">
                <div class="section-title1">
                    <h2><?php echo e($singleWidgetData['title'] ?? ''); ?>

                        <span><?php echo e($singleWidgetData['span_color_title']  ?? ''); ?></span>
                    </h2>
                    <p><?php echo clean($singleWidgetData['description'] ?? ''); ?></p>
                </div>
                <div class="explore-btn">
                    <a href="<?php echo e(isset($singleWidgetData['page_link']) ? $singleWidgetData['page_link'] : 'javascript:void(0)'); ?>"> <?php echo e(translate('Explore More')); ?> <span><img
                                src="<?php echo e(asset('frontend/assets/images/icon/explore-elliose.svg')); ?>"
                                alt=""></span></a>
                </div>
            </div>
        </div>


        <div
            class="row row-cols-xxl-5 row-cols-xl-4 row-cols-md-3 row-cols-sm-2 row-cols-1 g-3 cf justify-content-center mb-120">
            <?php $__currentLoopData = activeCategoryCountJob( $singleWidgetData['show_item'] ?? 10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col">
                    <div class="single-category">
                        <div class="category-top">
                            <div class="icon">
                                <?php if(fileExists($folder = 'categories', $fileName = $category->category_image) == true &&
                                        $category->category_image !== ''): ?>
                                    <img src="<?php echo e(asset('storage/categories/' . $category->category_image)); ?>"
                                        alt="">
                                <?php else: ?>
                                    <img
                                        src="<?php echo e(asset('frontend/assets/images/icon/account-finance.svg')); ?>"alt="">
                                <?php endif; ?>
                            </div>
                            <div class="sl-no">
                                <h6><?php echo e($key + 1); ?></h6>
                            </div>
                        </div>
                        <div class="category-content">
                            <h5><a
                                    href="<?php echo e($jobListUrl? $jobListUrl.'?jobcategory='.$category->category_slug:'javascript:void(0)'); ?>"><?php echo e($category->getTranslation('category_name')); ?></a>
                            </h5>
                            <p><?php echo e(translate('Job Available')); ?>: <span><?php echo e($category?->jobs->count()?? 0); ?></span></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</div>
<!-- ========== Home1 Category end============= -->
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/pages/widgets/latest-jobs-category.blade.php ENDPATH**/ ?>