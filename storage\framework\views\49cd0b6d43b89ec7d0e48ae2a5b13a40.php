<?php

    $singleWidgetData = [];
    if (!empty($singleWidgetDataShow)) {
        $singleWidgetData = $singleWidgetDataShow->getTranslation('widget_content');
    }
    $pageSetting = getThemeOption('page_setting') ?? [];
    $jobListUrl = $pageSetting['job_list_url'] ?? '';

?>

<!-- ========== Latest jobs Category Start============= -->
<div class="category-section pt-120 mb-120">
    <div class="container">
        <div class="row mb-35 wow animate fadeInDown" data-wow-delay="200ms" data-wow-duration="1500ms">
            <div class="col-lg-12 d-flex align-items-center justify-content-between flex-wrap gap-3">
                <div class="section-title">
                    <?php if(!empty($singleWidgetData['title'])): ?>
                        <h3><?php echo e($singleWidgetData['title']); ?></h3>
                    <?php endif; ?>
                </div>
                <?php if(!empty($singleWidgetData['page_link'])): ?>
                    <a href="<?php echo e($singleWidgetData['page_link']); ?>" class="primary-btn-3">View All</a>
                <?php endif; ?>
            </div>
        </div>
        <div
            class="row row-cols-xl-5 row-cols-lg-4 row-cols-md-3 row-cols-sm-2 row-cols-1 justify-content-center gy-md-5 gy-4">
            <?php
                $showItem = !empty($singleWidgetData['show_item']) ? (int)$singleWidgetData['show_item'] : 10;
                $categories = jobCategory($showItem);
            ?>

            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col wow animate fadeInDown" data-wow-delay="<?php echo e(200 + ($index * 200)); ?>ms" data-wow-duration="1500ms">
                    <div class="single-category text-center">
                        <div class="category-wrap">
                            <div class="category-icon">
                                <?php if($category->category_icon): ?>
                                    <img src="<?php echo e(asset('storage/' . $category->category_icon)); ?>" alt="<?php echo e($category->getTranslation('category_name')); ?>">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('frontend/assets/images/icon/category-icon' . (($index % 10) + 1) . '.svg')); ?>" alt="<?php echo e($category->getTranslation('category_name')); ?>">
                                <?php endif; ?>
                            </div>
                            <div class="category-content">
                                <h5><a href="<?php echo e(route('find.job', ['category' => $category->category_slug])); ?>"><?php echo e($category->getTranslation('category_name')); ?></a></h5>
                                <p>Available: <span><?php echo e($category->jobs_count ?? 0); ?></span></p>
                            </div>
                        </div>
                        <div class="button-area">
                            <a href="<?php echo e(route('find.job', ['category' => $category->category_slug])); ?>" class="icon">
                                <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.06567 12.9342L12.9341 3.0658M12.9341 3.0658H5.53278M12.9341 3.0658V10.4671"
                                        stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        </div>
    </div>
</div>
<!-- ========== Latest jobs Category End============= -->
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/pages/widgets/latest-jobs-category.blade.php ENDPATH**/ ?>