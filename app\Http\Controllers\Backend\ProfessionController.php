<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProfessionRequest;
use App\Repositories\Admin\ProfessionRepository;
use Illuminate\Http\Request;

class ProfessionController extends Controller
{
    public function __construct(protected ProfessionRepository $profession) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->profession->content();
        }

        return view('admin.pages.profession.index');
    }

    /** new Resource store
     * ========= store============
     *
     * @param ProfessionRequest
     * @return Response
     */
    public function store(ProfessionRequest $request)
    {

        if (!$this->hasPermissions(['profession.add', 'profession.edit'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->profession->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $profession = $this->profession->getById($id);
        $data['name'] = $profession->getTranslation('name', Request()->lang);
        $data['id'] = $profession->id;
        $data['lang'] = Request()->lang;
        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['profession.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->profession->destroy($id);
        return $this->formatResponse($result);

    }

    /** Job Type status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['profession.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->profession->statusChange($id);
        return $this->formatResponse($result);

    }
}
