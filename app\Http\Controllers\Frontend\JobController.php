<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\JobInformationRequest;
use App\Models\Admin\Job;
use App\Models\Admin\JobSkill;
use App\Repositories\Admin\JobCategoryRepostitory;
use App\Repositories\JobRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;

class JobController extends Controller
{
    public function __construct(
        protected JobRepository $job,
        protected JobCategoryRepostitory $category
    ) {}

    /**=== jobLists
     * ========== jobLists =========
     *
     * @return Response
     */

    public function jobLists(Request $request)
    {


        $job_style = 'list_view';
        $param['featured'] = '';
        $param['category_slug'] = '';
        $param['company'] = '';

        $options = [];
        $category = [];
        $jobtype = [];
        $careerlevel = [];
        $daterange = '';
        $experience = [];
        $job_title = '';
        $company_name = '';

        $sortedBy = '';

        if (isset($request->featured)) {
            $options['job_featured'] = 1;
            $param['featured'] = 1;
        }
        if (isset($request->category)) {
            $category = $request->category;
        }
        if (isset($request->jobtype)) {
            $jobtype = $request->jobtype;
        }
        if (isset($request->daterange)) {
            $daterange = $request->daterange;
        }
        if (isset($request->careerlevel)) {
            $careerlevel = $request->careerlevel;
        }
        if (isset($request->experience)) {
            $experience = $request->experience;
        }

        if (isset($request->jobcategory)) {
            $cat = $this->category->getBySlug($request->jobcategory);
            $param['category_slug'] = $request->jobcategory;
            $options['category_id'] = $cat->id;
        }

        if (isset($request->sort)) {
            $sortedBy = $request->sort;
        }
        if (isset($request->job_title)) {
            $job_title = $request->job_title;
        }
        if (isset($request->company)) {
            $company_name = $request->company;
        }

        // dd($company_name);

        $jobs = Job::query();
        $jobs->when($category, function ($q) use ($category) {
            return $q->whereIn('category_id', explode(',', $category));
        });

        $jobs->when($category, function ($q) use ($category) {
            return $q->whereIn('category_id', explode(',', $category));
        });

        if (! empty($jobtype)) {
            $jobs->withWhereHas('jobJobTypes', function ($query) use ($request) {
                return $query->whereIn('type_name', explode(',', $request->jobtype));
            });
        }

        if (! empty($careerlevel)) {
            $jobs->withWhereHas('jobLevels', function ($query) use ($careerlevel) {
                $query->whereIn('career_level_id', explode(',', $careerlevel));
            });
        }
        if (! empty($experience)) {
            $jobs->withWhereHas('experiences', function ($query) use ($experience) {
                return $query->whereIn('experience_id', explode(',', $experience));
            });
        }
        if (! empty($daterange)) {
            $jobs->whereBetween('created_at', [$daterange, Carbon::now()]);
        }
        if (isset($request->minsalery, $request->maxsalery)) {
            $jobs->where(function ($query) use ($request) {
                if (isset($request->minsalery, $request->maxsalery)) {
                    $query->where('salary_mode', 'range');
                    $query->whereBetween('min_salary', [$request->minsalery, $request->maxsalery])
                        ->orWhere(function ($query) use ($request) {
                            $query->whereBetween('max_salary', [$request->maxsalery, $request->maxsalery]);
                        });
                }
                $query->orWhere('salary_mode', '!=', 'range');
            });
        }
        if (! empty($daterange)) {
            $jobs->whereBetween('created_at', [$daterange, Carbon::now()]);
        }

        if (! empty($sortedBy)) {
            $jobs->orderBy('id', $sortedBy);
        }
        if (! empty($job_title)) {
            $jobs->when($job_title, function ($q) use ($job_title) {
                return $q->where('job_title', 'LIKE', "%{$job_title}%");
            });
        }
        if (! empty($company_name)) {
            $jobs->withWhereHas('company', function ($query) use ($company_name) {
                return $query->where('slug', $company_name);
            });
        }

        $jobs = $jobs->withWhereHas('company', function ($query) {
            $query->withWhereHas('user', function ($query) {
                $query->where('status', 1);
                $query->where('is_email_verified', 1);
            });
            $query->where('status', 1);
            $query->select('id', 'company_name', 'slug', 'status');
        })
            ->where('job_deadline', '>=', Carbon::now())
            ->where($options)
            ->select('id', 'job_title', 'slug', 'company_id', 'job_deadline', 'job_vacancy', 'job_image', 'salary_mode', 'min_salary', 'max_salary', 'salary_type')
            ->paginate(14);

        if ($request->ajax()) {
            $jobs_view = view('front.pages.jobs.job-section', compact('jobs', 'job_style', 'param'))->render();

            return response()->json([
                'status' => true,
                'jobs' => $jobs_view,
                'total' => $jobs->total(),
                'first_item' => $jobs->firstItem(),
                'last_item' => $jobs->lastItem()
            ]);
        }

        return view('front.pages.jobs.job-lists', compact('jobs', 'job_style', 'param'));
    }

    /**====== jobb details ==========
     *
     * @param
     * @return
     */
    public function jobDetails($slug)
    {
        try {
            $singleJob = Job::with('jobJobTypes', 'experiences', 'education', 'bookMarkJob', 'jobCustomFields')
                ->with(['company' => function ($query) {
                    $query->select('id', 'company_name', 'slug', 'company_website', 'address', 'iframe_link');
                    $query->with('galleries');
                }])
                ->category()
                ->countryCity()
                ->where('slug', $slug)
                ->where('status', 1)
                ->firstOrFail();
            $singleJob->view_job += 1;
            $singleJob->update();

            return view('front.pages.jobs.single-job', compact('singleJob'));
        } catch (\Throwable $th) {
            return view('front.pages.404');
        }
    }

    /** Display company job list
     *=========== companyJobList ============
     *
     * @param Request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $allJobList = $this->job->jobSearch($request);

        return view('front.pages.company-dashboard.job-list', compact('allJobList'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function jobCreate()
    {
        return view('front.pages.jobs.create');
    }

    /** new  resource create form
     *
     */
    public function jobInformation(JobInformationRequest $request)
    {
        if (isset(Request()->id) || isset(Request()->job_id)) {
            $result = $this->job->jobPrimaryInformation($request);
            if ($result['status'] !== true) {
                return response()->json(['status' => false, 'message' => $result['message']]);
            }
            if ($result['prefix'] !== 'additional-filed') {
                return response()->json([
                    'status' => true,
                    'message' => $result['message'],
                    'jobId' => $result['jobId'],
                    'prefix' => $result['prefix']]);
            }
            $job = $this->job->getById($result['jobId']);
                $types = $job->jobJobTypes;
                $jobLevels = $job->jobLevels;
                $jobExperiences = $job->experiences;
                $jobRole = $job?->jobRole?->select('id', 'name')->first();
                $skills = $job->skills;
                $previewContent = view('front.pages.jobs.preview-job', compact('job', 'types', 'jobLevels', 'jobExperiences', 'jobRole', 'skills'))->render();

                return response()->json([
                    'status' => true,
                    'message' => $result['message'],
                    'jobId' => $result['jobId'],
                    'prefix' => $result['prefix'],
                    'preview' => $previewContent]);
        } else {
           $subcriptions= subcriptionCheck() ;
            if (! $subcriptions) {
                return response()->json([
                    'status' => false,
                    'message' => translate('You have not Subscription Plan, Please Update Plan')
                ]);
            }
            if ($subcriptions?->avilable_qty  == 0) {
                return response()->json([
                    'status' => false,
                    'message' => translate('You Subscripton Plan has been finished, Please Update Plan')
                ]);
            }

            $result = $this->job->jobPrimaryInformation($request);
            if ($result['status'] !== true) {
                return response()->json([
                    'status' => false,
                    'message' => $result['message']
                ]);
            }

            if ($result['prefix'] !== 'additional-filed') {
                return response()->json([
                    'status' => true,
                    'message' => $result['message'],
                    'jobId' => $result['jobId'],
                    'prefix' => $result['prefix']
                ]);
            }
            $job = $this->job->getById($result['jobId']);
            $types = $job->jobJobTypes;
            $jobLevels = $job->jobLevels;
            $jobExperiences = $job->experiences;
            $jobRole = $job?->jobRole?->select('id', 'name')->first();
            $skills = $job->skills;
            $previewContent = view('front.pages.jobs.preview-job', compact('job', 'types', 'jobLevels', 'jobExperiences', 'jobRole', 'skills'))->render();

            return response()->json([
                'status' => true,
                'message' => $result['message'],
                'jobId' => $result['jobId'],
                'prefix' => $result['prefix'],
                'preview' => $previewContent]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function jobEdit($id)
    {
        try {
            $job = $this->job->show($id);
            $types = $job->jobJobTypes;
            $jobLevels = $job->jobLevels;
            $jobExperiences = $job->experiences;
            $jobRole = $job?->jobRole?->select('id', 'name')->first();
            $skills = $job->skills;
            return view('front.pages.jobs.edit', compact('job', 'types', 'jobLevels', 'jobExperiences', 'jobRole', 'skills'));
        } catch (\Throwable $th) {
            return view('front.pages.404');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function jobShow($id)
    {
        $jobInfo = $this->job->show($id);

        return view('front.pages.jobs.show', compact('jobInfo'));
    }

    /** specified resource status update .
     * ===========  jobStatusUpdate =========
     *
     * @param  int  $id
     * @return Response
     */
    public function jobStatusUpdate($id)
    {
        $result = $this->job->statusChange($id);
        if ($result['status'] == false) {
            return response()->json([
                'status' => false,
                'message' => $result['message']
            ]);
        } else {
            return response()->json([
                'status' => true,
                'message' => $result['message']
            ]);
        }
    }

    /**
     * skillSearch
     *
     * @param  mixed  $request
     * @return void
     */
    public function skillSearch(Request $request)
    {

        if ($request->q && $request->q != '') {
            $job = JobSkill::where('name', 'like', "%{$request->q}%")->select('id', 'name')->get();
            return response()->json(['items' => $job]);
        }

        return response()->json(Request()->q);
    }

    /** custom field delete by job id
     *
     *======= customFieldDelete ========
     *
     * @param int id
     * @return Response
     */
    public function customFieldDelete($id)
    {
        $result = $this->job->customFieldDelete($id);

        if ($result['status'] !== true) {
            return response()->json([
                'status' => false,
                'message' => $result['message']
            ]);
        }
        return response()->json([
            'status' => true,
            'message' => $result['message']
        ]);


    }
}
