@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Mulish:ital,wght@0,200..1000;1,200..1000&display=swap");

:root {
  --font-inter: "Inter", sans-serif;
  --font-mulish: "Mulish", sans-serif;
  --white-color: #ffffff;
  --primary-color1: #022c1f;
  --primary-color1-opc: 2, 44, 31;
  --primary-color2: #059669;
  --primary-color2-opc: 5, 150, 105;
  --primary-color3: #061421;
  --title-color: #1f1f1f;
  --title-color2: #022c1f;
  --border-color: #eeeeee;
  --paragraph-color: #585858;
}

/*================================================
02. Mixins Css
=================================================*/

@mixin eighteen-down-device {
  @media (max-width: 1799px) {
    @content;
  }
}

@mixin seventeen-down-device {
  @media (max-width: 1699px) {
    @content;
  }
}

@mixin fifteen-down-device {
  @media (max-width: 1499px) {
    @content;
  }
}

@mixin xxl-down-device {
  @media (max-width: 1399px) {
    @content;
  }
}

@mixin xl-down-device {
  @media (max-width: 1199px) {
    @content;
  }
}

@mixin xxl-device {
  @media (min-width: 1400px) and (max-width: 1599px) {
    @content;
  }
}

@mixin xl-device {
  @media (min-width: 1200px) and (max-width: 1399px) {
    @content;
  }
}

@mixin lg-device {
  @media (min-width: 992px) and (max-width: 1199px) {
    @content;
  }
}

@mixin xl-up-device {
  @media (min-width: 1200px) {
    @content;
  }
}

@mixin lg-up-device {
  @media (min-width: 992px) {
    @content;
  }
}

@mixin lg-down-device {
  @media (max-width: 991px) {
    @content;
  }
}

// md-device============
@mixin md-device {
  @media (min-width: 768px) and (max-width: 991px) {
    @content;
  }
}

@mixin xxl-up-device {
  @media (min-width: 1600px) {
    @content;
  }
}

@mixin md-up-device {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin md-down-device {
  @media (max-width: 767px) {
    @content;
  }
}

// sm-device
@mixin sm-device {
  @media (min-width: 576px) and (max-width: 768px) {
    @content;
  }
}

@mixin sm-down-device {
  @media (max-width: 576px) {
    @content;
  }
}

@mixin sm-mobile-device {
  @media (max-width: 425px) {
    @content;
  }
}

@mixin big-mobile-device {
  @media (min-width: 375px) and (max-width: 576px) {
    @content;
  }
}

@mixin threefifty-down-device() {
  @media (max-width: 350px) {
    @content;
  }
}

/*================================================
  03. Global Css
  =================================================*/

html {
  font-size: 100%;
  scroll-behavior: smooth;
}

input {
  border: none;
  outline: none;
}

button {
  outline: none;
  border: none;
}

i.bx {
  vertical-align: middle;
}

img {
  max-width: 100%;
  height: auto;
}

a {
  text-decoration: none;
}

p {
  font-family: var(--font-inter);
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  color: var(--paragraph-color);
}

.pt-120 {
  padding-top: 120px;

  @include lg-device() {
    padding-top: 100px;
  }

  @include lg-down-device() {
    padding-top: 90px;
  }
}

.pb-120 {
  padding-bottom: 120px;

  @include lg-device() {
    padding-bottom: 100px;
  }

  @include lg-down-device() {
    padding-bottom: 90px;
  }
}

.pt-100 {
  padding-top: 100px;

  @include lg-down-device() {
    padding-top: 80px;
  }
}

.pb-100 {
  padding-bottom: 100px;

  @include lg-down-device() {
    padding-bottom: 80px;
  }
}

.pt-90 {
  padding-top: 90px;

  @include lg-down-device() {
    padding-top: 80px;
  }

  @include md-down-device() {
    padding-top: 70px;
  }
}

.pb-90 {
  padding-bottom: 90px;

  @include lg-down-device() {
    padding-bottom: 80px;
  }

  @include md-down-device() {
    padding-bottom: 70px;
  }
}

.pb-80 {
  padding-bottom: 80px;

  @include lg-device() {
    padding-bottom: 60px;
  }
}

.pb-65 {
  padding-bottom: 65px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-10 {
  padding-bottom: 10px;
}

.mt-120 {
  margin-top: 120px;

  @include lg-device() {
    margin-top: 100px;
  }

  @include lg-down-device() {
    margin-top: 90px;
  }
}

.mb-120 {
  margin-bottom: 120px;

  @include lg-device() {
    margin-bottom: 100px;
  }

  @include lg-down-device() {
    margin-bottom: 90px;
  }
  @include md-down-device() {
    margin-bottom: 70px;
  }
}

.mb-100 {
  margin-bottom: 100px;

  @include lg-down-device() {
    margin-bottom: 80px;
  }
}

.mt-100 {
  margin-top: 100px !important;

  @include lg-down-device() {
    margin-top: 80px !important;
  }
}

.mb-90 {
  margin-bottom: 90px;

  @include lg-down-device() {
    margin-bottom: 70px;
  }
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-10 {
  margin-bottom: 10px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-110 {
  padding-top: 110px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pe-80 {
  padding-right: 80px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pl-110 {
  padding-left: 110px;

  @include xxl-device() {
    padding-left: 70px;
  }

  @include xl-device() {
    padding-left: 40px;
  }

  @include xl-down-device() {
    padding-left: unset;
  }
}

.mb-60 {
  margin-bottom: 60px;
  @include lg-down-device() {
    margin-bottom: 50px;
  }
  @include md-down-device() {
    margin-bottom: 40px;
  }
}

.mb-70 {
  margin-bottom: 70px;
  @include lg-down-device(){
    margin-bottom: 50px;
  }
  @include md-down-device(){
    margin-bottom: 40px;
  }
}

.mb-80 {
  margin-bottom: 80px;
  @include lg-down-device() {
    margin-bottom: 70px;
  }
  @include md-down-device() {
    margin-bottom: 60px;
  }
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-44 {
  margin-bottom: 44px;

  @include lg-down-device() {
    margin-bottom: 0px;
  }
}

.mb-35 {
  margin-bottom: 35px;
  @include md-down-device() {
    margin-bottom: 30px;
  }
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-35 {
  margin-top: 35px;
}

.mb-40 {
  margin-bottom: 40px;
  @include md-down-device(){
    margin-bottom: 30px;
  }
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mt-60 {
  margin-top: 60px;

  @include md-down-device() {
    margin-top: 40px;
  }
}

.mt-70 {
  margin-top: 70px;

  @include md-down-device() {
    margin-top: 40px;
  }
}

body {
  background: #fbfbfb;
}

.primary-btn-1 {
  font-family: var(--font-inter);
  display: inline-flex;
  align-items: center;
  border-radius: 5px;
  background: transparent;
  border: 1px solid var(--primary-color2);
  position: relative;
  transition: 0.5s all ease;
  z-index: 1;
  padding: 9px 16px;
  color: var(--primary-color2);
  svg {
    fill: var(--primary-color2);
    margin-right: 8px;
  }
  &::before {
    transition: 0.5s all ease;
    position: absolute;
    border-radius: 5px;
    top: 0;
    left: 50%;
    right: 50%;
    bottom: 0;
    opacity: 0;
    content: "";
    background-color: var(--primary-color2);
    z-index: -1;
  }
  &:hover {
    color: var(--white-color);
    svg {
      fill: var(--white-color);
    }
    &:before {
      transition: 0.5s all ease;
      left: 0;
      right: 0;
      opacity: 1;
    }
  }
}
.primary-btn-2 {
  font-family: var(--font-inter);
  display: inline-flex;
  align-items: center;
  border-radius: 5px;
  background-color: var(--primary-color2);
  white-space: nowrap;
  position: relative;
  transition: 0.5s all ease;
  z-index: 1;
  padding: 10px 21px;
  color: var(--white-color);
  svg {
    fill: var(--white-color);
    margin-right: 8px;
  }
  &::before {
    transition: 0.5s all ease;
    position: absolute;
    border-radius: 5px;
    top: 0;
    left: 50%;
    right: 50%;
    bottom: 0;
    opacity: 0;
    content: "";
    background-color: var(--title-color);
    z-index: -1;
  }
  &:hover {
    color: var(--white-color);
    svg {
      fill: var(--white-color);
    }
    &:before {
      transition: 0.5s all ease;
      left: 0;
      right: 0;
      opacity: 1;
    }
  }
}
.primary-btn-3 {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 16px;
  line-height: 1;
  color: var(--primary-color2);
  white-space: nowrap;
  position: relative;
  display: inline-block;
  background: linear-gradient(
    to bottom,
    var(--primary-color2) 0%,
    var(--primary-color2) 98%
  );
  background-repeat: no-repeat;
  background-size: 100% 1px;
  background-position: left 100%;
  transition: background-size 0.75s;
  padding-bottom: 1px;

  &:hover {
    background-size: 0 1px;
    background-position: 0% 100%;
  }
}
@keyframes fade-up {
  0% {
    opacity: 0;
    transform: translateY(10px);
    visibility: hidden;
  }

  100% {
    opacity: 1;
    transform: translateY(0px);
    visibility: visible;
  }
}

.primary-btn-4 {
  font-family: var(--font-inter);
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: var(--title-color2);
  transition: 0.35s;
  svg {
    fill: var(--title-color2);
  }
  &:hover {
    color: var(--primary-color2);
    svg {
      fill: var(--primary-color2);
    }
  }
}

// section title
.section-title {
  h3 {
    font-family: var(--font-mulish);
    font-weight: 700;
    font-size: 34px;
    line-height: 1.2;
    color: var(--title-color2);
    margin-bottom: 0;
    @include sm-down-device(){
      font-size: 28px;
    }
  }
}

.slider-btn-grp {
  display: flex;
  align-items: center;
  gap: 50px;
  .slider-btn {
    min-width: 30px;
    max-width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    background-color: var(--white-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.5s;
    svg {
      fill: none;
      stroke: var(--title-color2);
      transition: 0.5s;
    }
    &:hover {
      background-color: var(--primary-color2);
      border-color: var(--primary-color2);
      svg {
        stroke: var(--white-color);
      }
    }
    &.swiper-button-disabled {
      opacity: 0.2;
    }
  }
  &.two {
    .slider-btn {
      min-width: 35px;
      max-width: 35px;
      height: 35px;
      svg {
        stroke: var(--primary-color2);
      }
      &:hover {
        background-color: var(--primary-color2);
        border-color: var(--primary-color2);
        svg {
          stroke: var(--white-color);
        }
      }
    }
  }
}
/*================================================
03. topbar area
=================================================*/
.topbar {
  background: #fcf2b1;
  padding: 18px 12%;
  @include seventeen-down-device() {
    padding: 18px 5%;
  }
  @include xxl-down-device() {
    padding: 15px 4%;
  }
  @include xl-down-device() {
    padding: 15px 3%;
  }
  @include md-down-device() {
    display: none;
  }
  .topbar-left {
    p {
      font-family: var(--font-inter);
      font-weight: 500;
      font-size: 14px;
      line-height: 1;
      color: #411c06;
      margin-bottom: 0;
      display: flex;
      align-items: center;
      gap: 9px;
    }
  }
  .topbar-right {
    display: flex;
    align-items: center;
    gap: 37px;
    .language-area {
      position: relative;
      cursor: pointer;

      a {
        font-family: var(--font-inter);
        font-size: 14px;
        font-weight: 500;
        line-height: 1;
        text-align: left;
        color: #411c06;
      }

      .lang-bar {
        position: relative;

        .lang-btn {
          font-family: var(--font-inter);
          font-size: 14px;
          font-weight: 500;
          color: #411c06;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .lang-card {
          margin: 0;
          padding: 0;
          list-style: none;
          position: absolute;
          top: 40px;
          right: -2px;
          z-index: 999;
          width: 180px;
          background-color: var(--primary-color2);
          box-shadow: 0px 30px 80px rgba(8, 0, 42, 0.08);
          display: none;
          transform: scaleY(0);
          transform-origin: top;
          cursor: pointer;

          &.active {
            display: block;
            transform: scaleY(1);
            animation: fade-up 0.5s linear;
          }

          li {
            a {
              font-family: var(--font-inter);
              color: var(--white-color);
              font-weight: 500;
              text-transform: capitalize;
              font-size: 14px;
              padding: 10px 20px;
              width: 100%;
              display: flex;
              align-items: center;
              transition: 0.5s;

              &:hover {
                background-color: var(--primary-color3);
                color: var(--white-color);
              }
            }
          }
        }
      }
    }
    .social-area {
      position: relative;
      &::after {
        content: "";
        position: absolute;
        top: 53%;
        transform: translateY(-50%);
        left: -18px;
        width: 1px;
        height: 14px;
        background-color: #a6a6a6;

        @include xl-device() {
          left: -19px;
        }

        @include md-down-device() {
          left: -22px;
        }

        @include sm-down-device() {
          display: none;
        }
      }

      &:first-child {
        &::after {
          display: none;
        }
      }
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        li {
          a {
            height: 21px;
            width: 21px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--white-color);
            border-radius: 50%;
            transition: 0.35s;
            i {
              color: var(--primary-color2);
              transition: 0.35s;
              position: relative;
              left: 0;
              top: 0;
              margin-left: 0;
              font-size: 14px;
              &.bi-twitter-x{
                font-size: 11px;
              }
            }
            &:hover {
              background-color: var(--primary-color2);
              i {
                color: var(--white-color);
              }
            }
          }
        }
      }
    }
  }
}

/*================================================
04. header area
=================================================*/
header.header-area {
  position: relative;
  width: 100%;
  z-index: 9;
  display: flex;
  align-items: center;
  background-color: var(--white-color);
  @include lg-down-device() {
    padding: 8px 0;
  }
  @include md-down-device() {
    padding: 0;
  }
  &.sticky {
    position: fixed;
    top: 0px;
    left: 0;
    z-index: 99999;
    background-color: var(--white-color);
    border-bottom: 1px solid #f1f1f1;

    @keyframes smooth-header {
      0% {
        transform: translateY(-30px);
      }

      100% {
        transform: translateY(0px);
      }
    }
  }
 .menu-close-btn {
    height: 32px;
    width: 32px;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: 0.35s;

    i {
      font-size: 16px;
      line-height: 1;
      color: var(--white-color);
      transition: 0.35s;
    }

    &:hover {
      background-color: var(--white-color);
      i {
        color: var(--title-color);
      }
    }
  }
  .header-logo {
    @include lg-down-device() {
      padding: 20px 0;
    }
    a {
      img {
        width: 160px;
        @include xxl-down-device() {
          width: 140px;
        }
      }
    }
  }
  .mobile-logo-wrap {
    img {
      max-width: 120px;
    }
  }
  .menu-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 12%;
    width: 100%;
    @include seventeen-down-device() {
      padding: 0px 5%;
    }
    @include xxl-down-device() {
      padding: 0px 4%;
    }
    @include xl-down-device() {
      padding: 0px 3%;
    }
    @include sm-down-device(){
      padding: 0 10px;
    }
    .nav-right {
      gap: 24px;
      @include fifteen-down-device() {
        gap: 15px;
      }
      @include lg-down-device() {
        justify-content: end !important;
      }
      .lg-btn {
        @include xl-down-device() {
          padding: 9px 20px;
        }
      }
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: center;
        gap: 25px;
        @include xxl-down-device() {
          gap: 15px;
        }
      }
      .mobile-menu-btn {
        svg{
          fill: var(--title-color);
        }
        &:hover{
          svg{
            animation: qode-draw 0.75s cubic-bezier(0.57, 0.39, 0, 0.86) 1 forwards;
          }
        }
        display: none;
        visibility: hidden;
        @include lg-down-device() {
          display: block;
          visibility: visible;
        }
        @keyframes qode-draw {
          0%, 100% {
            -webkit-clip-path: inset(-2px -2px);
            clip-path: inset(-2px -2px);
          }
          42% {
            -webkit-clip-path: inset(-2px -2px -2px 100%);
            clip-path: inset(-2px -2px -2px 100%);
          }
          43% {
            -webkit-clip-path: inset(-2px 100% -3px -2px);
            clip-path: inset(-2px 100% -3px -2px);
          }
        }
      }
      .notifacion-card {
        top: 30px !important;
      }
    }
  }
  .main-menu {
    display: inline-block;
    .mobile-menu-logo {
      display: none;
    }
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
      > li {
        display: inline-block;
        position: relative;
        padding: 0 15px;
        @include lg-device() {
          padding: 0px 4px;
        }
        &:hover {
          i {
            color: var(--title-color);
            font-size: 20px;
          }
        }
        a {
          font-size: 16px;
          color: var(--primary-color3);
          font-weight: 500;
          display: block;
          text-transform: capitalize;
          padding: 31px 8px;
          position: relative;
          font-family: var(--font-inter);
          transition: all 0.5s ease-out 0s;
          position: relative;
          @include fifteen-down-device() {
            padding: 31px 7px;
          }
          @include xxl-down-device() {
            padding: 28px 5px;
          }
          @include xl-down-device() {
            padding: 25px 5px;
          }
          @include lg-device(){
            font-size: 15px;
          }
          @include lg-down-device() {
            padding: 25px 10px;
          }
        }

        i {
          font-size: 20px;
          text-align: center;
          color: var(--title-color);
          font-style: normal;
          position: absolute;
          right: -5px;
          top: 35px;
          z-index: 999;
          cursor: pointer;
          display: none;
          transition: all 0.5s ease-out 0s;
          opacity: 0;
          &.active {
            color: var(--title-color2);
            &::before {
              content: "\F2EA";
            }
          }
          @include lg-down-device() {
            opacity: 1;
          }
        }
        &:hover {
          > a {
            color: var(--primary-color2);
          }
        }
        &.active {
          > a {
            color: var(--primary-color2);
          }
        }
      }

      li.menu-item-has-children > i {
        display: block;
      }
    }
  }

  @media only screen and (max-width: 991px) {
    .main-menu {
      position: fixed;
      top: 0;
      left: 0;
      width: 280px;
      padding: 30px 20px !important;
      z-index: 99999;
      height: 100%;
      overflow: auto;
      background: var(--primary-color1);
      -webkit-transform: translateX(-260px);
      transform: translateX(-100%);
      -webkit-transition: -webkit-transform 0.3s ease-in;
      transition: -webkit-transform 0.3s ease-in;
      transition: transform 0.3s ease-in;
      transition: transform 0.3s ease-in, -webkit-transform 0.3s ease-in;
      box-shadow: 0px 2px 20px rgba(#000, 0.03);
      &.show-menu {
        transform: translateX(0);
      }
      .mobile-menu-logo {
        text-align: left;
        padding-top: 20px;
        display: block;
        padding-bottom: 8px;
      }

      ul {
        float: none;
        text-align: left;
        padding: 50px 10px 35px 0;

        li {
          display: block;
          position: relative;
          padding: 0 5px;

          i {
            display: block;
          }

          a {
            padding: 10px 0;
            display: block;
            font-weight: 500;
            font-size: 16px;
            color: var(--white-color);
          }
          .bi {
            top: 8px;
            font-size: 20px;
          }
        }
      }
    }

    .mobile-menu {
      position: relative;
      top: 2px;
      padding: 0 5px;
      border-radius: 50%;
      display: inline-block;
    }
    .social-area {
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        li {
          a {
            height: 30px;
            width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--primary-color2);
            border-radius: 50%;
            transition: 0.35s;
            i {
              color: var(--white-color);
              transition: 0.35s;
              position: relative;
              left: 0;
              top: 0;
              font-size: 15px;
              margin-left: 0;
            }
          }
          &:hover {
            i {
              color: var(--primary-color3);
            }
          }
        }
      }
    }
    .primary-btn-1 {
      justify-content: center;
      width: 100%;
    }
    .primary-btn-2 {
      justify-content: center;
      width: 100%;
    }
  }
}

/*================================================
05. banner wrapper
=================================================*/
.banner-wrapper {
  background: url(../image/banner-bg.jpg);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  overflow: hidden;
  position: relative;
  @include xl-down-device(){
    padding: 100px 0;
  }
  @include lg-down-device(){
    padding: 90px 0;
  }
  @include sm-down-device(){
    padding: 70px 0;
  }
  .banner-content {
    max-width: 672px;
    width: 100%;
    padding: 120px 0;
    @include xl-down-device(){
      max-width: 780px;
      padding: 0;
    }
    .banner-tag {
      font-family: var(--font-mulish);
      font-weight: 800;
      font-size: 14px;
      line-height: 1;
      color: var(--white-color);
      background: #bf3030;
      padding: 5px 10px;
      border-radius: 14px;
      display: inline-block;
      margin-bottom: 10px;
    }
    h1 {
      font-family: var(--font-mulish);
      font-weight: 300;
      font-style: italic;
      font-size: 68px;
      line-height: 1.2;
      color: var(--white-color);
      margin-bottom: 20px;
      @include xxl-device(){
        font-size: 65px;
      }
      @include xxl-down-device(){
        font-size: 58px;
      }
      @include lg-down-device() {
        font-size: 54px;
      }
      @include md-down-device() {
        font-size: 50px;
      }
      @include sm-down-device() {
        font-size: 43px;
        margin-bottom: 15px;
      }
      strong {
        font-weight: 700;
        font-style: normal;
        span{
          position: relative;
          display: inline-block;
          @include md-down-device(){
            display: inline;
          }
          &::before {
            content: "";
            position: absolute;
            bottom: -4px;
            right: 0;
            width: 100%;
            background: url(../image/vector/vector.png) no-repeat center center;
            height: 21px;
            @include md-down-device() {
              display: none;
            }
          }
        }
      }
    }
    p {
      font-family: var(--font-inter);
      font-weight: 400;
      font-size: 18px;
      line-height: 1.5;
      color: var(--white-color);
      margin-bottom: 15px;
      @include lg-down-device(){
        font-size: 17px;
      }
      @include md-down-device() {
        font-size: 16px;
      }
      @include sm-down-device(){
        font-size: 15px;
        line-height: 1.7;
      }
    }
    .job-search-area {
      background: #ffffff;
      border-radius: 10px;
      max-width: 880px;
      margin: 60px 0 20px;
      @include xxl-device(){
        margin: 50px 0 20px;
      }
      @include xxl-down-device(){
        margin: 50px 0 20px;
      }
      @include lg-down-device(){
        margin: 35px 0 20px;
      }
      @include md-down-device(){
        margin: 25px 0 10px;
      }
      form {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 26px 15px 20px;
        gap: 20px;
        @include xl-device(){
          padding: 15px 10px;
          gap: 10px;
        }
        @include lg-down-device(){
          padding: 10px 26px 10px 20px;
        }
        @include md-down-device() {
          flex-wrap: wrap;
          gap: 15px;
        }
        @include sm-down-device(){
          padding: 15px 20px;
        }
        .form-inner {
          @include sm-down-device(){
            width: 100%;
          }
          &.category {
            border-radius: 5px;
            padding-left: 60px;
            position: relative;
            width: 100%;
            max-width: 210px;
            @include xxl-down-device(){
              padding-left: 20px;
              max-width: 150px;
            }
            @include lg-down-device(){
              padding-left: 40px;
              max-width: 180px;
            }
            @include md-down-device() {
              max-width: 200px;
              padding-left: 30px;
            }
            @include sm-down-device(){
              padding-left: 0;
              max-width: unset;
            }
            &::before{
              content: '';
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              left: 0;
              width: 1px;
              height: 36px;
              background-color: #D9D9D9;
              @include sm-down-device(){
                display: none;
              }
            }
          }

          input {
            height: 30px;
            font-family: var(--font-inter);
            font-weight: 400;
            font-size: 15px;
            line-height: 1;
            color: var(--title-color);
            padding: 20px 20px 20px 0px;
            line-height: 1;
            width: 100%;
            border-bottom: none;
            @include xl-device(){
              padding: 20px 0;
            }
            @include sm-down-device(){
              padding: 15px 0;
              height: 27px;
            }
            &::placeholder {
              font-family: var(--font-inter);
              font-weight: 400;
              font-size: 15px;
              line-height: 18px;
              color: #a6a6a6;
            }
          }
          .nice-select {
            height: 30px;
            font-family: var(--font-inter);
            font-weight: 400;
            font-size: 16px;
            line-height: 18px;
            color: var(--title-color);
            width: 100%;
            display: flex;
            align-items: center;
            border: unset;
            padding-left: 0;
            @include xl-device(){
              font-size: 15px;
            }
            .option {
              min-height: 35px;
            }
            .list {
              width: 100%;
              li {
                font-family: var(--font-inter);
                font-weight: 400;
                font-size: 14px;
                line-height: 18px;
                color: var(--title-color);
                display: flex;
                align-items: center;
              }
            }
            &::after {
              border-bottom: 1px solid var(--primary-color3);
              border-right: 1px solid var(--primary-color3);
              margin-top: -6px;
              right: 5px;
              top: 50%;
              height: 8px;
              width: 8px;
            }
          }
          .nice-select .option:hover,
          .nice-select .option.focus,
          .nice-select .option.selected.focus {
            background-color: rgba(#00a7ac, 0.1);
          }
          .primry-btn-2 {
            height: 100%;
            padding: 12px 31px;
            img {
              margin-right: 5px;
            }
          }
          button{
            @include xl-device(){
              padding: 14px 33px;
            }
          }
        }
      }
    }
    .suggest-tag {
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: center;
        gap: 20px;
        @include md-down-device() {
          display: inline-flex;
          flex-wrap: wrap;
          gap: 10px;
        }
        li {
          a {
            font-family: var(--font-inter);
            font-weight: 500;
            font-size: 14px;
            line-height: 1;
            color: var(--white-color);
            transition: 0.35s;
            &:hover {
              color: #fcf2b1;
            }
          }
        }
      }
    }
  }
  .banner-img-wrap{
    min-width: 610px;
    position: relative;
    @include seventeen-down-device(){
      min-width: 555px;
    }
    @include xxl-device(){
      min-width: unset;
    }
    @include xxl-down-device(){
      min-width: unset;
    }
    .banner-img{
      display: flex;
      align-items: end;
      justify-content: end;
      @include xl-down-device(){
        display: none;
      }
      img{
        animation: fadeInRight 1s ease-out forwards;
        opacity: 0;
        animation-delay: 0.5s;
      }
      @keyframes fadeInRight {
        0% {
          opacity: 0;
          transform: translateX(100px);
        }
        100% {
          opacity: 1;
          transform: translateX(0);
        }
      }
    }
    .counter-item{
      padding: 0;
      margin: 0;
      list-style: none;
      @include xl-down-device(){
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 30px;
      }
      @include md-down-device(){
        gap: 15px;
      }
      @include sm-down-device(){
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        margin-top: 10px;
      }
      .single-counter{
        padding: 13px 25px;
        background-color: #FFFFFF;
        border-radius: 10px;
        text-align: center;
        position: absolute;
        top: 55px;
        left: 30%;
        span{
          color: var(--paragraph-color);
          font-family: var(--font-inter);
          font-weight: 400;
          font-size: 14px;
          line-height: 1;
          padding: 10px 0;
          display: block;
        }
        h6{
          color: var(--title-color);
          font-family: var(--font-inter);
          font-weight: 700;
          font-size: 16px;
          line-height: 1;
          margin-bottom: 0;
          strong{
            font-weight: 700;
          }
        }
        &:nth-child(2){
          top: 20%;
          left: 5px;
          @include xl-down-device(){
            top: unset;
            left: unset;
          }
        }
        &:nth-child(3){
          top: unset;
          bottom: 13%;
          left: 5px;
          @include xl-down-device(){
            bottom: unset;
            left: unset;
          }
        }
        &:nth-child(4){
          top: unset;
          bottom: 35px;
          left: 45%;
          @include xl-down-device(){
            bottom: unset;
            left: unset;
          }
        }
        @include xl-down-device(){
          position: relative;
          top: unset;
          left: unset;
        }
        @include sm-down-device(){
          width: 100%;
        }
      }
    }
  }
  .vector{
    position: absolute;
    bottom: 0;
    right: 0;
    height: 504px;
    object-fit: cover;
    @include seventeen-down-device(){
      width: 230px;
    }
    @include xxl-device(){
      width: 180px;
      height: 385px;
    }
    @include xxl-down-device(){
      width: 180px;
      height: 385px;
    }
    @include xl-down-device(){
      display: none;
    }
  }
}

/*================================================
06. category section
=================================================*/
.category-section {
  .single-category {
    position: relative;
    .category-wrap {
      mask-image: url(../image/category-bg.png);
      mask-size: cover;
      mask-repeat: no-repeat;
      height: 100%;
      width: 100%;
      mask-position: right;
      border-radius: 20px;
      position: relative;
      z-index: 1;
      padding: 27px 10px;
      border-radius: 10px;
      background: #EEF8F9;
      transition: 0.35s;
      .category-icon {
        margin-bottom: 18px;
      }
      .category-content {
        h5 {
          a {
            font-family: var(--font-inter);
            font-weight: 500;
            font-size: 20px;
            line-height: 1;
            text-align: center;
            color: var(--title-color2);
            margin-bottom: 13px;
            transition: 0.5s;
            @include xxl-down-device(){
              font-size: 18px;
            }
            &:hover{
              color: var(--primary-color2);
            }
          }
        }
        p {
          font-family: var(--font-inter);
          font-weight: 400;
          font-size: 14px;
          line-height: 1;
          color: var(--paragraph-color);
          margin-bottom: 0;
          span {
            font-weight: 600;
            color: var(--title-color2);
          }
        }
      }
    }
    .button-area {
      .icon {
        position: absolute;
        top: 80%;
        right: 2px;
        z-index: 999;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 1px solid var(--primary-color2);
        transition: 0.5s;
        svg {
          fill: none;
          stroke: var(--primary-color2);
          transition: 0.5s;
        }
      }
    }
    &:hover {
      .category-wrap {
        background: #e7f8f2;
      }
      .button-area {
        .icon {
          background: var(--primary-color2);
          border: 1px solid var(--primary-color2);
          svg {
            stroke: var(--white-color);
          }
        }
      }
    }
  }
}

/*================================================
07.  trusted company
=================================================*/
.trusted-company {
  .section-title {
    margin-bottom: 20px;
    h6 {
      font-family: var(--font-mulish);
      font-weight: 600;
      font-size: 18px;
      line-height: 1;
      color: var(--title-color2);
      margin-bottom: 0;
      position: relative;
      display: inline-block;
      &::after {
        content: "";
        width: 114px;
        height: 1px;
        background-color: rgba(var(--primary-color2-opc), 0.5);
        border-radius: 5px;
        position: absolute;
        top: 55%;
        right: -128px;
        transform: translateY(-50%);
        @include sm-down-device(){
          width: 60px;
          right: -70px;
        }
      }
    }
  }
  .marquee{
    display: flex;
    gap: 80px;
    overflow: hidden;
    user-select: none;
    @include xl-down-device(){
      gap: 40px;
    }
    @include sm-down-device(){
      gap: 30px;
    }
    .marquee__group{
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: space-around;
      gap: 80px;
      min-width: 100%;
      animation: scroll-x 30s linear infinite;
      @include xl-down-device(){
        gap: 40px;
      }
      @include sm-down-device(){
        gap: 30px;
      }
      a{
        img{
          width: 160px;
          @include xl-down-device(){
            width: 145px;
          }
        }
      }
    }
    @keyframes scroll-x {
      from {
        transform: translateX(0);
      }
      to {
        transform: translateX(-100%);
      }
    }
  }
}

/*================================================
08. Rescuiters section
=================================================*/
.rescuiters-section {
  padding: 0 30px;
  @include md-down-device() {
    padding: 0px 20px;
  }
  @include sm-down-device() {
    padding: 0px 10px;
  }
  .rescuiters-section-wrap {
    background: #f2f2f2;
    padding: 75px 20px;
    border-radius: 30px;
    @include lg-down-device() {
      padding: 60px 10px;
    }
    @include md-down-device() {
      padding: 50px 10px;
    }
    @include sm-down-device() {
      padding: 50px 5px;
      border-radius: 20px;
    }
  }
}
.rescuiters-card {
  background: var(--white-color);
  border-radius: 20px;
  padding: 0 25px;
  transition: 0.5s;
  border: 1px solid var(--border-color);
  &:hover {
    border: 1px solid var(--primary-color2);
  }
  @include xxl-down-device(){
    padding: 0 20px;
  }
  @include lg-down-device(){
    padding: 0 15px;
  }
  @include sm-down-device(){
    border-radius: 15px;
  }
  .company-area {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 25px 0px;
    margin-bottom: 25px;
    border-bottom: 1px solid var(--border-color);
    @include lg-down-device(){
      gap: 10px;
    }
    .icon{
      img{
        @include sm-down-device(){
          width: 60px;
        }
      }
    }
    .name-location {
      h5 {
        margin-bottom: 10px;
        a {
          font-family: var(--font-inter);
          font-weight: 600;
          font-size: 20px;
          line-height: 1;
          color: var(--primary-color2);
          transition: 0.5s;
          &:hover{
            color: var(--title-color);
          }
          @include lg-down-device(){
            font-size: 19px;
          }
          @include sm-down-device(){
            font-size: 18px;
          }
        }
      }
      .info{
        padding: 0;
        margin: 0;
        list-style: none;
        li{
          font-family: var(--font-inter);
          font-weight: 400;
          font-size: 15px;
          line-height: 24px;
          color: var(--paragraph-color);
          margin-bottom: 0;
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;
          svg {
            fill: var(--paragraph-color);
          }
          &:last-child{
            margin-bottom: 0;
          }
          @include sm-down-device(){
            font-size: 14px;
            gap: 5px;
          }
        }
      }
    }
  }
  .job-details-vacancies {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    padding: 0px 0px 30px;
    .vacancies {
      p {
        font-family: var(--font-inter);
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        color: var(--paragraph-color);
        margin-bottom: 0;
        span {
          font-weight: 500;
          color: var(--title-color2);
        }
      }
    }
  }
}
/*================================================
09. feature section 
=================================================*/
.feature-card {
  position: relative;
  .feature-wrape {
    mask-image: url(../image/featured-bg.png);
    mask-size: cover;
    mask-repeat: no-repeat;
    height: 100%;
    width: 100%;
    mask-position: right;
    border-radius: 20px;
    position: relative;
    z-index: 1;
    padding: 30px 30px;
    border-radius: 20px;
    background: var(--white-color);
    border: 1px solid transparent;
    transition: 0.35s;
    @include xl-device(){
      padding: 25px 20px;
    }
    @include xl-down-device(){
      mask-position: right bottom;
    }
    @include lg-down-device(){
      padding: 25px 18px;
    }
    .company-area {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 30px;
      @include xxl-down-device(){
        margin-bottom: 25px;
      }
      @include lg-down-device(){
        gap: 10px;
      }
      .company-details {
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap: 12px;
        .name-location {
          h5 {
            font-family: var(--font-inter);
            font-weight: 600;
            font-size: 20px;
            line-height: 1.2;
            color: var(--primary-color2);
            margin-bottom: 7px;
            @include lg-down-device(){
              font-size: 18px;
            }
          }
          p {
            font-family: var(--font-inter);
            font-weight: 400;
            font-size: 16px;
            line-height: 1.5;
            color: var(--paragraph-color);
            margin-bottom: 0;
            @include lg-down-device(){
              font-size: 15px;
            }
            @include sm-down-device(){
              font-size: 14px;
            }
            span {
              color: var(--title-color2);
              font-weight: 500;
            }
          }
        }
        .bookmark {
          cursor: pointer;
          i {
            color: var(--paragraph-color);
            transition: 0.5s;
            &:hover {
              color: #ee4f4f;
              &::before{
                content: "\F415";
              }
            }
          }
        }
      }
    }
    .job-discription {
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: inline-flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 14px;
        margin-bottom: 35px;
        @include lg-down-device(){
          margin-bottom: 30px;
        }
        @include sm-down-device(){
          gap: 8px;
          row-gap: 12px;
        }
        li {
          background: #faf4db;
          padding: 5px 15px;
          border-radius: 5px;
          display: flex;
          gap: 4px;
          align-items: center;
          font-family: var(--font-inter);
          font-weight: 500;
          font-size: 14px;
          line-height: 20px;
          color: #8e5e0f;
          text-align: center;
          @include xl-device(){
            font-size: 13px;
          }
          @include md-device(){
            font-size: 12px;
          }
          @include sm-down-device(){
            font-size: 12px;
            padding: 5px 10px;
          }
          &.style-2 {
            background: #e4f7ff;
            color: #0e658c;
            svg {
              fill: #0e658c;
            }
          }
          &.style-3 {
            background: #fff0f5;
            color: #6d1e38;
          }
          &.style-4 {
            background: #edfedd;
            color: #243f0b;
          }

          &.style-5 {
            background: #f3edfb;
            color: #322052;
          }
        }
      }
    }
    .date {
      p {
        font-family: var(--font-inter);
        font-weight: 500;
        font-size: 15px;
        line-height: 1;
        color: var(--paragraph-color);
        margin-bottom: 0;
        @include sm-down-device(){
          font-size: 13px;
        }
      }
    }
  }
  .button-area {
    position: absolute;
    top: 84%;
    right: -5px;
    z-index: 99;
    @include lg-down-device(){
      right: 0;
    }
    .primary-btn-4{
      @include lg-down-device(){
        font-size: 15px;
      }
      @include sm-down-device(){
        font-size: 14px;
      }
    }
  }
  &.two{
    .feature-wrape{
      background-color: #F4F4F4;
    }
  }
}
.featured-section {
  padding: 0 30px;
  @include xxl-down-device(){
    padding: 0 20px;
  }
  @include md-down-device(){
    padding: 0 10px;
  }
  .featured-section-wrap {
    background: #f2f2f2;
    padding: 75px 0;
    border-radius: 30px;
    @include md-down-device(){
      padding: 60px 0;
      border-radius: 20px;
    }
    @include sm-down-device(){
      padding: 50px 0;
    }
  }
}

/*================================================
10. feature section 
=================================================*/
.testimonial-card2 {
  .testimonial-img {
    background-color: #f0f0f0;
    padding: 15px;
    border-radius: 20px;
    height: 100%;
    @include lg-device() {
      padding: 10px;
    }
    @include sm-down-device() {
      padding: 10px;
      border-radius: 15px;
      height: unset;
    }
    img {
      border-radius: 10px;
      height: 100%;
      object-fit: cover;
      @include sm-down-device() {
        height: unset;
      }
    }
  }
  .testimonial-content-wrap {
    background-image: url(../image/testimonial-bg.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    padding: 55px 65px;
    border-radius: 20px;
    position: relative;
    @include xxl-down-device() {
      padding: 55px 40px;
      background-position: right;
    }
    @include lg-device() {
      padding: 45px 25px;
    }
    @include lg-down-device() {
      padding: 45px 28px;
    }
    @include sm-down-device() {
      padding: 35px 15px;
      border-radius: 15px;
    }
    .company-logo {
      margin-bottom: 40px;
      @include lg-device() {
        margin-bottom: 30px;
      }
      @include md-down-device() {
        margin-bottom: 30px;
      }
      img {
        width: 144px;
        &.dark {
          display: none;
        }
      }
    }
    .testimonial-content {
      margin-bottom: 45px;
      @include lg-device() {
        margin-bottom: 30px;
      }
      @include md-down-device() {
        margin-bottom: 30px;
      }
      @include sm-down-device() {
        margin-bottom: 25px;
      }
      .rating-area {
        padding: 0;
        margin: 0;
        list-style: none;
        display: flex;
        align-items: center;
        gap: 5px;
        line-height: 1;
        margin-bottom: 15px;
        @include sm-down-device() {
          margin-bottom: 10px;
        }
        li {
          i {
            color: #e4c40c;
            font-size: 14px;
          }
        }
      }
      p {
        color: var(--paragraph-color);
        font-family: var(--font-mulish);
        font-size: 28px;
        font-weight: 500;
        line-height: 1.3;
        margin-bottom: 0;
        @include xxl-down-device() {
          font-size: 25px;
        }
        @include lg-device() {
          font-size: 22px;
          line-height: 1.4;
        }
        @include lg-down-device() {
          font-size: 24px;
          line-height: 1.4;
        }
        @include sm-down-device() {
          font-size: 18px;
          line-height: 1.7;
        }
        span {
          color: var(--title-color);
        }
      }
    }
    .author-area {
      max-width: 270px;
      width: 100%;
      @include sm-down-device() {
        max-width: unset;
      }
      h5 {
        color: var(--title-color2);
        font-family: var(--font-mulish);
        font-size: 22px;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 5px;
      }
      span {
        color: var(--paragraph-color);
        font-family: var(--font-inter);
        font-size: 14px;
        font-weight: 500;
        line-height: 1;
      }
    }
    .quote {
      fill: var(--white-color);
      stroke: var(--border-color);
      position: absolute;
      top: 60px;
      right: 110px;
      @include lg-device() {
        top: 40px;
        right: 80px;
      }
      @include lg-down-device() {
        top: 40px;
      }
      @include md-down-device() {
        right: 60px;
      }
      @include sm-down-device() {
        top: 30px;
        right: 30px;
        width: 80px;
      }
    }
    .joint1 {
      fill: #f0f0f0;
      position: absolute;
      top: 80px;
      left: -24px;
      @include lg-down-device() {
        display: none;
      }
    }
    .joint2 {
      fill: #f0f0f0;
      position: absolute;
      bottom: 80px;
      left: -24px;
      @include lg-down-device() {
        display: none;
      }
    }
  }
  &.two {
    .testimonial-img {
      padding: 0;
      position: relative;
      border-radius: 5px;
      img {
        border-radius: 5px;
      }
    }
    .testimonial-content-wrap {
      background-image: unset;
      background-color: rgba(#f0f0f0, 0.8);
      border-radius: 10px;
      padding: 35px 30px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 25px;
      min-height: 396px;
      @include lg-device() {
        padding: 25px 18px;
        min-height: 348px;
      }
      @include lg-down-device() {
        min-height: 348px;
      }
      @include md-down-device() {
        min-height: 315px;
      }
      @include sm-down-device() {
        padding: 25px 15px;
        gap: 15px;
        min-height: 306px;
      }
      .testimonial-content {
        margin-bottom: 0;
        .testimonial-content-top {
          margin-bottom: 15px;
          @include sm-down-device() {
            margin-bottom: 10px;
          }
          .rating-area {
            margin-bottom: 2px;
            li {
              i {
                color: #dda701;
              }
            }
          }
          span {
            color: var(--title-color);
            font-family: var(--font-inter);
            font-size: 16px;
            font-weight: 600;
            line-height: 1;
          }
        }
      }
      .author-area {
        h5 {
          margin-bottom: 0;
        }
      }
      .quote {
        top: unset;
        bottom: 45px;
        right: 30px;
        @include sm-down-device() {
          bottom: 40px;
          right: 25px;
        }
      }
      .joint1,
      .joint2 {
        @include lg-down-device() {
          display: block;
        }
        @include md-down-device() {
          display: none;
        }
      }
    }
  }
}
.testimonial-section {
  .testimonial-slider-area {
    position: relative;
    .slider-btn-area {
      display: flex;
      align-items: center;
      gap: 30px;
      justify-content: end;
      position: absolute;
      bottom: 72px;
      right: 65px;
      z-index: 1;
      @include xxl-down-device() {
        right: 40px;
      }
      @include lg-device() {
        right: 25px;
        bottom: 62px;
      }
      @include lg-down-device() {
        right: 28px;
        bottom: 62px;
      }
      @include sm-down-device() {
        position: relative;
        margin-top: 20px;
        bottom: unset;
        right: unset;
        justify-content: space-between;
      }
      .slider-btn-grp {
        gap: 15px;
      }
      .franctional-pagi {
        color: var(--title-color2);
        font-family: var(--font-inter);
        font-size: 22px;
        font-weight: 600;
        width: unset;
        .swiper-pagination-total {
          color: var(--paragraph-color);
          font-size: 18px;
          font-weight: 600;
          font-family: var(--font-inter);
        }
      }
    }
  }
  .testimonial-slider {
    .swiper-slide-active {
      .company-logo {
        animation: fadeInDown 1.7s;
      }
      .testimonial-content,
      .author-area {
        animation: fadeInUp 1.7s;
      }
    }
  }
}

/*================================================
11. latest news
=================================================*/
.blog-section {
  .blog-card {
    .blog-img-wrap {
      position: relative;
      .blog-img{
        position: relative;
        display: block;
        overflow: hidden;
        border-radius: 30px;
        @include lg-down-device(){
          border-radius: 20px;
        }
        @include sm-down-device(){
          border-radius: 15px;
        }
        img {
          border-radius: 30px;
          transition: 0.5s ease-out;
          @include lg-down-device(){
            border-radius: 20px;
          }
          @include sm-down-device(){
            border-radius: 15px;
          }
        }
      }
      .batch {
        mask-image: url(../image/blog-mask.png);
        mask-position: left;
        mask-size: contain;
        mask-repeat: no-repeat;
        background: #fbfbfb;
        padding: 48px 48px 18px 0px;
        position: absolute;
        bottom: 0;
        left: 0;
        @include md-device(){
          padding: 46px 40px 16px 0px;
        }
        @include sm-down-device(){
          padding: 48px 48px 12px 0px;
        }
        ul {
          margin: 0;
          padding: 0;
          list-style: none;
          display: flex;
          align-items: center;
          gap: 12px;
          @include md-device(){
            gap: 8px;
          }
          li {
            font-family: var(--font-inter);
            font-weight: 400;
            font-size: 14px;
            line-height: 1;
            color: var(--paragraph-color);
            position: relative;
            padding-left: 12px;
            &::after {
              content: "";
              position: absolute;
              top: 53%;
              transform: translateY(-50%);
              left: 0;
              width: 1px;
              height: 14px;
              background-color: #d9d9d9;
            }
            a{
              color: var(--paragraph-color);
              transition: 0.5s;
              &:hover{
                color: var(--primary-color2);
              }
            }
            @include md-device(){
              padding-left: 8px;
            }
            @include sm-down-device(){
              font-size: 13px;
            }
            &:first-child {
              padding-left: 0;
              &::after {
                display: none;
              }
            }
          }
        }
      }
    }
    .blog-content {
      padding-top: 15px;
      @include lg-down-device(){
        padding-top: 10px;
      }
      h5 {
        margin-bottom: 15px;
        a {
          font-family: var(--font-mulish);
          font-weight: 700;
          font-size: 20px;
          line-height: 1.2;
          color: var(--title-color2);
          text-transform: capitalize;
          transition: 0.35s;
          &:hover {
            color: var(--primary-color2);
          }
        }
      }
    }
    &:hover{
      .blog-img-wrap{
        .blog-img{
          img{
            transform: scale(1.1);
          }
        }
      }
    }
  }
}

/*================================================
11. Empzio work section
=================================================*/
.empzio-work-section {
  .single-work-section {
    position: relative;
    .single-work-wrap {
      h2 {
        font-family: var(--font-inter);
        font-weight: 800;
        font-size: 60px;
        line-height: 1;
        text-align: center;
        color: #bf9264;
        margin-bottom: 10px;
        @include xxl-down-device(){
          font-size: 55px;
        }
        @include xl-down-device(){
          font-size: 50px;
        }
        @include md-down-device(){
          font-size: 45px;
        }
        @include sm-down-device(){
          font-size: 42px;
        }
      }
      h4 {
        font-family: var(--font-mulish);
        font-weight: 700;
        font-size: 24px;
        line-height: 1.2;
        text-align: center;
        color: var(--title-color2);
        margin-bottom: 14px;
        text-transform: capitalize;
        @include xl-down-device(){
          font-size: 22px;
        }
        @include lg-down-device(){
          margin-bottom: 10px;
        }
      }
      p {
        font-family: var(--font-inter);
        font-weight: 400;
        font-size: 16px;
        line-height: 1.3;
        color: var(--paragraph-color);
        text-align: center;
        margin-bottom: 0;
        @include lg-device(){
          font-size: 14px;
        }
        @include md-down-device(){
          font-size: 14px;
        }
        @include sm-down-device(){
          font-size: 15px;
        }
      }
    }
    .arrow{
      fill: #BF9264;
      position: absolute;
      top: 10px;
      right: -28%;
      @include xxl-down-device(){
        width: 110px;
        right: -26%;
      }
      @include xl-down-device(){
        width: 100px;
      }
      @include lg-down-device(){
        display: none;
      }
    }
    &.style-2 {
      .single-work-wrap {
        h2 {
          color: #6f826a;
        }
      }
      .arrow{
        fill: #6F826A;
        top: unset;
        bottom: -24px;
        @include xl-down-device(){
          bottom: -28px;
        }
      }
    }
    &.style-3 {
      .single-work-wrap {
        h2 {
          color: #a6ca88;
        }
      }
      .arrow{
        fill: #A6CA88;
      }
    }
    &.style-4 {
      .single-work-wrap {
        h2 {
          color: #b2b464;
        }
      }
    }
  }
}
/*================================================
12. latest news
=================================================*/
.location-section {
  background-color: #F3F0EA;
  padding: 80px 0;
  margin-left: 30px;
  margin-right: 30px;
  border-radius: 30px;
  @include lg-down-device(){
    padding: 60px 0;
  }
  @include md-down-device(){
    margin-left: 10px;
    margin-right: 10px;
    border-radius: 20px;
    padding: 50px 0;
  }
  .location-card {
    position: relative;
    .location-image {
      position: relative;
      z-index: 2;
      border-radius: 15px;
      img{
        border-radius: 15px;
      }
    }
    .location-content-wrap{
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 2;
      max-width: 240px;
      width: 100%;
      @include xl-device(){
        max-width: 220px;
      }
    }
    .location-content{
      background-color: #F3F0EA;
      padding: 15px 10px;
      border-radius: 10px;
      text-align: center;
      h6{
        margin-bottom: 5px;
        a{
          color: var(--title-color2);
          font-family: var(--font-inter);
          font-weight: 600;
          font-size: 18px;
          line-height: 1.5;
          transition: 0.5s;
          &:hover{
            color: var(--primary-color2);
          }
        }
      }
      span{
        color: var(--primary-color2);
        font-family: var(--font-inter);
        font-weight: 600;
        font-size: 16px;
        line-height: 1;
      }
    }
  }
  .swiper-pagination1 {
    display: inline-flex;
    align-items: center;
    width: unset;
    z-index: 9;
    gap: 58px;
    position: relative;
    @include lg-down-device(){
      gap: 40px;
    }
    @include sm-down-device(){
      gap: 20px;
    }
    &::before{
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 95%;
      height: 1px;
      background-color: #A6A6A6;
    }
    @include sm-down-device() {
      justify-content: center;
    }
    .swiper-pagination-bullet {
      height: 8px;
      width: 8px;
      background-color: #A6A6A6;
      border-radius: 50%;
      opacity: 1;
      position: relative;
      &.swiper-pagination-bullet-active {
        width: 10px;
        height: 10px;
        background-color: var(--primary-color2);
      }
    }
  }
}

/*================================================
13. footer section
=================================================*/
.footer-section {
  background: var(--title-color);
  .footer-top {
    padding: 70px 0;
    .footer-widget {
      .footer-logo {
        display: inline-block;
        margin-bottom: 23px;
        img{
          width: 160px;
          @include sm-down-device(){
            width: 140px;
          }
        }
      }
      p {
        font-family: var(--font-inter);
        font-weight: 400;
        font-size: 16px;
        line-height: 1.4;
        color: #939393;
        margin-bottom: 45px;
        @include lg-device(){
          font-size: 14px;
          line-height: 1.6;
        }
        @include lg-down-device(){
          margin-bottom: 35px;
        }
      }
      .social-area {
        h5 {
          font-family: var(--font-mulish);
          font-weight: 700;
          font-size: 22px;
          line-height: 1;
          color: var(--white-color);
          margin-bottom: 25px;
        }
        ul {
          margin: 0;
          padding: 0;
          list-style: none;
          display: flex;
          align-items: center;
          gap: 15px;
          li {
            a {
              height: 26px;
              width: 26px;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: var(--white-color);
              border-radius: 50%;
              transition: 0.35s;
              i {
                color: var(--paragraph-color);
                transition: 0.35s;
                position: relative;
                left: 0;
                top: 0;
                margin-left: 0;
                font-size: 14px;
                &.bi-twitter-x{
                  font-size: 12px;
                }
              }
              &:hover {
                i {
                  color: var(--primary-color2);
                }
              }
            }
          }
        }
      }
      .footer-title {
        h5 {
          font-family: var(--font-mulish);
          font-size: 22px;
          font-weight: 700;
          line-height: 1;
          text-align: left;
          color: var(--white-color);
          margin-bottom: 25px;
        }
      }

      .widget-list {
        margin: 0;
        padding: 0;
        list-style: none;

        li {
          margin-bottom: 18px;
          &:last-child {
            margin-bottom: 0;
          }
          @include sm-down-device() {
            margin-bottom: 15px;
          }
          a {
            font-family: var(--font-inter);
            font-size: 16px;
            font-weight: 500;
            line-height: 1;
            text-align: left;
            color: #a5a5a5;
            transition: 0.35s;
            @include lg-device() {
              font-size: 14px;
            }
            @include lg-down-device(){
              font-size: 15px;
            }
            &:hover {
              color: var(--primary-color2);
            }
          }
        }
      }
      .app-list {
        ul {
          margin: 0;
          padding: 0;
          list-style: none;
          li {
            margin-bottom: 20px;
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
  .footer-bottom-wrap {
    border-top: 1px solid rgb(255, 255, 255, 0.15);
    padding: 30px 0;
    @include md-down-device(){
      padding: 25px 0;
    }
    .footer-bottom{
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;
      flex-wrap: wrap;
      @include md-down-device(){
        justify-content: center;
      }
      .copyright-area {
        p {
          font-family: var(--font-inter);
          font-weight: 400;
          font-size: 15px;
          line-height: 1;
          color: #d3d3d3;
          margin-bottom: 0;
          a {
            color: var(--white-color);
            font-weight: 500;
            transition: 0.5s;
            &:hover{
              color: var(--primary-color2);
            }
          }
        }
      }
      .contact-area {
        h6 {
          font-family: var(--font-inter);
          font-weight: 600;
          font-size: 15px;
          line-height: 1;
          color: var(--white-color);
          margin-bottom: 0;
          a {
            color: var(--white-color);
            transition: 0.5s;
            &:hover{
              color: var(--primary-color2);
            }
          }
        }
      }
    }
  }
}

/*================================================
14. breadcrcum area
=================================================*/
.breadcrumb-section {
  padding: 0 26px;
  @include lg-down-device() {
    padding: 0 20px;
  }
  @include sm-down-device() {
    padding: 0 10px;
  }
  .breadcrumb-wrap {
    background: #f1f1f1;
    border-radius: 20px;
    padding: 90px 0;
    @include lg-down-device() {
      padding: 60px 0;
    }
    @include md-down-device() {
      padding: 60px 0;
    }
    @include sm-down-device() {
      border-radius: 10px;
    }
    .breadcrumb-content {
      h1 {
        font-family: var(--font-inter);
        font-weight: 500;
        font-size: 50px;
        line-height: 1.2;
        color: var(--title-color2);
        margin-bottom: 0;
        @include xxl-down-device(){
          font-size: 47px;
        }
        @include xl-down-device(){
          font-size: 43px;
        }
        @include lg-down-device(){
          font-size: 38px;
        }
        @include md-down-device() {
          font-size: 35px;
        }
        @include sm-down-device() {
          font-size: 30px;
        }
      }
      >ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        gap: 8px;
        margin-bottom: 25px;
        @include lg-down-device() {
          margin-bottom: 20px;
        }
        @include sm-down-device() {
          margin-bottom: 15px;
        }
        li {
          font-family: var(--font-inter);
          font-weight: 500;
          font-size: 14px;
          line-height: 1;
          color: var(--title-color2);
          &::before {
            float: left;
            padding-right: 8px;
            content: url(../image/icon/star.svg);
          }
          &:first-child {
            &::before {
              display: none;
            }
          }
          a {
            color: #595959;
            transition: 0.5s;
            &:hover{
              color: var(--primary-color2);
            }
          }
        }
      }
    }
    .breadcrumb-content-left {
      display: flex;
      gap: 18px;
      .company-info {
        h5 {
          font-family: var(--font-inter);
          font-weight: 600;
          font-size: 20px;
          line-height: 1.2;
          color: var(--primary-color2);
          margin-bottom: 18px;
        }
        p {
          font-family: var(--font-inter);
          font-weight: 400;
          font-size: 16px;
          line-height: 1.3;
          color: var(--paragraph-color);
          margin-bottom: 0;
          span {
            font-weight: 500;
            color: var(--title-color2);
          }
        }
        .location {
          margin: 0;
          padding: 0;
          list-style: none;

          li {
            margin-bottom: 15px;
            &:last-child {
              margin-bottom: 0;
            }
            a {
              font-family: var(--font-inter);
              font-weight: 400;
              font-size: 15px;
              line-height: 1;
              color: var(--paragraph-color);

              svg {
                fill: var(--paragraph-color);
              }
            }
          }
        }
      }
    }
    .breadcrumb-content-right {
      .social-area {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 25px;
        h6 {
          font-family: var(--font-inter);
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
          color: var(--title-color2);
          margin-bottom: 0;
        }
        ul {
          margin: 0;
          padding: 0;
          list-style: none;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 15px;
          li {
            a {
              height: 20px;
              width: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: var(--white-color);
              border-radius: 50%;
              transition: 0.35s;
              i {
                color: var(--primary-color2);
                transition: 0.35s;
                position: relative;
                left: 0;
                top: 0;
                margin-left: 0;
                font-size: 14px;
                &.bi-twitter-x{
                  font-size: 11px;
                }
              }
              &:hover {
                background-color: var(--primary-color2);
                i {
                  color: var(--white-color);
                }
              }
            }
          }
        }
      }
      .button {
        display: flex;
        justify-content: end;
        @include lg-down-device() {
          justify-content: start;
        }
      }
    }
  }
  &.style-2 {
    .breadcrumb-wrap {
      background: #e8f2d6;
      .breadcrumb-content{
        display: grid;
        grid-template-columns: 38% 35% 20%;
        gap: 60px;
        justify-content: space-between;
        @include xxl-down-device(){
          gap: 20px;
          grid-template-columns: 35% 39% 20%;
        }
        @include xl-down-device(){
          grid-template-columns: 39% 32% 23%;
        }
        @include lg-down-device(){
          grid-template-columns: repeat(2, 1fr);
          row-gap: 30px;
        }
        @include md-down-device(){
          grid-template-columns: repeat(1, 1fr);
        }
        @include sm-down-device(){
          row-gap: 20px;
        }
        .company-icon-area {
          display: flex;
          align-items: center;
          gap: 18px;
          position: relative;
          padding-right: 66px;
          @include xxl-down-device(){
            padding-right: 20px;
            gap: 12px;
          }
          @include lg-down-device(){
            padding-right: 0;
          }
          @include md-down-device() {
            display: inline-flex;
            flex-wrap: wrap;
          }
          @include sm-down-device(){
            gap: 10px;
          }
          &::after{
            content: '';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 0;
            width: 1px;
            height: 67px;
            background-color: #BDBDBD;
            @include lg-down-device(){
              display: none;
            }
          }
          .icon{
            img{
              min-width: 60px;
              @include sm-down-device(){
                min-width: unset;
                width: 55px;
              }
            }
          }
          .content-area {
            h5 {
              font-family: var(--font-inter);
              font-weight: 600;
              font-size: 20px;
              line-height: 1.5;
              color: var(--primary-color2);
              margin-bottom: 5px;
              @include lg-device(){
                font-size: 18px;
              }
              @include sm-down-device(){
                font-size: 18px;
              }
            }
            p {
              font-family: var(--font-inter);
              font-weight: 400;
              font-size: 16px;
              line-height: 1.4;
              color: var(--paragraph-color);
              margin-bottom: 0;
              @include lg-device(){
                font-size: 14px;
              }
              @include sm-down-device(){
                font-size: 14px;
              }
              span {
                font-weight: 500;
                color: var(--title-color2);
              }
            }
          }
        }
        .job-discription {
          position: relative;
          padding-right: 66px;
          @include xxl-down-device(){
            padding-right: 20px;
          }
          @include lg-down-device(){
            padding-right: 0;
          }
          &::after{
            content: '';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 0;
            width: 1px;
            height: 67px;
            background-color: #BDBDBD;
            @include lg-down-device(){
              display: none;
            }
          }
          ul {
            margin: 0;
            padding: 0;
            list-style: none;
            display: inline-flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 14px;
            max-width: 418px;
            width: 100%;
            @include sm-down-device(){
              gap: 8px;
              row-gap: 14px;
            }
            li {
              background: #faf4db;
              padding: 5px 15px;
              border-radius: 5px;
              display: flex;
              gap: 4px;
              align-items: center;
              font-family: var(--font-inter);
              font-weight: 500;
              font-size: 14px;
              line-height: 20px;
              color: #8e5e0f;
              text-align: center;
              @include sm-down-device(){
                font-size: 13px;
                padding: 5px 12px;
              }
              &.style-2 {
                background: #e4f7ff;
                color: #0e658c;
                svg {
                  fill: #0e658c;
                }
              }
              &.style-3 {
                background: #fff0f5;
                color: #6d1e38;
              }
              &.style-4 {
                background: #edfedd;
                color: #243f0b;
              }
              &.style-5 {
                background: #f3edfb;
                color: #322052;
              }
            }
          }
        }
        .button-area {
          display: flex;
          align-items: center;
          justify-content: end;
          gap: 30px;
          @include lg-down-device(){
            justify-content: start;
          }
          .icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 1px solid #c5c5c5;
            display: flex;
            align-items: center;
            justify-content: center;
            svg {
              fill: var(--paragraph-color);
              margin-top: 2px;
            }
          }
        }
      }
    }
  }
}

/*================================================
15. sidebar area
=================================================*/
.range-wrap {
  padding-top: 10px;

  .slider-labels {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;

    .caption {
      color: var(--paragraph-color);
      font-family: var(--font-inter);
      font-size: 16px;
      font-weight: 600;
      line-height: 1;
      letter-spacing: 0.3px;
    }
  }

  .noUi-target,
  .range-wrap .noUi-target * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    touch-action: none;
    -moz-user-select: none;
    user-select: none;
    box-sizing: border-box;
  }

  .noUi-target {
    position: relative;
    direction: ltr;
  }

  .noUi-base {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
    /* Fix 401 */
  }

  .noUi-origin {
    position: absolute;
    right: 0;
    top: 0;
    left: 0;
    bottom: 0;
  }

  .noUi-handle {
    position: relative;
    z-index: 1;
  }

  .noUi-stacking .noUi-handle {
    /* This class is applied to the lower origin when
       its values is > 50%. */
    z-index: 10;
  }

  .noUi-state-tap {
    .noUi-origin {
      transition: left 0.3s, top 0.3s;
    }
  }

  .noUi-state-drag * {
    cursor: inherit !important;
  }

  .noUi-base,
  .range-wrap .noUi-handle {
    transform: translate3d(0, 0, 0);
  }

  .noUi-horizontal {
    height: 4px;

    .noUi-handle {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      left: 0px;
      right: 0px;
      top: -4px;
      background-color: var(--primary-color2);
      cursor: pointer;
    }
  }

  .noUi-background {
    background: #ececec;
    height: 5px;
    border-radius: 10px;
  }

  .noUi-connect {
    background: var(--title-color);
    transition: background 450ms;
    border-radius: 10px;
    height: 5px;
  }

  .noUi-target {
    border-radius: 10px;
  }
}
.left-sidebar {
  .widget-search {
    border: none;
    padding: 0;

    .wp-block-search__inside-wrapper {
      display: flex;
      align-items: center;
      border: 1px solid var(--border-color);
      border-radius: 12px;
      background: var(--white-color);

      input {
        background-color: transparent;
        color: #a6a6a6;
        font-family: var(--font-inter);
        font-size: 14px;
        font-weight: 400;
        border: none;
        outline: none;
        width: 100%;
        padding: 0px 25px;
        height: 52px;
        border-radius: unset;
      }

      button {
        min-width: 52px;
        max-width: 52px;
        height: 52px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        outline: none;
        background-color: var(--title-color);
        overflow: hidden;
        position: relative;
        z-index: 1;
        border-radius: 0 12px 12px 0;

        svg {
          fill: var(--white-color);
        }

        &::after {
          position: absolute;
          content: "";
          display: block;
          left: 15%;
          right: -20%;
          top: -4%;
          height: 150%;
          width: 150%;
          bottom: 0;
          border-radius: 2px;
          background-color: var(--primary-color2);
          transform: skewX(45deg) scale(0, 1);
          z-index: -1;
          transition: all 0.5s ease-out 0s;
        }

        &:hover {
          svg {
            fill: var(--white-color);
          }

          &::after {
            transform: skewX(45deg) scale(1, 1);
          }
        }
      }
    }
  }
  .left-sidebar-wrapper {
    background: #f2f2f2;
    border-radius: 10px;
    padding: 30px;
    @include xxl-down-device(){
      padding: 30px 25px;
    }
    @include sm-down-device(){
      padding: 25px 20px;
    }
    &.style-2 {
      padding: 15px;
      .single-widgets {
        padding: 25px;
      }
    }
    .single-widgets {
      border-bottom: 1px solid var(--white-color2);
      padding-bottom: 35px;
      background: var(--white-color);
      padding: 30px;
      border-radius: 12px;
      @include sm-down-device(){
        padding: 25px 20px;
      }
      &:last-child {
        border-bottom: none;
      }
      .widget-title {
        margin-bottom: 25px;
        @include sm-down-device() {
          margin-bottom: 20px;
        }
        h5 {
          color: var(--title-color2);
          font-family: var(--font-mulish);
          text-transform: capitalize;
          font-size: 22px;
          line-height: 1;
          margin-bottom: 0;
          font-weight: 700;
          text-align: left;
          @include sm-down-device() {
            font-size: 20px;
          }
        }
      }

      .checkbox-container {
        max-height: 300px;
        overflow: hidden;
        overflow-y: auto;
        &::-webkit-scrollbar {
          display: none;
        }
        ul {
          margin: 0;
          padding: 0;
          list-style: none;

          li {
            margin-bottom: 20px;
            position: relative;

            &:last-child {
              margin-bottom: 0;
            }
            @include sm-down-device(){
              margin-bottom: 15px;
            }
            .containerss {
              display: flex;
              gap: 5px;
              width: 100%;
              position: relative;
              padding-left: 25px;
              cursor: pointer;
              -webkit-user-select: none;
              -moz-user-select: none;
              user-select: none;
              @include sm-down-device(){
                padding-left: 20px;
              }
              span {
                color: var(--paragraph-color);
                line-height: 1.2;
                text-transform: capitalize;
                font-family: var(--font-inter);
                font-size: 16px;
                font-weight: 400;
                @include sm-down-device(){
                  font-size: 15px;
                }
              }

              input {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;

                &:checked ~ .checkmark {
                  background-color: transparent;
                  border-color: var(--primary-color2);

                  &::after {
                    content: "";
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    border-radius: 1px;
                    background: var(--primary-color2);
                  }
                }
                &:checked ~ span {
                  color: var(--primary-color2);
                }
              }

              .checkmark {
                position: absolute;
                top: 1px;
                left: 0;
                height: 12px;
                width: 12px;
                background-color: transparent;
                border: 1px solid var(--paragraph-color);
                border-radius: 2px;
                margin-top: 2px;
              }
            }
            i {
              position: absolute;
              top: 1px;
              right: 0;
              font-size: 13px;
              line-height: 1;
              cursor: pointer;
              transition: 0.5s ease;
              &.active {
                transform: rotate(90deg);
              }
            }
            .sub-category {
              position: static;
              min-width: 200px;
              border: none;
              opacity: 1;
              visibility: visible;
              transform: translateY(0px);
              padding: 20px 24px 0px;
              display: none;
            }
          }
        }
      }
      .recent-post-section {
        display: flex;
        align-items: center;
        gap: 14px;
        .recent-post-image {
          img {
            min-width: 59px;
            border-radius: 4px;
          }
        }
        .recent-post-content {
          h6 {
            margin-bottom: 8px;
            a{
              font-family: var(--font-mulish);
              font-weight: 700;
              font-size: 16px;
              line-height: 1.3;
              color: var(--title-color2);
              text-transform: capitalize;
              transition: 0.5s;
              &:hover{
                color: var(--primary-color2);
              }
            }
          }
          p {
            font-family: var(--font-inter);
            font-weight: 500;
            font-size: 14px;
            line-height: 100%;
            color: var(--paragraph-color);
            margin-bottom: 0;
          }
        }
      }

      &.style-2 {
        padding: 30px;
        border: 1px solid var(--border-color);

        @include xxl-down-device() {
          padding: 30px 20px;
        }
      }
    }
  }
}
.sidebar-banner {
  position: relative;
  .sidebar-banner-image {
    position: relative;
    img {
      border-radius: 20px;
    }
  }
  .banner-content {
    position: absolute;
    top: 0;
    left: 0;
    padding: 25px 30px;
    @include xxl-device() {
      padding: 15px 25px;
    }
    @include xl-device() {
      padding: 15px 25px;
    }
    h5 {
      font-family: var(--font-mulish);
      font-weight: 700;
      font-size: 22px;
      line-height: 1.2;
      color: var(--title-color2);
      text-transform: capitalize;
      margin-bottom: 15px;
    }
    .primary-btn-2 {
      padding: 6px 25px;
    }
  }
}
.pagination-area {
  .pagination {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    gap: 10px;
    .page-item {
      a {
        font-family: var(--font-inter);
        font-weight: 600;
        font-size: 16px;
        line-height: 1;
        text-align: center;
        color: var(--title-color2);
        width: 27px;
        height: 28px;
        background: transparent;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: 0.35s;
        svg {
          fill: var(--title-color2);
        }
        &:hover {
          background: var(--primary-color2);
          color: var(--white-color);
        }
      }
      &.active {
        a{
          background: var(--primary-color2);
          color: var(--white-color);
        }
      }
    }
    .arrow {
      a {
        width: 27px;
        height: 28px;
        border: 1px solid #a6a6a6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: 0.5s;
        svg {
          fill: #a6a6a6;
          transition: 0.5s;
        }
        &:hover{
          border: 1px solid var(--primary-color2);
          svg {
            fill: var(--primary-color2);
          }
        }
      }
    }
  }
}
.product-card-top-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 25px;
  @include sm-down-device() {
    gap: 8px;
    flex-direction: column;
    align-items: start;
  }
  .left-content {
    h6 {
      font-family: var(--font-inter);
      font-size: 16px;
      font-weight: 400;
      line-height: 1;
      letter-spacing: 0.02em;
      text-align: left;
      color: var(--paragraph-color);
      margin-bottom: 0;
      @include sm-down-device() {
        font-size: 14px;
      }
      span {
        font-weight: 600;
        color: var(--title-color2);
      }
    }
  }
  .grid-view {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    align-items: center;
    gap: 10px;

    li {
      border: 1px solid #eeeeee;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      transition: 0.35s;
      min-height: 24px;
      min-width: 24px;
      cursor: pointer;
      border-radius: 4px;

      svg {
        path {
          stroke: #a0a0a0;
        }
      }

      &:hover {
        background: var(--primary-color2);

        svg {
          path {
            stroke: var(--white-color);
          }
        }
      }

      &.active {
        background: var(--primary-color2);

        svg {
          path {
            stroke: var(--white-color);
          }
        }
      }
    }
  }
  .right-content {
    display: flex;
    align-items: center;
    gap: 40px;
    @include md-down-device() {
      display: inline-flex;
      flex-wrap: wrap;
    }

    &.style-2 {
      gap: 42px;

      @include md-down-device() {
        gap: 24px;
      }
    }

    .filter {
      display: flex;
      align-items: center;
      gap: 10px;
      line-height: 1;
      cursor: pointer;
      border: 1px solid var(--white-color2);
      padding: 5px 8px;

      .filter-icon {
        svg {
          fill: var(--title-color);
        }
      }

      span {
        color: var(--title-color);
        font-family: var(--font-inter);
        font-size: 14px;
        font-weight: 400;
        line-height: 1;
        letter-spacing: 0.02em;
        text-align: left;
      }
    }

    .category-area {
      .nice-select {
        color: var(--title-color2);
        font-family: var(--font-inter);
        font-size: 14px;
        font-weight: 400;
        display: flex;
        gap: 20px;
        width: 168px;
        background: transparent;
        padding-left: 15px;
        padding-right: 15px;
        border-radius: 0;
        border: none;
        padding-left: 0;
        @include md-down-device() {
          width: 160px;
        }

        .current {
          color: var(--title-color2);
          font-family: var(--font-inter);
          font-size: 14px;
          font-weight: 400;
          text-transform: capitalize;
        }

        &:after {
          border-bottom: 1px solid var(--title-color);
          border-right: 1px solid var(--title-color);
          content: "";
          display: block;
          height: 9px;
          margin-top: -5px;
          pointer-events: none;
          position: absolute;
          right: 7px;
          top: 46%;
          width: 9px;
        }

        .option {
          font-family: var(--font-inter);
          font-size: 13px;
          text-align: left;
          text-transform: capitalize;
          min-height: 35px;
          color: var(--title-color);
          padding-left: 25px;
          @include sm-down-device() {
            padding-left: 15px;
          }
        }

        .list {
          background-color: var(--white-color);
          box-shadow: 0px 4px 32px 0px #81818140;
          border: 1px solid #eeeeee;
          width: 230px;
          border-radius: unset;
          @include seventeen-down-device() {
            left: -25px;
          }
          @include xxl-down-device() {
            left: -50px;
          }
          @include md-down-device(){
            left: unset;
            right: 0;
          }
          @include sm-down-device() {
            left: 0;
            width: 200px;
          }
        }
      }

      .nice-select .option:hover,
      .nice-select .option.focus,
      .nice-select .option.selected.focus {
        background-color: #f6f6f6;
      }
    }

    .filter-content {
      h6 {
        font-family: var(--font-inter);
        font-size: 16px;
        font-weight: 400;
        line-height: 1;
        letter-spacing: 0.02em;
        text-align: left;
        color: var(--title-color);
        margin-bottom: 0;
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }
  }
}

.list-grid-product-wrap {
  &.column-2-wrapper {
    .item {
      width: 50%;

      @include md-down-device() {
        display: none;
      }
    }
  }

  &.column-1-wrapper {
    .item {
      width: 100%;
    }
    .feature-card{
      .feature-wrape{
        mask-image: url(../image/featured-bg2.png);
        mask-position: right bottom;
        .job-discription{
          ul{
            margin-bottom: 45px;
          }
        }
      }
    }
  }
}
/*================================================
16. faq section
=================================================*/
.faq-section {
  .faq-item {
    h4 {
      font-family: var(--font-inter);
      font-weight: 600;
      font-size: 28px;
      line-height: 1;
      color: var(--title-color);
      margin-bottom: 0;
    }
  }
  .faq-wrap {
    .accordion {
      .accordion-item {
        border: none;
        background-color: transparent;
        border-bottom: 1px solid var(--border-color);

        &:first-child {
          .accordion-header {
            .accordion-button {
              padding-top: 0;
            }
          }
        }

        &:last-child {
          border-bottom: unset;

          .accordion-header {
            .accordion-button {
              padding-bottom: 0;
            }
          }
        }

        .accordion-header {
          .accordion-button {
            border: none;
            border-radius: unset;
            box-shadow: none;
            color: var(--title-color);
            font-family: var(--font-inter);
            font-size: 18px;
            font-weight: 500;
            line-height: 1.4;
            text-transform: capitalize;
            padding: 17px 0;
            background-color: transparent;

            @include xxl-down-device() {
              font-size: 19px;
            }

            @include lg-down-device() {
              font-size: 18px;
            }

            &::after {
              width: unset;
              height: unset;
              content: "\F4FE";
              font-family: bootstrap-icons;
              background-image: none;
              font-weight: 800;
              font-size: 22px;
              color: var(--primary-color2);
            }

            &:not(.collapsed) {
              background-color: unset;

              &::after {
                content: "\F2EA";
              }
            }

            @include sm-down-device() {
              font-size: 16px;
            }
          }
        }

        .accordion-body {
          padding: 15px 0 20px;
          color: var(--paragraph-color);
          font-family: var(--font-inter);
          font-size: 16px;
          font-weight: 400;
          line-height: 1.5;
          letter-spacing: 0.48px;
          border-top: 1px solid var(--primary-color2);

          @include lg-down-device() {
            font-size: 16px;
          }
        }
      }
    }
  }
}

/*================================================
17. blog section2
=================================================*/
.blog-section2 {
  .blog-card2 {
    .blog-card-image {
      width: 100%;
      img {
        width: 100%;
        border-radius: 10px;
      }
    }
    .blog-card-content {
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: center;
        gap: 29px;
        margin-bottom: 10px;
        li {
          font-family: var(--font-inter);
          font-weight: 400;
          font-size: 14px;
          line-height: 1;
          color: #595959;
          position: relative;
          a{
            color: #595959;
            transition: 0.5s;
            &:hover{
              color: var(--primary-color2);
            }
          }
          &::after {
            content: "";
            height: 14px;
            width: 1px;
            background-color: #d9d9d9;
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            @include sm-down-device() {
              display: none;
              visibility: hidden;
            }
          }
          &:first-child {
            &::after {
              display: none;
            }
          }
        }
      }
      h3 {
        margin-bottom: 23px;
        @include sm-down-device(){
          margin-bottom: 15px;
        }
        a {
          font-family: var(--font-mulish);
          font-weight: 700;
          font-size: 30px;
          line-height: 1.3;
          color: var(--title-color2);
          text-transform: capitalize;
          transition: 0.35s;
          @include xxl-down-device(){
            font-size: 26px;
          }
          @include sm-down-device(){
            font-size: 21px;
          }
          &:hover {
            color: var(--primary-color2);
          }
        }
      }
      p {
        font-family: var(--font-inter);
        font-weight: 400;
        font-size: 18px;
        line-height: 1.6;
        color: var(--paragraph-color);
        margin-bottom: 28px;
        @include sm-down-device(){
          font-size: 16px;
          margin-bottom: 22px;
        }
      }
    }
  }
}

/*================================================
18. contact us section
=================================================*/
.contact-us-section {
  .inquiry-form {
    border: 1px solid var(--white-color2);
    padding: 40px;
    @include sm-down-device() {
      padding: 25px;
    }
  }
  .contact-info {
    background: #f2f2f2;
    padding: 35px 30px;
    border-radius: 20px;
    @include xxl-down-device() {
      padding: 25px 26px;
    }
    @include xl-down-device() {
      padding: 25px 26px;
    }
    @include md-down-device() {
      padding: 23px;
    }
    ul {
      margin: 0;
      padding: 0;
      list-style: none;
      display: inline-flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 24px;
      margin-bottom: 30px;
      @include md-down-device() {
        gap: 15px;
        margin-bottom: 20px;
      }
      li {
        display: flex;
        align-items: center;
        gap: 10px;
        .icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 1px solid var(--primary-color2);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: 0.35s;
          svg {
            fill: var(--primary-color2);
          }
          &:hover {
            background: var(--primary-color2);
            svg {
              fill: var(--white-color);
            }
          }
        }
        .content {
          display: flex;
          flex-direction: column;
          max-width: 230px;
          gap: 5px;
          a {
            color: #595959;
            font-family: var(--font-inter);
            font-weight: 400;
            font-size: 16px;
            transition: 0.35s;
            line-height: 1.5;
            &:hover {
              color: var(--primary-color2);
            }
          }
        }
      }
    }
    h5 {
      font-family: var(--font-mulish);
      font-weight: 700;
      font-size: 20px;
      line-height: 1;
      color: var(--title-color2);
      text-transform: capitalize;
      margin-bottom: 20px;
    }
    .service-available {
      display: flex;
      align-items: center;
      gap: 16px;
      p {
        font-family: var(--font-inter);
        font-weight: 500;
        font-size: 18px;
        line-height: 30px;
        color: var(--paragraph-color);
        margin-bottom: 0;
      }
      span {
        font-family: var(--font-inter);
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        color: #595959;
      }
    }
  }
  .social-area {
    margin-top: 37px;

    h5 {
      font-family: var(--font-mulish);
      font-weight: 700;
      font-size: 22px;
      line-height: 1;
      color: var(--title-color2);
      text-transform: capitalize;
      margin-bottom: 25px;
    }
    .social-link {
      padding: 0;
      margin: 0;
      list-style: none;
      display: flex;
      align-items: center;
      gap: 12px;

      li {
        margin-bottom: 0;

        a {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: transparent;
          transition: 0.5s;
          border: 1px solid var(--primary-color2);

          i {
            color: var(--primary-color2);
            font-size: 14px;
            transition: 0.5s;

            &.bi-twitter-x {
              font-size: 12px;
            }
          }

          &:hover {
            background-color: var(--primary-color2);
            color: var(--white-color);

            i {
              color: var(--white-color);
            }
          }
        }
      }
    }
  }
}

/*================================================
19. blog details section
=================================================*/
.contact-map-section {
  overflow: hidden;
  line-height: 1;
  margin-bottom: -2px;
  padding: 0 80px;
  border-radius: 30px;

  iframe {
    width: 100%;
    height: 100%;
    min-height: 620px;
    filter: invert(1) hue-rotate(180deg) brightness(0.8) grayscale(1);
    border-radius: 30px 30px 0 0;

    @include xxl-down-device() {
      min-height: 550px;
    }

    @include lg-down-device() {
      min-height: 500px;
    }

    @include sm-down-device() {
      min-height: 450px;
    }
  }
}

/*================================================
20. blog details section
=================================================*/
.blog-details-wrapper {
  .line-break {
    height: 20px;
    display: block;

    @include xl-down-device() {
      height: 15px;
    }
  }
  .post-author-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;

    .author-and-date {
      @include lg-device() {
        max-width: 420px;
      }

      @include lg-down-device() {
        max-width: 400px;
      }

      @include md-down-device() {
        max-width: unset;
      }

      @include sm-down-device() {
        gap: 10px;
      }

      .author-area {
        display: flex;
        align-items: center;
        gap: 14px;

        @include sm-down-device() {
          gap: 10px;
        }

        .author-img {
          img {
            min-width: 60px;
            max-width: 60px;
            height: 60px;
            border-radius: 50%;

            @include sm-down-device() {
              min-width: 40px;
              max-width: 40px;
              height: 40px;
            }
          }
        }

        .author-name-desig {
          span {
            color: #545454;
            font-family: var(--font-inter);
            font-size: 14px;
            font-weight: 500;
            line-height: 1;
            display: inline-block;
            margin-bottom: 10px;

            @include sm-down-device() {
              font-size: 11px;
              margin-bottom: 5px;
            }
          }

          h6 {
            margin-bottom: 0;
            a {
              color: var(--title-color2);
              font-family: var(--font-inter);
              text-transform: capitalize;
              font-size: 16px;
              font-weight: 500;
              line-height: 1.2;

              @include sm-down-device() {
                font-size: 16px;
              }
            }
          }
        }
      }
    }
    .date {
      span {
        color: #545454;
        font-family: var(--font-inter);
        font-size: 14px;
        font-weight: 500;
        line-height: 1;
        display: inline-block;
        margin-bottom: 10px;

        @include sm-down-device() {
          font-size: 11px;
          margin-bottom: 5px;
        }
      }

      h6 {
        color: var(--title-color2);
        font-family: var(--font-inter);
        text-transform: capitalize;
        font-size: 16px;
        font-weight: 500;
        line-height: 1.2;
        margin-bottom: 0;

        @include sm-down-device() {
          font-size: 16px;
        }
      }
    }

    .social-area {
      display: flex;
      align-items: center;
      gap: 20px;

      @include sm-down-device() {
        gap: 15px;
      }

      h6 {
        color: var(--title-color);
        font-family: var(--font-inter);
        text-transform: capitalize;
        font-size: 16px;
        font-weight: 500;
        line-height: 1;
        margin-bottom: 0;
      }

      .social-link {
        padding: 0;
        margin: 0;
        list-style: none;
        display: flex;
        align-items: center;
        gap: 12px;

        li {
          margin-bottom: 0;

          a {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #595959;
            transition: 0.5s;

            i {
              color: #595959;
              font-size: 14px;
              transition: 0.5s;

              &.bi-twitter-x {
                font-size: 12px;
              }
            }

            &:hover {
              background-color: var(--primary-color2);
              color: var(--white-color);

              i {
                color: var(--white-color);
              }
            }
          }
        }
      }
    }
  }
  .post-thumb {
    img {
      width: 100%;
      border-radius: 30px;
    }
  }
  .blog-details-wrape {
    .blog-details-content {
      h3 {
        font-family: var(--font-mulish);
        font-weight: 700;
        font-size: 34px;
        line-height: 1;
        color: var(--title-color2);
        margin-bottom: 0;
      }
      h5 {
        font-family: var(--font-mulish);
        font-weight: 700;
        font-size: 22px;
        line-height: 1;
        color: var(--title-color2);
        margin-bottom: 0;
      }
      h4 {
        font-family: var(--font-mulish);
        font-weight: 700;
        font-size: 28px;
        line-height: 1;
        color: var(--title-color2);
        margin-bottom: 0;
      }
      p {
        font-family: var(--font-inter);
        font-weight: 400;
        font-size: 18px;
        line-height: 1.5;
        color: var(--paragraph-color);
        margin-bottom: 0;
      }
    }
  }
}

/*================================================
21. form inner
=================================================*/
.form-inner {
  input {
    width: 100%;
    height: 52px;
    background-color: transparent !important;
    font-family: var(--font-inter);
    font-size: 16px;
    font-weight: 500;
    line-height: 1;
    color: var(--title-color2);
    appearance: none;
    border-bottom: 1px solid #d9d9d9;

    @include sm-down-device() {
      padding: 15px 20px;
    }

    &:focus {
      background-color: var(--white-color);
      color: var(--paragraph-color);
    }
  }

  textarea {
    width: 100%;
    background-color: transparent !important;
    font-family: var(--font-inter);
    font-size: 16px;
    font-weight: 500;
    line-height: 1;
    color: var(--title-color2);
    appearance: none;
    border: none;
    border-bottom: 1px solid #d9d9d9;

    &:focus {
      color: var(--paragraph-color);
      outline: none;
      border: none;
    }
  }

  button {
    padding: 14px 45px;
    border-radius: 6px;

    &::after {
      background-color: var(--primary-color);
    }

    &:hover {
      color: var(--white-color);
    }
  }
}

/*================================================
22. inquiry form
=================================================*/
.inquiry-form {
  background: #f0f0f0;
  border-radius: 30px;
  padding: 45px;
  &.contact-inquiry {
    background: #eef2e58c;
    button {
      padding: 14px 61px;
    }
  }

  @include xxl-down-device() {
    padding: 60px;
  }

  @include xl-down-device() {
    padding: 50px 40px;
  }

  @include md-down-device() {
    padding: 40px 25px;
  }

  @include sm-down-device() {
    padding: 35px 15px;
  }

  .title {
    margin-bottom: 30px;

    @include md-down-device() {
      margin-bottom: 20px;
    }

    h4 {
      color: var(--title-color2);
      text-transform: capitalize;
      margin-bottom: 0;
      font-family: var(--font-mulish);
      font-weight: 700;
      font-size: 24px;
      line-height: 1;

      @include lg-down-device() {
        font-size: 30px;
      }

      @include sm-down-device() {
        font-size: 28px;
      }
    }
  }

  .form-inner {
    input {
      background-color: var(--white-color);
    }

    textarea {
      color: var(--title-color2);

      &:focus {
        outline: none;
        border: none;
      }
    }
  }
}

/*================================================
23.  company details section
=================================================*/
.company-details-section {
  .company-left-content {
    h5 {
      font-family: var(--font-mulish);
      font-weight: 700;
      font-size: 22px;
      line-height: 1;
      color: var(--title-color2);
      margin-bottom: 23px;
    }
    p {
      font-family: var(--font-inter);
      font-weight: 400;
      font-size: 18px;
      line-height: 1.6;
      color: var(--paragraph-color);
      margin-bottom: 40px;
      @include sm-down-device(){
        font-size: 15px;
        line-height: 1.8;
      }
    }
    ul {
      margin: 0;
      padding: 0;
      list-style: none;
      margin-bottom: 40px;
      &:last-child {
        margin-bottom: 0;
      }
      li {
        font-family: var(--font-inter);
        font-weight: 400;
        font-size: 18px;
        line-height: 1.5;
        color: var(--paragraph-color);
        padding-left: 15px;
        position: relative;
        margin-bottom: 8px;
        @include sm-down-device(){
          font-size: 15px;
          line-height: 1.8;
        }
        &::before {
          content: "";
          height: 5px;
          width: 5px;
          border-radius: 50%;
          background-color: var(--paragraph-color);
          position: absolute;
          left: 0;
          top: 10px;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    h6 {
      font-family: var(--font-inter);
      font-weight: 600;
      font-size: 18px;
      line-height: 1;
      color: var(--paragraph-color);
      margin-bottom: 10px;
    }
    .working-details {
      p {
        margin-bottom: 15px;
      }
      ul {
        margin-bottom: 13px;
      }
    }
  }
  .company-right-content {
    .job-summary-area {
      background: var(--white-color);
      border: 1px solid var(--primary-color2);
      border-radius: 30px;
      padding: 50px 40px;
      margin-top: -150px;
      @include xl-down-device() {
        margin-top: 0;
      }
      @include md-down-device() {
        padding: 40px 30px;
      }
      @include sm-down-device() {
        padding: 20px;
      }
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        li {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 30px;
          &:last-child {
            margin-bottom: 0;
          }
          .left-content {
            display: flex;
            align-items: center;
            gap: 16px;
            svg {
              fill: var(--paragraph-color);
            }
            p {
              font-family: var(--font-inter);
              font-weight: 400;
              font-size: 16px;
              line-height: 1;
              margin-bottom: 0;
              color: var(--paragraph-color);
            }
          }
          .right-content {
            p {
              font-family: var(--font-inter);
              font-weight: 500;
              font-size: 16px;
              line-height: 1;
              color: var(--title-color);
              margin-bottom: 0;
            }
          }
        }
      }
    }
    .location-area {
      margin-top: 60px;
      h5 {
        font-family: var(--font-mulish);
        font-weight: 700;
        font-size: 22px;
        line-height: 0;
        color: var(--title-color2);
        margin-bottom: 40px;
      }
      iframe {
        width: 100%;
        height: 264px;
        border-radius: 20px;
      }
    }
    .view-job-btn {
      margin-top: 40px;
      a {
        font-family: var(--font-mulish);
        font-weight: 700;
        font-size: 16px;
        line-height: 1.3;
        color: var(--title-color2);
        svg {
          fill: var(--primary-color2);
        }
      }
    }
    .social-area {
      margin-top: 45px;
      h5 {
        font-family: var(--font-inter);
        font-weight: 700;
        font-size: 22px;
        line-height: 1;
        color: var(--title-color2);
        margin-bottom: 23px;
      }
      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: center;
        gap: 15px;
        li {
          a {
            height: 24px;
            width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--white-color);
            border: 1px solid var(--primary-color2);
            border-radius: 50%;
            transition: 0.35s;
            i {
              color: var(--primary-color2);
              transition: 0.35s;
              position: relative;
              left: 0;
              top: 0;
              margin-left: 0;
              font-size: 14px;
              &.bi-twitter-x{
                font-size: 11px;
              }
            }
            &:hover {
              background-color: var(--primary-color2);
              i {
                color: var(--white-color);
              }
            }
          }
        }
      }
    }
  }
}

/*================================================
24. gallery view section
=================================================*/
.gallery-view-section {
  .gallery-image {
    img {
      width: 100%;
      border-radius: 20px;
      @include sm-down-device(){
        border-radius: 10px;
      }
    }
  }
}

/*================================================
25. pricing plan area
=================================================*/
.pricing-plan-area {
  .pt-50{
    padding-top: 50px;
    @include xl-down-device(){
      padding-top: 0;
    }
  }
  .pricing-plan-area-wrap {
    position: relative;
    z-index: 1;
    @include xxl-down-device(){
      height: 100%;
    }
    .pricing-plan-card1 {
      mask-image: url(../image/inner-page/pricing-plan-bg.png);
      mask-size: cover;
      mask-repeat: no-repeat;
      height: 100%;
      width: 100%;
      position: relative;
      z-index: 1;
      padding: 90px 30px 35px;
      border-radius: 30px;
      background: #fdf3e9;
      @include xl-device(){
        padding: 90px 20px 35px;
      }
      @include md-device(){
        padding: 90px 20px 35px;
      }
      @include sm-down-device(){
        padding: 80px 15px 35px;
        border-radius: 15px;
      }
      .price-area {
        margin-bottom: 25px;
        border-bottom: 1px solid #A6A6A6;
        @include lg-down-device(){
          margin-bottom: 20px;
        }
        h2 {
          font-family: var(--font-mulish);
          font-weight: 700;
          font-size: 40px;
          line-height: 1.3;
          color: var(--primary-color2);
          margin-bottom: 20px;
          @include xl-device(){
            font-size: 34px;
          }
          @include md-device(){
            font-size: 30px;
          }
          @include md-down-device(){
            font-size: 35px;
          }
          @include sm-down-device(){
            font-size: 30px;
            line-height: 1.1;
          }
          span {
            font-family: var(--font-mulish);
            font-weight: 500;
            font-size: 18px;
            line-height: 1.3;
            color: var(--paragraph-color);
            @include xl-device(){
              font-size: 16px;
            }
            @include lg-down-device(){
              font-size: 16px;
            }
            @include sm-down-device(){
              font-size: 15px;
            }
          }
        }
      }
      .pricing-list {
        margin: 0;
        padding: 0;
        list-style: none;
        li {
          display: flex;
          align-items: center;
          gap: 12px;
          color: var(--paragraph-color);
          font-family: var(--font-inter);
          font-weight: 400;
          font-size: 17px;
          line-height: 1.2;
          margin-bottom: 20px;
          @include xl-device(){
            font-size: 16px;
            gap: 10px;
          }
          @include lg-down-device(){
            font-size: 16px;
            gap: 10px;
          }
          @include sm-down-device(){
            font-size: 15px;
            gap: 8px;
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      .add-on-list{
        padding: 0;
        margin: 0;
        list-style: none;
        li{
          margin-bottom: 32px;
          @include lg-down-device(){
            margin-bottom: 27px;
          }
          &:last-child{
            margin-bottom: 0;
          }
          .single-item{
            display: flex;
            justify-content: space-between;
            gap: 20px;
          }
          .content{
            h6{
              color: var(--title-color2);
              font-family: var(--font-inter);
              font-weight: 500;
              font-size: 20px;
              line-height: 1;
              margin-bottom: 12px;
              @include lg-down-device(){
                font-size: 18px;
              }
              @include sm-down-device(){
                margin-bottom: 8px;
              }
            }
            span{
              color: var(--paragraph-color);
              font-family: var(--font-inter);
              font-weight: 500;
              font-size: 18px;
              line-height: 1;
              @include lg-down-device(){
                font-size: 16px;
              }
              @include sm-down-device(){
                font-size: 15px;
              }
            }
          }
          .form-check{
            padding: 0;
            min-height: unset;
            .form-check-input{
              width: 47px;
              height: 22px;
              background-color: transparent;
              border: 1px solid var(--primary-color2);
              &:checked{
                background-color: var(--primary-color2);
                border-color: var(--primary-color2);
              }
              &:focus{
                border-color: var(--primary-color2);
                box-shadow: unset;
                
              }
            }
          }
          .pricing-list{
            margin-top: 20px;
            li{
              margin-bottom: 15px;
              font-size: 16px;
              @include lg-down-device(){
                font-size: 15px;
              }
              @include sm-down-device(){
                font-size: 14px;
              }
              &:last-child{
                margin-bottom: 0;
              }
            }
          }
        }
      }
      .order-summary-wrap{
        .title-and-quantity{
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 20px;
          margin-bottom: 25px;
          h5{
            color: var(--title-color2);
            font-family: var(--font-inter);
            font-weight: 500;
            font-size: 20px;
            line-height: 1;
            margin-bottom: 0;
            @include sm-down-device(){
              font-size: 18px;
            }
          }
          .quantity-area {
            .quantity {
              display: flex;
              gap: 5px;
              a {
                height: 24px;
                width: 34px;
                border-radius: 4px;
                background: var(--white-color);
                border: 1px solid var(--border-color);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                color: var(--title-color);
                font-weight: 600;
                cursor: pointer;
                transition: 0.35s;
                i{
                  color: var(--title-color);
                  transition: 0.35s;
                }
                &:hover {
                  background: var(--primary-color2);
                  border-color: var(--primary-color2);
                  color: var(--white-color);
                  i{
                    color: var(--white-color);
                  }
                }
              }
              input {
                height: 24px;
                width: 34px;
                border-radius: 4px;
                border: 1px solid var(--border-color);
                background-color: var(--white-color);
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: var(--font-dmsans);
                font-size: 14px;
                color: var(--title-color);
                font-weight: 600;
                text-align: center;
              }
            }
          }
        }
        table{
          width: 100%;
          margin-bottom: 25px;
          @include lg-down-device(){
            margin-bottom: 20px;
          }
          tbody{
            tr{
              td{
                color: var(--paragraph-color);
                font-family: var(--font-inter);
                font-weight: 400;
                font-size: 17px;
                line-height: 30px;
                padding-bottom: 10px;
                @include sm-down-device(){
                  font-size: 15px;
                  padding-bottom: 8px;
                }
                &:last-child{
                  text-align: end;
                }
              }
              &:last-child{
                border-top: 1px solid #D9D9D9;
                td{
                  padding-bottom: 0;
                  padding-top: 15px;
                  @include sm-down-device(){
                    padding-top: 10px;
                  }
                }
              }
            }
          }
        }
        .amount-and-btn-area{
          text-align: center;
          h2{
            color: var(--title-color2);
            font-family: var(--font-inter);
            font-weight: 500;
            font-size: 40px;
            line-height: 1;
            margin-bottom: 35px;
            @include xxl-down-device(){
              font-size: 38px;
              margin-bottom: 30px;
            }
            @include lg-down-device(){
              font-size: 35px;
              margin-bottom: 25px;
            }
            @include sm-down-device(){
              font-size: 33px;
              margin-bottom: 22px;
            }
          }
          .primary-btn-2{
            max-width: 245px;
            width: 100%;
            justify-content: center;
            padding: 12px 21px;
          }
        }
      }
    }
    .batch {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 99;
      @include xxl-down-device(){
        top: 5px;
      }
      @include md-device(){
        top: 7px;
      }
      @include md-down-device(){
        width: 100%;
        padding-left: 120px;
      }
      @include sm-down-device(){
        padding-left: 100px;
      }
      span {
        background: #ac7a50;
        padding: 13px 25px;
        font-family: var(--font-mulish);
        font-weight: 700;
        font-size: 24px;
        line-height: 100%;
        color: var(--white-color);
        text-transform: capitalize;
        border-radius: 27px 30px 0px 27px;
        display: inline-block;
        min-width: 325px;
        text-align: center;
        @include xl-device(){
          min-width: 255px;
          font-size: 22px;
        }
        @include md-device(){
          min-width: 230px;
          font-size: 20px;
        }
        @include md-down-device(){
          min-width: unset;
          width: 100%;
        }
        @include sm-down-device(){
          font-size: 20px;
        }
      }
    }
    &.two{
      .pricing-plan-card1{
        background-color: #E4EEFE;
      }
      .batch{
        span{
          background-color: #7887BF;
        }
      }
    }
    &.three{
      .pricing-plan-card1{
        background-color: #EAF4F4;
      }
      .batch{
        span{
          background-color: #61947E;
        }
      }
    }
  }
  
}
