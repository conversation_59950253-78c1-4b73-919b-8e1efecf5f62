<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\EducationLavelRequest;
use App\Repositories\Admin\EducationRepostitory;
use Illuminate\Http\Request;

class EducationController extends Controller
{
    public function __construct(protected EducationRepostitory $education) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->education->content();
        }

        return view('admin.pages.education-lavel.index');
    }

    /** new Education store
     * ========= store============
     *
     * @param App\Http\Requests\EducationLavelRequest
     * @return Response
     */
    public function store(EducationLavelRequest $request)
    {

        if (!$this->hasPermissions(['education.add', 'education.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->education->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $education = $this->education->getById($id);
        $data['name'] = $education->getTranslation('name', Request()->lang);
        $data['id'] = $education->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['education.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->education->destroy($id);
        return response()->json([
            'status' => $result['status'],
            'message' => $result['message']
        ]);

    }

    /** Education status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['education.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->education->statusChange($id);
        return $this->formatResponse($result);

    }
}
