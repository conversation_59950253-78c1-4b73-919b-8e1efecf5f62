<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class locale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $avail_able_locales = ['en', 'fr', 'sp', 'ru', 'bn'];
        $locale = session('APP_LOCALE');
        $locale = in_array($locale, $avail_able_locales) ? $locale : config('app.locale');
        app()->setLocale($locale);

        return $next($request);
    }
}
