<script>

    $(function(){
        "use strict";
        $(document).on('submit' '.register-form' , function(e){
            e.preventDefault();
            let form = $(this);
            let formData = new FormData(form[0]);
            let action = form.attr('action');
            $.ajax({
                type:"POST",
                url:action,
                data:formData,
                dataType:"json",
                cache: false,
                contentType: false,
                processData: false,
                success:function(data){
                    if(data.status ==false){
                        printErrorMsg(data.errors);
                    }else if(data.status ==true){
                        $(form)[0].reset();
                        toastr["info"](`${data.message}`);
                    }
                },
                error:function(data){
                    toastr["error"](`${data.message}`);
                }
            })
        })
        function printErrorMsg (msg) {
           $.each( msg, function( key, value ) {
               $('.'+key+'_err').text(value).fadeIn().delay(3000).fadeOut("slow");
           });
       }
       $(document).on("keydown.autocomplete", ".company_type", function(e) {
            $(this).autocomplete({
                source: function(request, response) {
                    let query = request.term;
                    let action = base + "/company-type/" + query;
                    ajaxAutoComplete(action, response);
                },
                select: function(event, ui) {
                 $(this).val(ui.item.company_type_name);
                }
            });
        });
    })
    function ajaxAutoComplete(action, response) {

        $.ajax({
            url: action,
            method: "GET",
            dataType: 'json',
            success: function(data) {
                response($.map(data, function(item) {
                    return {
                        label: item.company_type_name,
                        value: item.company_type_name
                    }
                }));
            }
        });
    }
</script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/front/login.blade.php ENDPATH**/ ?>