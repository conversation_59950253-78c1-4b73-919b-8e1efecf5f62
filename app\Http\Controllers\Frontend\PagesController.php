<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Admin\Pages;
use App\Repositories\Admin\BlogRepository;
use App\Repositories\CompanyRepository;
use App\Repositories\JobRepository;

class PagesController extends Controller
{
    public function __construct(protected JobRepository $job, protected BlogRepository $blog, protected CompanyRepository $company) {}

    public function customPage($slug)
    {
        $request = Request();
        $param['featured'] = '';
        $param['category_slug'] = '';
        $param['company'] = '';
        $style = isset(Request()->style) ? Request()->style : 'list-view';
        $datas = '';

        try {
            $data['page'] = Pages::with(['widgetContents' => function ($query) {
                $query->where('status', 1);
            }])->where('page_route', $slug)->first();
            $data['page']->page_slug;

            if ($request->ajax()) {
                if (isset($request->widget_name)) {
                    if ($request->widget_name == 'all-blog') {
                        return $this->blogSearch($request);
                    } elseif ($request->widget_name == 'job-list') {

                        $widgetItem = getWidgetContent($data['page']->id, 'job-list');

                        return $this->jobSearch($request, $widgetItem->widget_content['show_item']);
                    } elseif ($request->widget_name == 'company-list') {
                        $widgetItem = getWidgetContent($data['page']->id, 'company-list');

                        return $this->companySearch($request, $widgetItem->widget_content['show_item']);
                    }
                }
            } else {
                if (isset($request->featured) || isset($request->category) || isset($request->job_title) || isset($request->company) || isset($request->jobcategory) || isset($request->city) || isset($request->country)) {
                    $widgetItem = getWidgetContent($data['page']->id, 'job-list');
                    $param['featured'] = $request->featured;
                    $param['category_slug'] = $request->jobcategory;
                    $param['company'] = $request->company;
                    $datas = $this->jobSearch($request, $widgetItem->widget_content['show_item']);
                }
            }

            return view('front.pages.menu.pages', [
                'params' => $data['page']->page_slug,
                'param' => $param,
                'datas' => $datas,
                'style' => $style,
                'title' => $data['page']->page_name,
                'is_bread_crumb' => $data['page']->is_bread_crumb,
                'widget_contents' => $data['page']->widgetContents,
            ]);
        } catch (\Throwable $th) {
            //throw $th;

            return view('front.pages.404');
        }

    }

    /**
     * blogSearch
     *
     * @param  mixed  $request
     * @return view
     */
    public function blogSearch($request)
    {

        $blogs = $this->blog->blogSearch($request);
        $view = view('front.pages.blog.standard-content', compact('blogs'))->render();

        return response()->json(['status' => true, 'blogView' => $view]);

    }

    /**
     * jobSearch
     *
     * @param  mixed  $request
     * @param  int  $item
     */
    public function jobSearch($request, $item)
    {

        $job_style = 'list_view';
        $result = $this->job->searchJob($request, $item);
        $jobs = $result['jobs'];
        $param = $result['param'];
        if ($request->ajax()) {
            $jobs_view = view('front.pages.jobs.job-section', compact('jobs', 'job_style', 'param'))->render();

            return response()->json(['status' => true, 'jobs' => $jobs_view, 'total' => $jobs->total(), 'first_item' => $jobs->firstItem(),  'last_item' => $jobs->lastItem()]);
        }

        return $jobs;
    }

    /**
     * companySearch
     *
     * @param  mixed  $request
     * @param  int  $item
     */
    public function companySearch($request, $item)
    {
        $result = $this->company->companySearch($request, $item);
        $companies = $result['companies'];
        $company_style = $result['company_style'];
        if ($request->ajax()) {
            $companies_view = view('front.pages.company.item-style', compact('companies', 'company_style'))->render();
            return response()->json(['status' => true, 'companies' => $companies_view, 'total' => $companies->total(), 'first_item' => $companies->firstItem(),  'last_item' => $companies->lastItem()]);
        }
    }
}
