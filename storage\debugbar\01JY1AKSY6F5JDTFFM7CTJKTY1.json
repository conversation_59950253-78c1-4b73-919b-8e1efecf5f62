{"__meta": {"id": "01JY1AKSY6F5JDTFFM7CTJKTY1", "datetime": "2025-06-18 10:51:02", "utime": **********.471317, "method": "GET", "uri": "/admin/company?draw=1&columns%5B0%5D%5Bdata%5D=Company&columns%5B0%5D%5Bname%5D=Company&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=OwnerName&columns%5B1%5D%5Bname%5D=OwnerName&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=Organization%2FCountry&columns%5B2%5D%5Bname%5D=Organization%2FCountry&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=Establishment+Date&columns%5B3%5D%5Bname%5D=Establishment+Date&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=Verified&columns%5B4%5D%5Bname%5D=Verified&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=true&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=Status&columns%5B5%5D%5Bname%5D=Status&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=true&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=action&columns%5B6%5D%5Bname%5D=action&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=false&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=0&order%5B0%5D%5Bdir%5D=asc&start=0&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&_=1750243860232", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750243861.966042, "end": **********.471332, "duration": 0.5052900314331055, "duration_str": "505ms", "measures": [{"label": "Booting", "start": 1750243861.966042, "relative_start": 0, "end": **********.187017, "relative_end": **********.187017, "duration": 0.*****************, "duration_str": "221ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.187027, "relative_start": 0.*****************, "end": **********.471333, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.207336, "relative_start": 0.*****************, "end": **********.210031, "relative_end": **********.210031, "duration": 0.0026950836181640625, "duration_str": "2.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.46876, "relative_start": 0.****************, "end": **********.469153, "relative_end": **********.469153, "duration": 0.000392913818359375, "duration_str": "393μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/company", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "controller": "App\\Http\\Controllers\\Backend\\CompanyController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.company", "prefix": "admin/company", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/CompanyController.php:23-30</a>"}, "queries": {"count": 77, "nb_statements": 77, "nb_visible_statements": 77, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04669999999999998, "accumulated_duration_str": "46.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.227299, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 4.604}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": "middleware", "name": "prevent-back-history", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Middleware\\PreventBackHistory.php", "line": 19}], "start": **********.237967, "duration": 0.00526, "duration_str": "5.26ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 4.604, "width_percent": 11.263}, {"sql": "select `companies`.*, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id` and `job_deadline` >= '2025-06-18 10:51:02' and `status` = 1) as `active_jobs_count` from `companies` order by `id` desc", "type": "query", "params": [], "bindings": ["2025-06-18 10:51:02", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, {"index": 16, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 46}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/CompanyController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CompanyController.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.247715, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:35", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=35", "ajax": false, "filename": "CompanyRepository.php", "line": "35"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 15.867, "width_percent": 3.276}, {"sql": "select * from `company_types` where `company_types`.`id` in (1, 2, 3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/CompanyController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CompanyController.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2516232, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:35", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=35", "ajax": false, "filename": "CompanyRepository.php", "line": "35"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 19.143, "width_percent": 1.028}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 65 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.269885, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 20.171, "width_percent": 1.028}, {"sql": "select * from `users` where `users`.`id` = 271 limit 1", "type": "query", "params": [], "bindings": [271], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.271716, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 21.199, "width_percent": 0.685}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 20 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.273056, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 21.884, "width_percent": 0.685}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 64 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.276514, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 22.57, "width_percent": 0.792}, {"sql": "select * from `users` where `users`.`id` = 265 limit 1", "type": "query", "params": [], "bindings": [265], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2783458, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 23.362, "width_percent": 1.306}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 8 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.280102, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 24.668, "width_percent": 0.857}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 63 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.283369, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 25.525, "width_percent": 0.792}, {"sql": "select * from `users` where `users`.`id` = 264 limit 1", "type": "query", "params": [], "bindings": [264], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2846582, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 26.317, "width_percent": 0.6}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 1 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.2859151, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 26.916, "width_percent": 0.642}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 62 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.2902188, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 27.559, "width_percent": 0.921}, {"sql": "select * from `users` where `users`.`id` = 263 limit 1", "type": "query", "params": [], "bindings": [263], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.291599, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 28.48, "width_percent": 0.621}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 60 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.295579, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 29.101, "width_percent": 1.135}, {"sql": "select * from `users` where `users`.`id` = 257 limit 1", "type": "query", "params": [], "bindings": [257], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.297276, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 30.236, "width_percent": 0.899}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 15 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.298734, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 31.135, "width_percent": 0.878}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 59 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.30208, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 32.013, "width_percent": 0.921}, {"sql": "select * from `users` where `users`.`id` = 256 limit 1", "type": "query", "params": [], "bindings": [256], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3034592, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 32.934, "width_percent": 0.6}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 12 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.304714, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 33.533, "width_percent": 0.664}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 58 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.307717, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 34.197, "width_percent": 0.749}, {"sql": "select * from `users` where `users`.`id` = 255 limit 1", "type": "query", "params": [], "bindings": [255], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3089871, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 34.946, "width_percent": 0.621}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 16 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3102572, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 35.567, "width_percent": 0.985}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 57 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3142772, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 36.552, "width_percent": 1.028}, {"sql": "select * from `users` where `users`.`id` = 254 limit 1", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3157191, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 37.58, "width_percent": 0.728}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 11 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.317045, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 38.308, "width_percent": 0.749}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 55 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3201458, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 39.058, "width_percent": 0.878}, {"sql": "select * from `users` where `users`.`id` = 225 limit 1", "type": "query", "params": [], "bindings": [225], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.321498, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 39.936, "width_percent": 0.707}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 17 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.322805, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 40.642, "width_percent": 0.749}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 54 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3258572, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 41.392, "width_percent": 1.328}, {"sql": "select * from `users` where `users`.`id` = 224 limit 1", "type": "query", "params": [], "bindings": [224], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3278408, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 42.719, "width_percent": 1.692}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 13 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.329805, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 44.411, "width_percent": 0.899}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 53 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3335161, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 45.31, "width_percent": 0.942}, {"sql": "select * from `users` where `users`.`id` = 223 limit 1", "type": "query", "params": [], "bindings": [223], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.33489, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 46.253, "width_percent": 0.771}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 52 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.338008, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 47.024, "width_percent": 0.921}, {"sql": "select * from `users` where `users`.`id` = 222 limit 1", "type": "query", "params": [], "bindings": [222], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.339368, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 47.944, "width_percent": 0.771}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 19 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.340697, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 48.715, "width_percent": 0.792}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 51 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3440251, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 49.507, "width_percent": 1.842}, {"sql": "select * from `users` where `users`.`id` = 221 limit 1", "type": "query", "params": [], "bindings": [221], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.346163, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 51.349, "width_percent": 0.878}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 14 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.347615, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 52.227, "width_percent": 0.835}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 50 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [50], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.352077, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 53.062, "width_percent": 1.777}, {"sql": "select * from `users` where `users`.`id` = 219 limit 1", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.356456, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 54.839, "width_percent": 1.328}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 49 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [49], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.362944, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 56.167, "width_percent": 6.488}, {"sql": "select * from `users` where `users`.`id` = 218 limit 1", "type": "query", "params": [], "bindings": [218], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3674312, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 62.655, "width_percent": 1.392}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 3 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3695061, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 64.047, "width_percent": 1.37}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 48 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.373143, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 65.418, "width_percent": 1.028}, {"sql": "select * from `users` where `users`.`id` = 217 limit 1", "type": "query", "params": [], "bindings": [217], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.374564, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.445, "width_percent": 0.835}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 2 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3759398, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 67.281, "width_percent": 0.899}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 47 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.383892, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 68.18, "width_percent": 1.884}, {"sql": "select * from `users` where `users`.`id` = 216 limit 1", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.385938, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 70.064, "width_percent": 1.092}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 9 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.387659, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 71.156, "width_percent": 1.135}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 46 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.392408, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 72.291, "width_percent": 1.306}, {"sql": "select * from `users` where `users`.`id` = 214 limit 1", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.394437, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 73.597, "width_percent": 1.949}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 45 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.4003098, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 75.546, "width_percent": 1.306}, {"sql": "select * from `users` where `users`.`id` = 213 limit 1", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.401982, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 76.852, "width_percent": 1.071}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 5 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.403609, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.923, "width_percent": 0.749}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 44 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.4068449, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 78.672, "width_percent": 0.835}, {"sql": "select * from `users` where `users`.`id` = 212 limit 1", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.4081671, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 79.507, "width_percent": 0.664}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 4 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.409456, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.171, "width_percent": 0.685}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 43 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.414972, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.857, "width_percent": 1.585}, {"sql": "select * from `users` where `users`.`id` = 203 limit 1", "type": "query", "params": [], "bindings": [203], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.416795, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 82.441, "width_percent": 1.156}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 41 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.4207711, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 83.597, "width_percent": 1.049}, {"sql": "select * from `users` where `users`.`id` = 192 limit 1", "type": "query", "params": [], "bindings": [192], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.422263, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 84.647, "width_percent": 0.857}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 10 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.423696, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 85.503, "width_percent": 0.664}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 40 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.427409, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 86.167, "width_percent": 3.469}, {"sql": "select * from `users` where `users`.`id` = 191 limit 1", "type": "query", "params": [], "bindings": [191], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.430318, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 89.636, "width_percent": 1.006}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 39 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.434461, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 90.642, "width_percent": 0.964}, {"sql": "select * from `users` where `users`.`id` = 177 limit 1", "type": "query", "params": [], "bindings": [177], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.435856, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 91.606, "width_percent": 0.728}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 35 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.439518, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 92.334, "width_percent": 0.835}, {"sql": "select * from `users` where `users`.`id` = 167 limit 1", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.440844, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 93.169, "width_percent": 0.707}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 26 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.445573, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 93.876, "width_percent": 2.12}, {"sql": "select * from `users` where `users`.`id` = 158 limit 1", "type": "query", "params": [], "bindings": [158], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.447767, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 95.996, "width_percent": 0.792}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 20 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.4515738, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 96.788, "width_percent": 0.899}, {"sql": "select * from `users` where `users`.`id` = 152 limit 1", "type": "query", "params": [], "bindings": [152], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.452933, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 97.687, "width_percent": 0.707}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 1 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.4566288, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 98.394, "width_percent": 0.899}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.4579911, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 99.293, "width_percent": 0.707}]}, "models": {"data": {"App\\Models\\Admin\\CompanyTypeTranslation": {"value": 70, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyTypeTranslation.php&line=1", "ajax": false, "filename": "CompanyTypeTranslation.php", "line": "?"}}, "App\\Models\\Admin\\Company": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\User": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\CompanyType": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=1", "ajax": false, "filename": "CompanyType.php", "line": "?"}}, "App\\Models\\Admin\\CompanyTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyTranslation.php&line=1", "ajax": false, "filename": "CompanyTranslation.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 148, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/lang/flags/in.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/company?_=1750243860232&columns%5B0%5D%5Bdata%5D=Company&columns%5B0%5D%...", "action_name": "admin.company", "controller_action": "App\\Http\\Controllers\\Backend\\CompanyController@index", "uri": "GET admin/company", "controller": "App\\Http\\Controllers\\Backend\\CompanyController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/company", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/CompanyController.php:23-30</a>", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "duration": "514ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Company</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Company</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"9 characters\">OwnerName</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">OwnerName</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Organization/Country</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Organization/Country</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Establishment Date</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Establishment Date</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Verified</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Verified</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1750243860232</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-292566748 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-292566748\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-77637780 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/admin/company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6InUrS2xVM0VoYU9kTHF0SDA3TU5BeWc9PSIsInZhbHVlIjoiMXNoVGszNlFZbkltckh6Sk5BQnhrU2hYNjc0RmhvQ0hDbkVTK1JxR0Q5alhoRE1sRGREMUkzNytqMkFVWWlETk9pbWlrUFJIYlQrY3UxSkw4cUNaa3J1ajkrYndGcnQvWnlack5Ra3hKZHUzY1NDYndEVVVuanh0N2RZUi9LNVQiLCJtYWMiOiJmYWRlZjE3NDFhNzFmN2I3ODAwN2FmNDdhNGY0ZTBlMzFiNmU2MWQzOGU1ODM4NmFiYWJkM2RhZjEwOTExYTIxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImxYcGd4b3JWcVBPaUt4RHZwYWpiOHc9PSIsInZhbHVlIjoiS3JYdGZwTzh6ekdSYjdZeHdGY0NNR010RTdwMDJlMUx0eVlzQnhxZGM1MDBnQ20yUTFqZFdkQ3k3eWVsOWJjUFlOcCtUb09NZkFNSWdiZ283aTQwWmZVeXNZS3VDQ29Za1A5bE9WZWRkZnRxTG9pbWJha2RLZ2UyN2tkcnZFRHgiLCJtYWMiOiIwODNmZTA3NjkzYTk0OTE4NmM3MGMwNmU0YzFlMjY3YTQ2ZTEzNzM3OTU1OTdmNGM5YjlhMWFmMWQyM2ZkMzNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77637780\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1081016312 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qUd4p9n8BUUDYUEq0qgc1rceEYaWteQGcYoDZANF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1081016312\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2033793175 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 10:51:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033793175\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1353501741 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/storage/lang/flags/in.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353501741\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/company?_=1750243860232&columns%5B0%5D%5Bdata%5D=Company&columns%5B0%5D%...", "action_name": "admin.company", "controller_action": "App\\Http\\Controllers\\Backend\\CompanyController@index"}, "badge": null}}