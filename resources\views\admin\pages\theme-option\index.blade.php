@extends('admin.master')

@push('post_styles')
    <link rel="stylesheet" href="{{ asset('backend/assets/css/codemirror.css') }}">
    <script src="{{ asset('backend/assets/js/codemirror.js') }}"></script>
    <script src="{{ asset('backend/assets/js/codemirror_javascript.js') }}"></script>
@endpush

@section('breadcrumb')
    <div class="breadcrumb-area">
        <h5>{{ translate('Dashboard') }}</h5>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">{{ translate('Dashboard') }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ translate('Theme Option') }}</li>
            </ol>
        </nav>
    </div>
@endsection
@section('main_content')
    <div class="main-container">
        <div class="main-content">
            <div class="page-title2">
                <img src="{{ asset('backend/assets/images/bg/title-loog.png') }}" class="title-logo" alt="">
                <h5>{{ translate('Theme Option Settings') }}</h5>
            </div>

            <!--================== theme-option-area start ==================== -->

            <div class="tab-area theme-option-area">

                <div class="d-flex align-items-start">

                    <!--================== theme-option-area panel start ==================== -->

                    <div class="nav theme-option-nav flex-column nav-pills me-3" id="v-pills-tab" role="tablist"
                        aria-orientation="vertical">
                        <button class="nav-link theme-option-link active" id="general-tab" data-bs-toggle="pill"
                            data-bs-target="#general"type="button" role="tab" aria-controls="general"
                            aria-selected="true"> {{ translate('General') }}</button>
                        <button class="nav-link theme-option-link" id="logo-tab" data-bs-toggle="pill"
                            data-bs-target="#logo" type="button" role="tab" aria-controls="logo"
                            aria-selected="false">{{ translate('Logo & Favicon') }}</button>
                        <button class="nav-link theme-option-link" id="theme-color-tab" data-bs-toggle="pill"
                            data-bs-target="#theme-color" type="button" role="tab" aria-controls="theme-color"
                            aria-selected="false">{{ translate('Theme Color') }}</button>
                        <button class="nav-link theme-option-link" id="page-setting-tab" data-bs-toggle="pill"
                            data-bs-target="#page-setting" type="button" role="tab" aria-controls="page-setting"
                            aria-selected="false">{{ translate('Page Setting') }}</button>
                        <button class="nav-link theme-option-link" id="header-tab" data-bs-toggle="pill"
                            data-bs-target="#header" type="button" role="tab" aria-controls="header"
                            aria-selected="false">{{ translate('Header') }} </button>

                        <button class="nav-link theme-option-link" id="footer-tab" data-bs-toggle="pill"
                            data-bs-target="#footer" type="button" role="tab" aria-controls="footer"
                            aria-selected="false">{{ translate('Footer') }}</button>

                        <button class="nav-link theme-option-link" id="breadcrumb -tab" data-bs-toggle="pill"
                            data-bs-target="#breadcrumb " type="button" role="tab" aria-controls="breadcrumb "
                            aria-selected="false">{{ translate('Breadcrumb') }}</button>

                        <button class="nav-link theme-option-link" id="google_analytics-tab" data-bs-toggle="pill"
                            data-bs-target="#google_analytics" type="button" role="tab"
                            aria-controls="google_analytics"
                            aria-selected="false">{{ translate('Google Analytics') }}</button>

                        <button class="nav-link theme-option-link" id="cookie-tab" data-bs-toggle="pill"
                            data-bs-target="#cookie" type="button" role="tab" aria-controls="cookie"
                            aria-selected="false">{{ translate('GDPR Cookie') }}</button>

                        <button class="nav-link theme-option-link" id="tawk-chat-tab" data-bs-toggle="pill"
                            data-bs-target="#tawk-chat" type="button" role="tab" aria-controls="tawk-chat"
                            aria-selected="false">{{ translate('Tawk Chat') }}</button>
                        {{-- @can('themeoption.script')
                            <button class="nav-link theme-option-link" id="custom-script-tab" data-bs-toggle="pill"
                                data-bs-target="#custom-script" type="button" role="tab" aria-controls="custom-script"
                                aria-selected="false">{{ translate('Custom Scripts') }}</button>
                        @endcan --}}

                    </div>

                    <!--================== theme-option-area panel end ==================== -->


                    @php
                        $generalSetting = getThemeOption('general') ?? [];
                    @endphp
                    <!--================== theme-option-area panel content start ==================== -->

                    <div class="tab-content theme-option-content" id="v-pills-tabContent">
                        <div class="tab-pane fade show active" id="general" role="tabpanel"
                            aria-labelledby="general-tab">
                            <div class="row">
                                <div class="col-12">
                                    <div class="eg-card product-card">
                                        <form enctype="multipart/form-data" class="add_setting" method="POST"
                                            action="{{ route('admin.theme.setting') }}" data-key="general">
                                            @csrf
                                            <div class="row">
                                                <div class="col-xl-6">
                                                    <div class="form-inner mb-2">
                                                        <label>{{ translate('Application Name') }}
                                                        </label><input type="text" class="username-input"
                                                            value="{{ $generalSetting['application_name'] ?? '' }}"
                                                            name="application_name">
                                                    </div>
                                                </div>
                                                <div class="col-xl-6">
                                                    <div class="form-inner mb-2"> <label>{{ translate('Address') }}
                                                        </label>
                                                        <input type="text" class="username-input"
                                                            name="company_address"
                                                            value="{{ $generalSetting['company_address'] ?? '' }}">
                                                    </div>
                                                </div>
                                                <div class="col-xl-6">
                                                    <div class="form-inner mb-2">
                                                        <label>{{ translate('Contact Number') }}</label>
                                                        <input type="text" class="username-input" name="company_phone"
                                                            value="{{ $generalSetting['company_phone'] ?? '' }}">
                                                    </div>
                                                </div>
                                                <input type="hidden" name="primary_color"
                                                    value="{{ $generalSetting['primary_color'] ?? '#00A7AC' }}">
                                            </div>
                                            <div class="text-end mt-4">
                                                <button type="submit"
                                                    class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @php
                            $siteLogo = getThemeOption('site_logo') ?? [];
                        @endphp
                        <div class="tab-pane fade footer-widget" id="logo" role="tabpanel"
                            aria-labelledby="v-pills-profile-tab">
                            <div class="eg-card product-card">
                                <form enctype="multipart/form-data" class="add_setting" method="POST"
                                    action="{{ route('admin.theme.setting') }}" data-key="site_logo">
                                    @csrf
                                    <div class="row">
                                        <div class="col-xl-6">
                                            <div class="form-inner file-upload mb-35">
                                                <label class="control-label">{{ translate('Site Logo') }}</span></label>
                                                <div class="dropzone-wrapper">
                                                    <div class="dropzone-desc">
                                                        <i class="glyphicon glyphicon-download-alt"></i>
                                                        <p>{{ translate('Choose an image file or drag it here') }}</p>
                                                    </div>
                                                    <input type="file" class="dropzone theme-option-image">
                                                    <input type="hidden" name="site_logo" id="old_file"
                                                        value="{{ $siteLogo['site_logo'] ?? '' }}">
                                                </div>
                                                <div class="preview-zone hidden">
                                                    <div class="box box-solid">
                                                        <div class="box-body">

                                                            @if (isset($siteLogo['site_logo']) &&
                                                                    fileExists('theme-options', $siteLogo['site_logo']) == true &&
                                                                    $siteLogo['site_logo'] !== '')
                                                                <div class="img-thumb-wrapper card shadow"> <span
                                                                        class="remove text-danger">
                                                                        <i class="bi bi-trash"></i> </span>
                                                                    <img class="img-thumb" width="100"
                                                                        src="{{ asset('storage/theme-options/' . $siteLogo['site_logo']) }}" />
                                                                </div>
                                                            @endif

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-6">
                                            <div class="form-inner file-upload mb-35">
                                                <label class="control-label">{{ translate('favicon') }}</span></label>
                                                <div class="dropzone-wrapper">
                                                    <div class="dropzone-desc">
                                                        <i class="glyphicon glyphicon-download-alt"></i>
                                                        <p>{{ translate('Choose an image file or drag it here') }}</p>
                                                    </div>
                                                    <input type="file" class="dropzone theme-option-image">
                                                    <input type="hidden" name="site_fav_icon" id="old_file"
                                                        value="{{ $siteLogo['site_fav_icon'] ?? '' }}">

                                                </div>
                                                <div class="preview-zone hidden">
                                                    <div class="box box-solid">
                                                        <div class="box-body">
                                                            @if (isset($siteLogo['site_fav_icon']) &&
                                                                    fileExists('theme-options', $siteLogo['site_fav_icon']) == true &&
                                                                    $siteLogo['site_fav_icon'] != '')
                                                                <div class="img-thumb-wrapper card shadow"> <span
                                                                        class="remove text-danger">
                                                                        <i class="bi bi-trash"></i> </span>
                                                                    <img class="img-thumb" width="100"
                                                                        src="{{ asset('storage/theme-options/' . $siteLogo['site_fav_icon']) }}" />
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-end mt-4">
                                        <button type="submit"
                                            class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="tab-pane fade footer-widget" id="theme-color" role="tabpanel"
                            aria-labelledby="v-pills-profile-tab">
                            <div class="eg-card product-card">
                                <form enctype="multipart/form-data" class="add_setting" method="POST"
                                    action="{{ route('admin.theme.setting') }}" data-key="general">
                                    @csrf
                                    <div class="row">

                                        <input type="hidden" class="username-input"
                                            value="{{ getThemeOption('general')['company_name'] ?? '' }}"
                                            name="company_name">
                                        <input type="hidden" class="username-input" name="company_address"
                                            value=" {{ getThemeOption('general')['company_address'] ?? '' }}">
                                        <input type="hidden" class="username-input" name="company_phone"
                                            value="{{ getThemeOption('general')['company_phone'] ?? '' }}">

                                        <div class="col-xl-6">
                                            <div class="form-inner mb-2"><label>{{ translate('Theme Color') }}</label>

                                                <input name="primary_color"
                                                    value="{{ getThemeOption('general')['primary_color'] ?? '#00A7AC' }}"
                                                    data-jscolor="{
                                                    preset: 'dark',
                                                    closeButton: true,
                                                    closeText: 'OK'
                                                    }">

                                            </div>
                                        </div>


                                    </div>
                                    <div class="text-end mt-4">
                                        <button type="submit"
                                            class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>


                        <div class="tab-pane fade footer-widget" id="page-setting" role="tabpanel"
                            aria-labelledby="v-pills-profile-tab">
                            <div class="eg-card product-card">
                                <form enctype="multipart/form-data" class="add_setting" method="POST"
                                    action="{{ route('admin.theme.setting') }}" data-key="page_setting">
                                    @csrf

                                    <div class="row">
                                        <div class="col-xl-6">
                                            <div class="form-inner mb-2">
                                                <label>{{ translate('Job List Url') }}<span></span></label><input
                                                    type="text" class="username-input"
                                                    value="{{ getThemeOption('page_setting')['job_list_url'] ?? '' }}"
                                                    name="job_list_url">
                                            </div>
                                        </div>
                                        <div class="col-xl-6">
                                            <div class="form-inner mb-2"> <label>{{ translate('Company List Url') }}
                                                </label>
                                                <input type="text" class="username-input" name="company_list_url"
                                                    value="{{ getThemeOption('page_setting')['company_list_url'] ?? '' }}">
                                            </div>
                                        </div>

                                        <div class="col-xl-6">
                                            <div class="form-inner mb-2">
                                                <label>{{ translate('Terms & Condition Page Url') }} </label>
                                                <input type="text" class="username-input" name="terms_condition_url"
                                                    value="{{ getThemeOption('page_setting')['terms_condition_url'] ?? '' }}">
                                            </div>
                                        </div>
                                        <div class="col-xl-6">
                                            <div class="form-inner mb-2">
                                                <label>{{ translate('Privacy & Policy Page Url') }} </label>
                                                <input type="text" class="username-input" name="privacy_url"
                                                    value="{{ getThemeOption('page_setting')['privacy_url'] ?? '' }}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-end mt-4">
                                        <button type="submit"
                                            class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>


                        <div class="tab-pane sub-tab-panel fade" id="header" role="tabpanel"
                            aria-labelledby="header-tab">

                            @php
                                $header = getThemeOption('header' . activeLanguage()) ?? 'headeren';
                                $header = $header ? $header : getThemeOption('headeren');
                            @endphp

                            <div class="eg-card product-card">
                                <form enctype="multipart/form-data" class="add_setting" method="POST"
                                    action="{{ route('admin.theme.setting') }}" data-key="header{{ activeLanguage() }}">
                                    @csrf
                                    <div class="row">
                                        <div class="card-header mb-4">{{ translate('Header Left Bar') }}
                                            {{ strtoupper(activeLanguage()) }}</div>
                                        <div class="col-sm-12">
                                            <div class="row">
                                                <div class="col-sm-3">
                                                    <label><b>{{ translate('Header Text') }}</b></label>
                                                </div>
                                                <div class="col-sm-9 form-inner mb-2">
                                                    <input type="text" class="username-input"
                                                        value="{{ $header['header-left-text'] ?? '' }}"
                                                        name="header-left-text">
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">

                                        <div class="card-header mb-4">{{ translate('Header Social Option') }}</div>

                                        <div class="col-sm-12">
                                            <div class="row">

                                                <div class="col-sm-3">
                                                    <label><b>{{ translate('Social') }}</b></label>
                                                </div>

                                                <div class="col-sm-9">
                                                    <div class="social-area" id="social_area_list">

                                                        @php
                                                            $count = 0;
                                                        @endphp

                                                        @if (isset($header['social']))
                                                            @foreach ($header['social'] as $key => $social)
                                                                @php
                                                                    $count++;
                                                                @endphp
                                                                <div class="row social-item">
                                                                    <div class="col-xl-3">
                                                                        <div class="form-inner mb-2"><label>
                                                                                {{ translate('Icon') }}
                                                                                {{ translate('Class') }}({{ translate('Bootstrap') }})
                                                                                <span></span></label>
                                                                            <input type="text" class="username-input"
                                                                                name="social[{{ $count }}][class]"
                                                                                value="{{ $social['class'] }}">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-xl-7">
                                                                        <div class="form-inner mb-2">
                                                                            <label> {{ translate('Social Url') }}
                                                                                <span></span></label>
                                                                            <input type="text" class="username-input"
                                                                                name="social[{{ $count }}][url]"
                                                                                value="{{ $social['url'] }}">
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-2 mt-4">
                                                                        <div
                                                                            class="d-flex align-items-center justify-content-between">
                                                                            <div class="add-row">
                                                                                <button type="button"
                                                                                    class="remove-work-area-row remove eg-btn btn--red back-btn"><i
                                                                                        class="bi bi-trash"></i></button>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        @endif
                                                    </div>

                                                    <div class="col-sm-2">
                                                        <div class="d-flex align-items-center justify-content-between">
                                                            <div class="add-row">
                                                                <button type="button"
                                                                    class="eg-btn add-social btn--primary back-btn"><i
                                                                        class="bi bi-plus-lg"></i></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>

                                    <div class="text-end mt-4">
                                        <button type="submit"
                                            class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                    </div>
                                </form>
                            </div>

                        </div>
                        <div class="tab-pane fade sub-tab-panel" id="footer" role="tabpanel"
                            aria-labelledby="footer-tab">


                            <!--================== theme-option-area sub panel start ==================== -->
                            <nav>
                                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                    <button class="nav-link active" id="footer-top-tab" data-bs-toggle="tab"
                                        data-bs-target="#footer-top" type="button" role="tab"
                                        aria-controls="footer-top" aria-selected="true"> {{ translate('Footer Top') }}
                                    </button>
                                    <button class="nav-link" id="footer-bottom-tab" data-bs-toggle="tab"
                                        data-bs-target="#footer-bottom" type="button" role="tab"
                                        aria-controls="footer-bottom"
                                        aria-selected="false">{{ translate('Footer Bottom') }}</button>
                                </div>
                            </nav>

                            <!--================== theme-option-area sub panel end  ==================== -->

                            <!--================== theme-option-area sub panel content start  ==================== -->

                            <div class="tab-content" id="nav-tabContent">
                                <div class="tab-pane fade show active" id="footer-top" role="tabpanel"
                                    aria-labelledby="footer-top-tab">
                                    <div class="eg-card product-card">
                                        @php
                                            $footerTop = getThemeOption('footer_top' . activeLanguage()) ?? null;
                                            $footerTop = $footerTop ? $footerTop : getThemeOption('footer_topen');
                                        @endphp
                                        <form enctype="multipart/form-data" class="add_setting" method="POST"
                                            action="{{ route('admin.theme.setting') }}"
                                            data-key="footer_top{{ activeLanguage() }}">
                                            @csrf
                                            <div class="row">
                                                <div class="col-xl-6">
                                                    <div class="form-inner mb-2">
                                                        <label><b>{{ translate('Footer widget 1') }} </b>
                                                            {{ strtoupper(activeLanguage()) }} </label>
                                                        <textarea name="footer_widget_one" class="description">
                                                            {!! clean($footerTop['footer_widget_one'] ?? '') !!}
                                                         </textarea>
                                                    </div>
                                                </div>
                                                <div class="col-xl-6">
                                                    <div class="form-inner mb-2">
                                                        <label><b>{{ translate('Footer widget 2') }}</b>
                                                            {{ strtoupper(activeLanguage()) }}</label>
                                                        <textarea name="footer_widget_two" class="description">
                                                            {!! clean($footerTop['footer_widget_two'] ?? '') !!}
                                                         </textarea>
                                                    </div>
                                                </div>
                                                <div class="col-xl-6">
                                                    <div class="form-inner mb-2">
                                                        <label><b>{{ translate('Footer widget 3') }} </b>
                                                            {{ strtoupper(activeLanguage()) }}</label>
                                                        <textarea name="footer_widget_three" class="description">
                                                            {!! clean($footerTop['footer_widget_three'] ?? '') !!}
                                                         </textarea>
                                                    </div>
                                                </div>
                                                <div class="col-xl-6">
                                                    <div class="form-inner mb-2">
                                                        <label><b>{{ translate('Footer widget 4') }}</b>
                                                            {{ strtoupper(activeLanguage()) }}</label>
                                                        <textarea name="footer_widget_four" class="description">
                                                            {!! clean($footerTop['footer_widget_four'] ?? '') !!}
                                                         </textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-end mt-4">
                                                <button type="submit"
                                                    class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <div class="tab-pane fade active" id="footer-bottom" role="tabpanel"
                                    aria-labelledby="footer-bottom-tab">

                                    @php
                                        $footerBottom = $footerBottom ?? null;
                                        $footerBottom = $footerBottom
                                            ? $footerBottom
                                            : getThemeOption('footer_bottomen');
                                    @endphp
                                    <div class="eg-card product-card">
                                        <form enctype="multipart/form-data" class="add_setting" method="POST"
                                            action="{{ route('admin.theme.setting') }}"
                                            data-key="footer_bottom{{ activeLanguage() }}">
                                            @csrf

                                            <div class="row">
                                                <div class="card-header mb-4">{{ translate('Left Area') }}</div>
                                                <div class="col-sm-12">
                                                    <div class="row">
                                                        <div class="col-sm-3">
                                                            <label><b>{{ translate('Left Text') }}</b>
                                                                {{ strtoupper(activeLanguage()) }}</label>
                                                        </div>
                                                        <div class="col-sm-9 form-inner mb-2">
                                                            <div class="form-inner mb-2">
                                                                <textarea name="hot_line" class="description">
                                                                    {!! clean($footerBottom['hot_line'] ?? '') !!}
                                                                    </textarea>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-sm-3">
                                                            <label><b>{{ translate('Copy Right') }}
                                                                    {{ strtoupper(activeLanguage()) }}</b></label>
                                                        </div>
                                                        <div class="col-sm-9 form-inner mb-2">
                                                            <div class="form-inner mb-2">
                                                                <textarea name="copy_right_text" class="description">
                                                                      {!! clean($footerBottom['copy_right_text'] ?? '') !!}
                                                                    </textarea>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="card-header mb-4">{{ translate('Right Area') }}</div>


                                                <div class="col-sm-12">
                                                    <div class="row">
                                                        <div class="col-sm-3">
                                                            <label><b>{{ translate('Footer Logo') }}</b></label>
                                                        </div>
                                                        <div class="col-sm-9 form-inner mb-2">
                                                            <div class="form-inner file-upload mb-35">
                                                                <div class="dropzone-wrapper">
                                                                    <div class="dropzone-desc">
                                                                        <i class="glyphicon glyphicon-download-alt"></i>
                                                                        <p>{{ translate('Choose an image file or drag it here') }}
                                                                        </p>
                                                                    </div>
                                                                    <input type="file"
                                                                        class="dropzone theme-option-image">
                                                                    <input type="hidden" name="footer_logo"
                                                                        id="old_file"
                                                                        value="{{ $footerBottom['footer_logo'] ?? '' }}">

                                                                </div>
                                                                <div class="preview-zone hidden">
                                                                    <div class="box box-solid">
                                                                        <div class="box-body">

                                                                            @if (isset($footerBottom['footer_logo']) &&
                                                                                    fileExists($folder = 'theme-options', $fileName = $footerBottom['footer_logo']) == true &&
                                                                                    $footerBottom['footer_logo'] !== '')
                                                                                <div class="img-thumb-wrapper card shadow">
                                                                                    <span class="remove text-danger">
                                                                                        <i class="bi bi-trash"></i>
                                                                                    </span>
                                                                                    <img class="img-thumb" width="100"
                                                                                        src="{{ asset('storage/theme-options/' . $footerBottom['footer_logo']) }}" />
                                                                                </div>
                                                                            @endif

                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-sm-12">
                                                    <div class="row">
                                                        <div class="col-sm-3">
                                                            <label><b> {{ translate('Allow Footer Menu') }} ?</b></label>
                                                        </div>
                                                        <div class="col-sm-9 form-inner mb-2">
                                                            <div class="form-inner mb-2">
                                                                <div
                                                                    class="form-check form-switch candidate-option justify-content-start">
                                                                    <input class="form-check-input"
                                                                        name="is_footer_menu"{{ isset($footerBottom['is_footer_menu']) == 'on' ? 'checked' : '' }}
                                                                        type="checkbox" id="is_footer_menu">
                                                                    <label class="form-check-label ml-1"
                                                                        for="is_footer_menu"> </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>


                                                <div class="col-sm-12">
                                                    <div class="row">
                                                        <div class="col-sm-3">
                                                            <label><b> {{ translate('Allow Social Option') }} ?</b></label>
                                                        </div>
                                                        <div class="col-sm-9 form-inner mb-2">
                                                            <div class="form-inner mb-2">
                                                                <div
                                                                    class="form-check form-switch candidate-option justify-content-start">
                                                                    <input class="form-check-input"
                                                                        name="is_footer_social"{{ isset($footerBottom['is_footer_social']) == 'on' ? 'checked' : '' }}
                                                                        type="checkbox" id="is_footer_social">
                                                                    <label class="form-check-label ml-1"
                                                                        for="is_footer_social"> </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                            <div class="text-end mt-4">
                                                <button type="submit"
                                                    class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <!--================== theme-option-area sub panel content end  ==================== -->


                        </div>

                        <div class="tab-pane fade footer-widget" id="breadcrumb" role="tabpanel"
                            aria-labelledby="v-pills-profile-tab">
                            <div class="eg-card product-card">

                                @php
                                    $breadcrumb = getThemeOption('breadcrumb') ?? null;
                                    $breadcrumb = $breadcrumb ? $breadcrumb : getThemeOption('breadcrumb');
                                @endphp
                                <form enctype="multipart/form-data" class="add_setting" method="POST"
                                    action="{{ route('admin.theme.setting') }}" data-key="breadcrumb">
                                    @csrf
                                    <div class="row">
                                        <div class="col-xl-6">
                                            <div class="form-inner file-upload mb-35">
                                                <label
                                                    class="control-label">{{ translate('Breadcrumb Image') }}</span></label>
                                                <div class="dropzone-wrapper">
                                                    <div class="dropzone-desc">
                                                        <i class="glyphicon glyphicon-download-alt"></i>
                                                        <p>{{ translate('Choose an image file or drag it here') }}</p>
                                                    </div>
                                                    <input type="file" class="dropzone theme-option-image">
                                                    <input type="hidden" name="breadcrumb_img" id="old_file"
                                                        value="{{ $breadcrumb['breadcrumb_img'] ?? null }}">
                                                </div>
                                                <div class="preview-zone hidden">
                                                    <div class="box box-solid">
                                                        <div class="box-body">

                                                            @if (isset($breadcrumb['breadcrumb_img']) &&
                                                                    fileExists($folder = 'theme-options', $fileName = $breadcrumb['breadcrumb_img']) == true &&
                                                                    $breadcrumb['breadcrumb_img'] !== '')
                                                                <div class="img-thumb-wrapper card shadow"> <span
                                                                        class="remove text-danger">
                                                                        <i class="bi bi-trash"></i> </span>
                                                                    <img class="img-thumb" width="100"
                                                                        src="{{ asset('storage/theme-options/' . $breadcrumb['breadcrumb_img']) }}" />
                                                                </div>
                                                            @endif

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="text-end mt-4">
                                        <button type="submit"
                                            class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="tab-pane fade footer-widget" id="google_analytics" role="tabpanel"
                            aria-labelledby="v-pills-profile-tab">
                            <div class="eg-card product-card">
                                <form enctype="multipart/form-data" class="add_setting" method="POST"
                                    action="{{ route('admin.theme.setting') }}" data-key="google_analytics">
                                    @csrf
                                    <div class="row">
                                        <div class="col-xl-12">
                                            <div class="form-inner mb-35">
                                                <label> {{ translate('Measurement') }} {{ translate('ID') }} /
                                                    {{ translate('Tracking') }} {{ translate('ID') }}</label>
                                                <input type="text" class="form-control"
                                                    value="{{ isset(getThemeOption('google_analytics')['analytics_id']) ? getThemeOption('google_analytics')['analytics_id'] : '' }}"
                                                    name="analytics_id"
                                                    placeholder=" {{ translate('Measurement') }} {{ translate('ID') }} / {{ translate('Tracking') }} {{ translate('ID') }}">
                                            </div>
                                        </div>

                                    </div>
                                    <div class="text-end mt-4">
                                        <button type="submit"
                                            class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="tab-pane fade footer-widget" id="cookie" role="tabpanel"
                            aria-labelledby="v-pills-profile-tab">
                            <div class="eg-card product-card">
                                @php
                                    $cookie = getThemeOption('cookie' . activeLanguage()) ?? null;
                                    $cookie = $cookie ? $cookie : getThemeOption('cookieen');
                                @endphp

                                <form enctype="multipart/form-data" class="add_setting" method="POST"
                                    action="{{ route('admin.theme.setting') }}"
                                    data-key="cookie{{ activeLanguage() }}">
                                    @csrf
                                    <div class="row">
                                        <div class="col-xl-4 mb-35">
                                            <div class="row">
                                                <label class="col-sm-2"><b>{{ translate('Enabled') }}/
                                                        {{ translate('Disabled') }}</b></label>
                                                <div class="form-check form-switch col-sm-10">
                                                    <input class="form-check-input"
                                                        {{ isset($cookie['gdpr_cookie_enabled']) == 'on' ? 'checked' : '' }}
                                                        name="gdpr_cookie_enabled" type="checkbox">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-sm-12">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div class="form-inner mb-35">
                                                        <label>{{ translate('Cookie Title') }}
                                                            {{ strtoupper(activeLanguage()) }} </label>
                                                        <input type="text" class="form-control" name="gdpr_title"
                                                            value="{{ $cookie['gdpr_title'] ?? '' }}"
                                                            placeholder="{{ translate('Cookie Title') }}">
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div class="col-sm-12">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div class="form-inner mb-35">
                                                        <label> {{ translate('Description') }}
                                                            {{ strtoupper(activeLanguage()) }} </label>
                                                        <textarea class="description" name="gdpr_description">{!! clean($cookie['gdpr_description'] ?? '') !!}</textarea>

                                                    </div>
                                                </div>

                                            </div>
                                        </div>


                                    </div>
                                    <div class="text-end mt-4">
                                        <button type="submit"
                                            class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="tab-pane fade footer-widget" id="tawk-chat" role="tabpanel"
                            aria-labelledby="v-pills-profile-tab">
                            <div class="eg-card product-card">
                                @php
                                    $tawk = getThemeOption('tawk') ?? null;
                                    $tawk = $tawk ? $tawk : getThemeOption('tawk');
                                @endphp
                                <form enctype="multipart/form-data" class="add_setting" method="POST"
                                    action="{{ route('admin.theme.setting') }}" data-key="tawk">
                                    @csrf
                                    <div class="row">
                                        <div class="col-xl-4 mb-35">
                                            <div class="row">
                                                <label
                                                    class="col-sm-2"><b>{{ translate('Enabled') }}/{{ translate('Disabled') }}</b></label>
                                                <div class="form-check form-switch col-sm-10">
                                                    <input class="form-check-input" name="tawk_enabled"type="checkbox"
                                                        id="flexSwitchCheckProduct18"
                                                        {{ isset($tawk['tawk_enabled']) == 'on' ? 'checked' : '' }}>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="col-xl-12">
                                            <label class="col-sm-2 mb-2"><b>{{ translate('Tawk Embed Url') }}</b></label>
                                            <div class="form-inner mb-35">
                                                <input type="text" value="{{ $tawk['tawk_code'] ?? null }}"
                                                    name="tawk_code">
                                            </div>
                                            <p> <a href="https://www.tawk.to/" target="_blank">
                                                    <b>{{ translate('Go to Tawk') }}</b> </a></p>

                                        </div>
                                    </div>
                                    <div class="text-end mt-4">
                                        <button type="submit"
                                            class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>





                        <div class="tab-pane fade sub-tab-panel" id="custom-script" role="tabpanel"
                            aria-labelledby="custom-script-tab">


                            <!--================== theme-option-area sub panel start ==================== -->
                            <nav>
                                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                    <button class="nav-link active" id="custom-css-tab" data-bs-toggle="tab"
                                        data-bs-target="#custom-css" type="button" role="tab"
                                        aria-controls="custom-css"
                                        aria-selected="true">{{ translate('Custom CSS') }}</button>
                                    <button class="nav-link" id="custom-js-tab" data-bs-toggle="tab"
                                        data-bs-target="#custom-js" type="button" role="tab"
                                        aria-controls="custom-js"
                                        aria-selected="false">{{ translate('Custom JS') }}</button>
                                </div>
                            </nav>

                            <!--================== theme-option-area sub panel end  ==================== -->

                            <!--================== theme-option-area sub panel content start  ==================== -->

                            @can('themeoption.script')
                                <div class="tab-content" id="nav-tabContent">
                                    <div class="tab-pane fade show active" id="custom-css" role="tabpanel"
                                        aria-labelledby="custom-css-tab">
                                        <div class="eg-card product-card">
                                            <form enctype="multipart/form-data" class="add_setting" method="POST"
                                                action="{{ route('admin.theme.setting') }}" data-key="custom_css">
                                                @csrf
                                                <div class="form-inner mb-2">
                                                    <textarea name="custom_css" class="editorContainer">{!! getThemeOption('custom_css')['custom_css'] ?? null !!}</textarea>
                                                </div>
                                                <div class="text-end mt-4">
                                                    <button type="submit"
                                                        class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade active" id="custom-js" role="tabpanel"
                                        aria-labelledby="custom-js-tab">
                                        <div class="eg-card product-card">
                                            <form enctype="multipart/form-data" class="add_setting" method="POST"
                                                action="{{ route('admin.theme.setting') }}" data-key="custom_js">
                                                @csrf
                                                <div class="form-inner mb-2">
                                                    <textarea name="custom_js" class="editorContainer">{!! getThemeOption('custom_js')['custom_js'] ?? '' !!}</textarea>
                                                </div>
                                                <div class="text-end mt-4">
                                                    <button type="submit"
                                                        class="eg-btn btn--green btn shadow  me-3">{{ translate('Save') }}</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @endcan
                            <!--================== theme-option-area sub panel content end  ==================== -->


                        </div>
                    </div>
                    <!--================== theme-option-area panel content end ==================== -->

                    <!--================== theme-option-area panel end ==================== -->


                </div>

            </div>

            <!--================== theme-option-area end ==================== -->
        </div>
    </div>
@endsection

@push('post_scripts')
    @include('js.admin.them-optiopn');
@endpush
