<?php

namespace App\Http\Controllers\Backend;

use App\Classes\FileUpload;
use App\Http\Controllers\Controller;
use App\Http\Requests\PageRequest;
use App\Models\Admin\Pages;
use App\Models\Admin\WidgetContentTranslation;
use App\Models\Admin\Widgets;
use App\Models\Admin\WidgetsContents;
use App\Repositories\Admin\PagesRepostitory;
use Illuminate\Http\Request;

class PagesController extends Controller
{
    public function __construct(protected PagesRepostitory $pages, protected FileUpload $file) {}

    /** all  resource get
     * ======= index ========
     *
     * @param Request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->pages->content($request);
        }
        return view('admin.pages.pages.index');
    }

    /** new post store
     * ========= store============
     *
     * @param App\Http\Requests\PageRequest
     * @return Response
     */
    public function store(PageRequest $request)
    {

        if (!$this->hasPermissions(['page.add'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->pages->create($request);
        return $this->formatResponse($result);
    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $page = Pages::with('widgetContents.widget')->find($id);
        $widgetList = Widgets::orderBy('id', 'asc')->get();
        $lang = Request()->lang;
        return view('admin.pages.pages.edit', compact('page', 'widgetList', 'lang'));
    }

    /** specific resource update by id
     * ========= update ===========
     *
     * @param App\Http\Requests\PageRequest
     * @return Response
     */
    public function update(Request $request)
    {
        if (!$this->hasPermissions(['page.edit'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->pages->update($request->id, $request);
        return $this->formatResponse($result);

    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['page.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->pages->destroy($id);
        return $this->formatResponse($result);
    }

    /** status change
     * ======= statusChange ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['page.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->pages->statusChange($id);
        return $this->formatResponse($result);

    }

    /** =========== widget added to page ===========
     * =========== widgetAddedToPage ===========
     *
     * @param string slug (widget_slug)
     * @return Response
     */
    public function widgetAddedToPage($slug)
    {
        if (!$this->hasPermissions(['widget.add'])) {
            return $this->permissionDeniedResponse();
        }
        $pageId = request()->pageId;
        $widgetName = request()->widgetName;
        $randomId = substr(md5(mt_rand()), 0, 7);
        $content = view("admin.pages.widgets.$slug", compact('randomId', 'pageId', 'slug', 'widgetName'))->render();
        $this->storeWidgetByPage($pageId, $randomId, $slug);
        return response()->json([
            'content' => $content,
            'pageId' => $pageId,
            'status' => true,
            'message' => translate('Save Successfully')
        ]);

    }

    /** widget update by page
     * ===========  widgetUpdateByPage =============
     *
     * @param mix Request
     * @return Response
     */
    public function widgetUpdateByPage(Request $request)
    {

        try {

            if (!$this->hasPermissions(['widget.edit'])) {
                return $this->permissionDeniedResponse();
            }
            $widgetsContent = WidgetsContents::where('ui_card_number', $request->ui_card_number)
            ->where('widget_slug', $request->widget_slug)
            ->first();

            if ($request->lang == default_language() || $request->lang == '') {
                $widgetsContent->ui_card_number = $request->ui_card_number;
                $widgetsContent->widget_slug = $request->widget_slug;
                $widgetsContent->page_id = $request->page_id;
                $widgetsContent->widget_content = $request->content[0];
                $widgetsContent->update();
            } else {
                $this->widgetTranslate($widgetsContent->id, $request);
            }
            return response()->json([
                'status' => true,
                'message' => translate('Update Successfully')
            ]);

        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

    /** status change
     * ======= statusChange ========
     *
     * @param  int id
     * @return Response
     */
    public function widgetStatusChange($id)
    {
        try {
            if (!$this->hasPermissions(['widget.status'])) {
                return $this->permissionDeniedResponse();
            }

            $widget = WidgetsContents::where('id', $id)
                ->orWhere('ui_card_number', $id)
                ->first();

            if ($widget) {
                $widget->update(['status' => !$widget->status]);
                return response()->json([
                    'status' => true,
                    'message' => translate('Status changed successfully')
                ]);
            }
            return response()->json([
                'status' => false,
                'message' => translate('Widget not found')
            ]);

        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => translate('Something Wrong!')
            ]);
        }
    }

    /** widget delete by page
     * ======= statusChange ========
     *
     * @param  int id
     * @return Response
     */
    public function widgetDeleteByPage($id)
    {
        try {

            if (!$this->hasPermissions(['widget.delete'])) {
                return $this->permissionDeniedResponse();
            }

            $widget = WidgetsContents::where('id', $id)
                ->orWhere('ui_card_number', $id)
                ->first();

            if (!$widget) {
                return response()->json([
                    'status' => false,
                    'message' => translate('Widget not found')
                ]);
            }

            $widget->delete();
            return response()->json([
                'status' => true,
                'message' => translate('Widget deleted successfully')
            ]);

        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => translate('Something Wrong!')]
            );
        }
    }

    /** sorted widget
     *======== widgetSortedByPage ==============
     *
     * @param Request
     * @return Response
     */
    public function widgetSortedByPage(Request $request)
    {

        try {

            if (!$this->hasPermissions(['widget.edit'])) {
                return $this->permissionDeniedResponse();
            }
            if (!isset($request->content[0])) {
                return response()->json([
                    'status' => false,
                    'message' => translate('Invalid data')
                ]);
            }
            $count = 0;
            foreach ($request->content[0] as $key => $code) {
                $count++;
                $widgetContent = WidgetsContents::where('ui_card_number', $code)
                    ->where('widget_slug', $key)
                    ->first();

                if ($widgetContent) {
                    $widgetContent->update(['position' => $count]);
                } else {
                    $this->storeWidgetByPage($request->pageId, $code, $key);
                }
            }

            return response()->json([
                'status' => true,
                'message' => translate('Updated Successfully')
            ]);

        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => translate('Something Wrong!')
            ]);
        }
    }

    /** store widget to page
     * =========== storeWidgetByPage ============
     *
     * @param int PageId
     * @param string randomId
     * @param  string slug {widget slug}
     * @return Response
     */
    public function storeWidgetByPage($pageId, $randomId, $slug)
    {

        $widget = WidgetsContents::where('page_id', $pageId)->select('position')->orderBy('position', 'DESC')->latest()->first();
        $widgetsContent = new WidgetsContents;
        $widgetsContent->ui_card_number = $randomId;
        $widgetsContent->widget_slug = $slug;
        $widgetsContent->page_id = $pageId;
        $widgetsContent->position = $widget ? $widget->position += 1 : 1;
        $widgetsContent->save();
    }

    /**  widget tranlsate
     *
     *============ translate ============
     *
     * @param request
     * @return response
     */
    public function widgetTranslate($id, $request)
    {
        WidgetContentTranslation::updateOrCreate(['widget_content_id' => $id, 'lang' => $request->lang], [
            'page_id' => $request->page_id,
            'widget_content_id' => $id,
            'widget_content' => isset($request->content[0]) ? $request->content[0] : null,
        ]);
    }
}
