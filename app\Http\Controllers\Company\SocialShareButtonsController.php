<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;

class SocialShareButtonsController extends Controller
{
    public function ShareWidget()
    {
        $currenturl = url()->full();

        $shareComponent = \Share::page(
            $currenturl
        )
            ->facebook()
            ->twitter()
//            ->telegram()
//            ->reddit()
//            ->whatsapp()
            ->linkedin();

        return response()->json([
            'status' => true,
            'data' => (string) view('front.pages.company.posts', compact('shareComponent')),
        ]);
    }
}
