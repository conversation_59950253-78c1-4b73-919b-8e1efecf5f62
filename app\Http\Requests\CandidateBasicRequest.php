<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class CandidateBasicRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            //

            'candidate_image' => Request()->candidate_image ? 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2048' : '',
            'candidate_designation' => Request()->prefix == 'basic_information' ? 'required' : '',
            'candidate_experience' => Request()->prefix == 'basic_information' ? 'required' : '',
            'candidate_qualification' => Request()->prefix == 'basic_information' ? 'required' : '',
            // 'candidate_current_salary' => Request()->prefix=='basic_information' ?  'required|int' :"",
            'candidate_expected_salary' => Request()->prefix == 'basic_information' ? 'required|int' : '',
            'candidate_nationality' => Request()->prefix == 'basic_information' ? 'required' : '',
            'candidate_phone_number' => Request()->prefix == 'basic_information' ? 'required' : '',
            'candidate_available_time' => Request()->prefix == 'basic_information' ? 'required' : '',

            'candidate_father_name' => Request()->prefix == 'profile_information' ? 'required' : '',
            'candidate_mother_name' => Request()->prefix == 'profile_information' ? 'required' : '',
            'candidate_dob' => Request()->prefix == 'profile_information' ? 'required' : '',
            'candidate_national_id' => Request()->prefix == 'profile_information' ? 'required' : '',
            'candidate_present_address' => Request()->prefix == 'profile_information' ? 'required' : '',
            'candidate_marital_status' => Request()->prefix == 'profile_information' ? 'required' : '',
            'candidate_gender' => Request()->prefix == 'profile_information' ? 'required' : '',
            'candidate_religion' => Request()->prefix == 'profile_information' ? 'required' : '',
            'candidate_skills' => Request()->prefix == 'profile_information' ? (Request()->has_skill == 'yes' ? '' : 'required') : '',

        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [

            'candidate_image.required' => 'Candidate image field is required',
            'candidate_designation.required' => 'Candidate designation field is required',
            'candidate_experience.required' => 'Candidate experience field is required',
            'candidate_qualification.required' => 'Candidate qualification field is required',
            'candidate_current_salary.required' => 'Candidate Current Salary field is required',
            'candidate_expected_salary.required' => 'Candidate Expected Salary field is required',
            'candidate_current_salary.integer' => 'Candidate Current Salary must be Integer',
            'candidate_expected_salary.integer' => 'Candidate Expected Salary must be Integer',

            'candidate_image.image' => 'Company Logo will be must image',
            'candidate_image.mimes' => 'Company Logo Image type will be jpeg,png,jpg,gif,svg',
            'candidate_image.max' => 'Company Logo Image will be less than 2 MB',

            'candidate_father_name.required' => 'Candidate Father Name field is required',
            'candidate_mother_name.required' => 'Candidate Mother Name field is required',
            'candidate_dob.required' => 'Candidate Date Of Birth field is required',
            'candidate_national_id.required' => 'Candidate National Id field is required',
            'candidate_present_address.required' => 'Candidate Present Address field is required',
            'candidate_marital_status.required' => 'Candidate Marital Status field is required',
            'candidate_gender.required' => 'Candidate Gender field is required',
            'candidate_religion.required' => 'Candidate Religion field is required',
            'candidate_skills.required' => 'Candidate Skill field is required',

        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => false,
            'errors' => $validator->errors(),
        ]));
    }
}
