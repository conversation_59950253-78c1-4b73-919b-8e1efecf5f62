<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\MenuItemRequest;
use App\Models\Admin\Blog;
use App\Models\Admin\Category;
use App\Models\Admin\Menu;
use App\Models\Admin\MenuItem;
use App\Models\Admin\MenuItemTranslation;
use App\Models\Admin\Pages;
use Illuminate\Http\Request;

class MenuController extends Controller
{
    /**
     * menu
     *
     * @param  mixed  $request
     * @return View
     */
    public function menu(Request $request)
    {
        $categories = Category::get();
        $pages = Pages::get();
        $menus = Menu::get();
        $blogs = Blog::get();
        $selectedMenu = 0;
        $itemsWithChildrens = '';
        if (isset($request->id)) {
            $itemsWithChildrens = $this->getChildrens($request->id);
            $selectedMenu = $request->id;
        }

        return view('admin.pages.menu.index', compact('categories', 'pages', 'menus', 'blogs', 'selectedMenu', 'itemsWithChildrens'));
    }

    /**
     * addToMenu
     *
     * @param  mixed  $request
     * @return Response
     */
    public function addToMenu(Request $request)
    {
        try {

            if (!$this->hasPermissions(['menu.add'])) {
                return $this->permissionDeniedResponse();
            }
            $menuId = $request->menuId;
            if ($request->type == 'custom') {
                $menuItem = MenuItem::where('menu_id', $menuId)->max('order');
                $data['title'] = $request->custom_name;
                $data['slug'] = slug($request->custom_name);
                $data['menu_type'] = $request->type;
                $data['menu_id'] = $menuId;
                $data['custom_link'] = $request->custom_link;
                $data['new_tap'] = $request->new_tap == 'on' ? 1 : 0;
                $data['order'] = $menuItem ? $menuItem += 1 : 1;
                MenuItem::create($data);
            } else {
                $ids = $request->ids;
                foreach ($ids as $id) {
                    $item = $this->getItem($request->type, $id);
                    $menuItem = MenuItem::where('menu_id', $menuId)->max('order');
                    $data['title'] = $item['title'];
                    $data['slug'] = $item['slug'];
                    $data['slug'] = $item['slug'];
                    $data['menu_item_name'] = $item['title'];
                    $data['menu_type'] = $request->type;
                    $data['menu_id'] = $menuId;
                    $data['order'] = $menuItem ? $menuItem += 1 : 1;
                    $data['page_route'] = $item['page_route'];
                    MenuItem::create($data);
                }
            }

           $message= [
                'status' => true,
                'message' => translate('Add  Successfully')
            ];
            return $this->formatResponse($message);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

    /**
     * getItem
     *
     * @param  string  $type
     * @param  int  $id
     * @return Response
     */
    public function getItem($type, $id)
    {
        $data = [];
        switch ($type) {
            case 'category':
                $category = Category::where('id', $id)->select('id', 'category_name', 'category_slug')->first();
                $data['title'] = $category->category_name;
                $data['slug'] = $category->category_slug;
                $data['page_route'] = $category->category_slug;

                break;
            case 'blog':

                $blog = Blog::where('id', $id)->select('blog_title', 'slug')->first();
                $data['title'] = $blog->blog_title;
                $data['slug'] = $blog->slug ? $blog->slug : slug($blog->blog_title);
                $data['page_route'] = $blog->slug ? $blog->slug : slug($blog->blog_title);

                break;

            case 'page':

                $page = Pages::where('id', $id)->select('page_name', 'page_slug', 'page_route')->first();
                $data['title'] = $page->page_name;
                $data['slug'] = $page->page_slug;
                $data['page_route'] = $page->page_route;
                break;
        }

        return $data;
    }

    /**
     * @param MenuItemRequest
     */
    public function updateMenuItem(MenuItemRequest $request)
    {
        try {

            if (!$this->hasPermissions(['menu.edit'])) {
                return $this->permissionDeniedResponse();
            }
            $menuItem = MenuItem::where('id', $request->id)->first();
            if ($request->lang == default_language() || $request->lang == '') {
                $menuItem->update([
                    'title' => $request->title,
                    'slug' => slug($request->title),
                    'target' => $request->custom_link,
                    'new_tap' => $request->new_tap == 'on' ? 1 : 0,

                ]);
            } else {
                $this->translate($menuItem->id, $request);
            }
            return response()->json([
                'status' => true,
                'message' => translate('Update Successfully'),
                'menu' => 'menu'
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => translate('Something Wrong!')
            ]);
        }
    }

    /**
     * deleteMenuItem
     *
     * @param  int  $id
     * @return Response
     */
    public function deleteMenuItem($id)
    {
        try {

            if (!$this->hasPermissions(['menu.delete'])) {
                return $this->permissionDeniedResponse();
            }
            $menuitem = MenuItem::with('childrens')->findOrFail($id);
            if ($menuitem) {
                $menuitem->childrens()->delete();
                $menuitem->delete();
                $message= [
                    'status' => true,
                    'message' => translate('Delete  Successfully')
                ];
                return $this->formatResponse($message);
            }

        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => translate('Something Wrong!')
            ]);
        }
    }

    /**
     * storeMenuItem
     *
     * @param  mixed  $request
     * @return Response
     */
    public function storeMenuItem(Request $request)
    {
        try {

            if (!$this->hasPermissions(['menu.edit'])) {
                return $this->permissionDeniedResponse();
            }
            $array_menu = json_decode($request->menuItems, true);
            $count = 1;
            foreach ($array_menu as $value) {
                $menuItem = MenuItem::find($value['id']);
                $menuItem->order = $count++;
                $menuItem->parent_id = null;
                $menuItem->update();
                if (array_key_exists('children', $value)) {
                    $this->childMenu($value['children'], $menuItem->id);
                }
            }
            $message= [
                'status' => true,
                'message' => translate('Update  Successfully')
            ];
            return $this->formatResponse($message);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => translate('Something Wrong!')
            ]);
        }
    }

    /**
     * editMenuItem
     *
     * @param  int  $id
     */
    public function editMenuItem($id)
    {
        $menuItem = MenuItem::find($id);
        $title = $menuItem->getTranslation('title');
        return response([
            'status' => true,
            'menuItem' => $menuItem,
            'title' => $title
        ]);
    }

    /**
     * childMenu
     *
     * @param  mixed  $childrens
     * @param  int  $parentId
     */
    public function childMenu($childrens, $parentId)
    {
        $count = 1;
        foreach ($childrens as $children) {
            $menuItem = MenuItem::find($children['id']);
            $menuItem->order = $count++;
            $menuItem->parent_id = $parentId;
            $menuItem->update();
            if (array_key_exists('children', $children)) {
                $this->childMenu($children['children'], $menuItem->id);
            }
        }
    }

    /**
     * getChildrens
     *
     * @param  int  $menu_id
     * @param  int  $parent_id
     * @param  string  $orderBy
     */
    public function getChildrens($menu_id, $parent_id = null, $orderBy = 'asc')
    {
        return MenuItem::with('childrens')
            ->where(['menu_id' => $menu_id, 'parent_id' => $parent_id])
            ->orderBy('order', $orderBy)
            ->get();
    }

    /**  menu tranlsate
     *
     *============ translate ============
     *
     * @param request
     * @return response
     */
    public function translate($id, $request)
    {
        return MenuItemTranslation::updateOrCreate(['menu_item_id' => $id, 'lang' => $request->lang], [
            'title' => $request->title,
            'lang' => $request->lang,
        ]);
    }
}
