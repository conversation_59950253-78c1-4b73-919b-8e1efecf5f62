<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\JobSkillRequest;
use App\Repositories\Admin\JobSkillRepostitory;
use Illuminate\Http\Request;

class JobSkillController extends Controller
{
    //

    public function __construct(protected JobSkillRepostitory $jobSkill) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->jobSkill->content();
        }

        return view('admin.pages.skill.index');
    }

    /** new Job Sub Category store
     * ========= store============
     *
     * @param JobSkillRequest
     * @return Response
     */
    public function store(JobSkillRequest $request)
    {

        if (auth()->user()->hasRole('company')) {
            $result = $this->jobSkill->create($request);
            return $this->formatResponse($result);
        }
        if (!$this->hasPermissions(['jobskill.add', 'jobskill.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->jobSkill->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $jobSkill = $this->jobSkill->getById($id);

        $data['name'] = $jobSkill->getTranslation('name', Request()->lang);
        $data['id'] = $jobSkill->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['jobskill.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->jobSkill->destroy($id);
        return $this->formatResponse($result);

    }

    /** Job Type status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {

        if (!$this->hasPermissions(['jobskill.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jobSkill->statusChange($id);
        return $this->formatResponse($result);

    }

    /**  Job Skill search
     * ========= roleSearch=======
     *
     * @param Request
     */
    public function skillSearch(Request $request)
    {

        if ($request->q && $request->q != '') {
            $result = $this->jobSkill->skillSearch($request->q);
            if ($result['status'] == true) {
                return response()->json(['items' => $result['skils']]);
            }
        }
    }
}
