<?php

namespace App\Exports;

use App\Models\Front\ApplyJob;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class AppliedUserListExport implements FromCollection, WithHeadings
{
    /**
     * @return \Illuminate\Support\Collection
     */
    protected $jobId;

    public function __construct($jobId)
    {
        $this->jobId = $jobId;
    }

    public function collection()
    {
        return ApplyJob::whereIn('job_id', [$this->jobId])->with('user', 'candidate', 'job')
            ->select()
            ->get([
                'company_id' => 'company_id',
                'candidate_id' => 'candidate_id',
                'job_id' => 'job_id',
            ])->map(function ($appliedUser) {
                return [
                    'id' => $appliedUser->id,
                    'candidate_name' => $appliedUser->user->first_name.' '.$appliedUser->user->last_name,
                    'email' => $appliedUser->user->email,
                    'candidate_phone_number' => $appliedUser->candidate->candidate_phone_number,
                    'candidate_secondary_phone_number' => $appliedUser->candidate->candidate_secondary_phone_number,
                    'candidate_designation' => $appliedUser->candidate->candidate_designation,
                    'candidate_experience' => $appliedUser->candidate->candidate_experience,
                    'candidate_qualification' => $appliedUser->candidate->candidate_qualification,
                    'candidate_present_address' => $appliedUser->candidate->candidate_present_address,
                    'candidate_gender' => $appliedUser->candidate->candidate_gender,
                    'candidate_current_salary' => $appliedUser->candidate->candidate_current_salary,
                    'candidate_expected_salary' => $appliedUser->candidate->candidate_expected_salary,
                    'candidate_available_time' => $appliedUser->candidate->candidate_available_time,
                ];
            });
    }

    public function headings(): array
    {
        return [
            '#',
            'Candidate Nname',
            'Email',
            'phone Number',
            'Secondary Phone Number',
            'Designation',
            'Experience',
            'Qualification',
            'Present address',
            'Gender',
            'Current Salary',
            'Expected Salary',
            'Available Time',
        ];
    }
}
