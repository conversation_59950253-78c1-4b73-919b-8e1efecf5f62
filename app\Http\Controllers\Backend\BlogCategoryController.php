<?php

namespace App\Http\Controllers\Backend;
use App\Http\Controllers\Controller;
use App\Http\Requests\BlogCategoryRequest;
use App\Repositories\Admin\BlogCategoryRepostitory;
use Illuminate\Http\Request;

class BlogCategoryController extends Controller
{
    public function __construct(protected BlogCategoryRepostitory $blogCategory) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->blogCategory->content();
        }

        return view('admin.pages.blog.category.index');
    }

    /** new Education store
     * ========= store============
     *
     * @param App\Http\Requests\BlogCategoryRequest
     * @return Response
     */
    public function store(BlogCategoryRequest $request)
    {

        if (!$this->hasPermissions(['blogcategory.add', 'blogcategory.edit'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->blogCategory->create($request);
        return $this->formatResponse($result);
    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {
        $blogCategory = $this->blogCategory->getById($id);
        $data['name'] = $blogCategory->getTranslation('blog_category_name', Request()->lang);
        $data['id'] = $blogCategory->id;
        $data['lang'] = Request()->lang;
        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['blogcategory.delete'])) {
            $this->permissionDeniedResponse();
        }
        $result = $this->blogCategory->destroy($id);
        return $this->formatResponse($result);
    }

    /** status change
     * ======= statusChange ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['blogcategory.status'])) {
           return $this->permissionDeniedResponse();
        }
        $result = $this->blogCategory->statusChange($id);
       return $this->formatResponse($result);

    }
}
