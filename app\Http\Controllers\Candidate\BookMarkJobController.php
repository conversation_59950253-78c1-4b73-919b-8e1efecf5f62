<?php

namespace App\Http\Controllers\Candidate;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class BookMarkJobController extends Controller
{
    /** job book mark added | remove
     *
     *=============jobBookMark=======

     *
     * @param  int  $jobId
     * @return Response
     */
    public function jobBookMark($jobId)
    {

        if (Auth::check()) {
            $user = User::find(Auth::user()->id);
            $bookmarkJob = $user->bookmarkJobs()->where('job_id', $jobId)->count();

            if ($bookmarkJob == 0) {
                $user->bookmarkJobs()->attach($jobId);
                return response()->json([
                    'status' => true,
                    'message' => translate('Job Book Mark Added Successfully.'),
                ]);
            } else {
                $user->bookmarkJobs()->detach($jobId);
                return response()->json([
                    'status' => true,
                    'message' => translate('Job Book Mark Remove Successfully.'),
                ]);
            }
        }
        return response()->json([
            'status' => false,
            'message' => translate('Please Need to Login.'),
        ]);

    }
}
