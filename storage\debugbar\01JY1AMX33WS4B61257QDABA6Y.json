{"__meta": {"id": "01JY1AMX33WS4B61257QDABA6Y", "datetime": "2025-06-18 10:51:38", "utime": **********.468313, "method": "GET", "uri": "/admin/company?draw=1&columns%5B0%5D%5Bdata%5D=Company&columns%5B0%5D%5Bname%5D=Company&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=OwnerName&columns%5B1%5D%5Bname%5D=OwnerName&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=Organization%2FCountry&columns%5B2%5D%5Bname%5D=Organization%2FCountry&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=Establishment+Date&columns%5B3%5D%5Bname%5D=Establishment+Date&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=Verified&columns%5B4%5D%5Bname%5D=Verified&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=true&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=Status&columns%5B5%5D%5Bname%5D=Status&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=true&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=action&columns%5B6%5D%5Bname%5D=action&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=false&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=0&order%5B0%5D%5Bdir%5D=asc&start=0&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&_=1750243896195", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750243897.9856, "end": **********.46833, "duration": 0.4827299118041992, "duration_str": "483ms", "measures": [{"label": "Booting", "start": 1750243897.9856, "relative_start": 0, "end": **********.219704, "relative_end": **********.219704, "duration": 0.*****************, "duration_str": "234ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.219714, "relative_start": 0.****************, "end": **********.468332, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "249ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.22935, "relative_start": 0.*****************, "end": **********.231851, "relative_end": **********.231851, "duration": 0.0025010108947753906, "duration_str": "2.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.465853, "relative_start": 0.****************, "end": **********.466239, "relative_end": **********.466239, "duration": 0.0003859996795654297, "duration_str": "386μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/company", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "controller": "App\\Http\\Controllers\\Backend\\CompanyController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.company", "prefix": "admin/company", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/CompanyController.php:23-30</a>"}, "queries": {"count": 77, "nb_statements": 77, "nb_visible_statements": 77, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03713000000000001, "accumulated_duration_str": "37.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.247996, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 1.293}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": "middleware", "name": "prevent-back-history", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Middleware\\PreventBackHistory.php", "line": 19}], "start": **********.25714, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 1.293, "width_percent": 1.508}, {"sql": "select `companies`.*, (select count(*) from `jobs` where `companies`.`id` = `jobs`.`company_id` and `job_deadline` >= '2025-06-18 10:51:38' and `status` = 1) as `active_jobs_count` from `companies` order by `id` desc", "type": "query", "params": [], "bindings": ["2025-06-18 10:51:38", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, {"index": 16, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 46}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/CompanyController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CompanyController.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.26177, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:35", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=35", "ajax": false, "filename": "CompanyRepository.php", "line": "35"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 2.801, "width_percent": 5.009}, {"sql": "select * from `company_types` where `company_types`.`id` in (1, 2, 3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 46}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/CompanyController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CompanyController.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.266508, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:35", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=35", "ajax": false, "filename": "CompanyRepository.php", "line": "35"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 7.81, "width_percent": 1.293}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 65 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.2834451, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 9.103, "width_percent": 1.32}, {"sql": "select * from `users` where `users`.`id` = 271 limit 1", "type": "query", "params": [], "bindings": [271], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.285364, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 10.423, "width_percent": 0.943}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 20 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.286751, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 11.365, "width_percent": 0.943}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 64 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.290584, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 12.308, "width_percent": 1.4}, {"sql": "select * from `users` where `users`.`id` = 265 limit 1", "type": "query", "params": [], "bindings": [265], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.29218, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 13.709, "width_percent": 1.185}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 8 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.293682, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 14.894, "width_percent": 2.155}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 63 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [63], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.298288, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 17.048, "width_percent": 1.347}, {"sql": "select * from `users` where `users`.`id` = 264 limit 1", "type": "query", "params": [], "bindings": [264], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.299767, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 18.395, "width_percent": 0.97}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 1 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.301099, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 19.364, "width_percent": 1.023}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 62 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [62], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3048868, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 20.388, "width_percent": 1.158}, {"sql": "select * from `users` where `users`.`id` = 263 limit 1", "type": "query", "params": [], "bindings": [263], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.30627, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 21.546, "width_percent": 0.943}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 60 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [60], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3093362, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 22.489, "width_percent": 1.185}, {"sql": "select * from `users` where `users`.`id` = 257 limit 1", "type": "query", "params": [], "bindings": [257], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.310733, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 23.674, "width_percent": 3.097}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 15 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.31353, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 26.771, "width_percent": 1.535}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 59 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [59], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3170621, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 28.306, "width_percent": 1.374}, {"sql": "select * from `users` where `users`.`id` = 256 limit 1", "type": "query", "params": [], "bindings": [256], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.318516, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 29.68, "width_percent": 1.131}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 12 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.319911, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 30.811, "width_percent": 1.158}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 58 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.323056, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 31.969, "width_percent": 1.32}, {"sql": "select * from `users` where `users`.`id` = 255 limit 1", "type": "query", "params": [], "bindings": [255], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.324486, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 33.288, "width_percent": 1.104}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 16 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3258739, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 34.393, "width_percent": 1.131}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 57 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.329915, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 35.524, "width_percent": 1.347}, {"sql": "select * from `users` where `users`.`id` = 254 limit 1", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.331382, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 36.87, "width_percent": 0.97}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 11 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.332721, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 37.84, "width_percent": 1.023}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 55 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.335914, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 38.863, "width_percent": 1.158}, {"sql": "select * from `users` where `users`.`id` = 225 limit 1", "type": "query", "params": [], "bindings": [225], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.337281, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 40.022, "width_percent": 0.943}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 17 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.338602, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 40.964, "width_percent": 1.023}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 54 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3416948, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 41.988, "width_percent": 1.131}, {"sql": "select * from `users` where `users`.`id` = 224 limit 1", "type": "query", "params": [], "bindings": [224], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.343067, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 43.119, "width_percent": 0.97}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 13 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.344979, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 44.088, "width_percent": 1.858}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 53 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.349449, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 45.947, "width_percent": 1.508}, {"sql": "select * from `users` where `users`.`id` = 223 limit 1", "type": "query", "params": [], "bindings": [223], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.350973, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 47.455, "width_percent": 1.104}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 52 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.354202, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 48.559, "width_percent": 1.293}, {"sql": "select * from `users` where `users`.`id` = 222 limit 1", "type": "query", "params": [], "bindings": [222], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3556168, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 49.852, "width_percent": 1.104}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 19 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3570428, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 50.956, "width_percent": 1.131}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 51 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [51], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3602571, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 52.087, "width_percent": 1.293}, {"sql": "select * from `users` where `users`.`id` = 221 limit 1", "type": "query", "params": [], "bindings": [221], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.3627272, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 53.38, "width_percent": 1.266}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 14 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3642828, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 54.646, "width_percent": 1.05}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 50 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [50], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3679638, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 55.696, "width_percent": 1.158}, {"sql": "select * from `users` where `users`.`id` = 219 limit 1", "type": "query", "params": [], "bindings": [219], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.369347, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 56.854, "width_percent": 0.943}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 49 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [49], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3724222, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 57.797, "width_percent": 1.185}, {"sql": "select * from `users` where `users`.`id` = 218 limit 1", "type": "query", "params": [], "bindings": [218], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.373797, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 58.982, "width_percent": 0.943}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 3 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.375114, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 59.925, "width_percent": 1.023}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 48 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [48], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.379635, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 60.948, "width_percent": 1.858}, {"sql": "select * from `users` where `users`.`id` = 217 limit 1", "type": "query", "params": [], "bindings": [217], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.38152, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 62.806, "width_percent": 1.293}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 2 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.383081, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 64.099, "width_percent": 1.131}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 47 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [47], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.386867, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 65.23, "width_percent": 1.751}, {"sql": "select * from `users` where `users`.`id` = 216 limit 1", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.388567, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.981, "width_percent": 1.131}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 9 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.389999, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 68.112, "width_percent": 1.077}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 46 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.3936312, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 69.189, "width_percent": 1.185}, {"sql": "select * from `users` where `users`.`id` = 214 limit 1", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.395827, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 70.374, "width_percent": 1.67}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 45 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.39972, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 72.044, "width_percent": 1.347}, {"sql": "select * from `users` where `users`.`id` = 213 limit 1", "type": "query", "params": [], "bindings": [213], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.40118, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 73.391, "width_percent": 0.97}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 5 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.402536, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 74.36, "width_percent": 0.996}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 44 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.405586, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 75.357, "width_percent": 1.131}, {"sql": "select * from `users` where `users`.`id` = 212 limit 1", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.406938, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 76.488, "width_percent": 0.97}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 4 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.4083009, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.458, "width_percent": 0.996}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 43 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.4123468, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 78.454, "width_percent": 1.616}, {"sql": "select * from `users` where `users`.`id` = 203 limit 1", "type": "query", "params": [], "bindings": [203], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.4145699, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.07, "width_percent": 1.535}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 41 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.4181871, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 81.605, "width_percent": 1.374}, {"sql": "select * from `users` where `users`.`id` = 192 limit 1", "type": "query", "params": [], "bindings": [192], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.419638, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 82.979, "width_percent": 1.131}, {"sql": "select * from `company_type_translations` where `company_type_translations`.`company_type_id` = 10 and `company_type_translations`.`company_type_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.421032, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CompanyType.php:20", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/CompanyType.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\CompanyType.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=20", "ajax": false, "filename": "CompanyType.php", "line": "20"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 84.11, "width_percent": 1.158}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 40 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.424693, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 85.268, "width_percent": 1.347}, {"sql": "select * from `users` where `users`.`id` = 191 limit 1", "type": "query", "params": [], "bindings": [191], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.426132, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 86.615, "width_percent": 1.131}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 39 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.431357, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 87.746, "width_percent": 1.966}, {"sql": "select * from `users` where `users`.`id` = 177 limit 1", "type": "query", "params": [], "bindings": [177], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.43329, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 89.712, "width_percent": 1.266}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 35 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.437215, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 90.978, "width_percent": 1.239}, {"sql": "select * from `users` where `users`.`id` = 167 limit 1", "type": "query", "params": [], "bindings": [167], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.438607, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 92.217, "width_percent": 0.97}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 26 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.442317, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 93.186, "width_percent": 1.239}, {"sql": "select * from `users` where `users`.`id` = 158 limit 1", "type": "query", "params": [], "bindings": [158], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.4438589, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 94.425, "width_percent": 1.05}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 20 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.4486, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 95.475, "width_percent": 1.347}, {"sql": "select * from `users` where `users`.`id` = 152 limit 1", "type": "query", "params": [], "bindings": [152], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.450039, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 96.822, "width_percent": 0.996}, {"sql": "select * from `company_translations` where `company_translations`.`company_id` = 1 and `company_translations`.`company_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.453771, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Company.php:94", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Company.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Company.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=94", "ajax": false, "filename": "Company.php", "line": "94"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 97.818, "width_percent": 1.212}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.455153, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CompanyRepository.php:69", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CompanyRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CompanyRepository.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCompanyRepository.php&line=69", "ajax": false, "filename": "CompanyRepository.php", "line": "69"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 99.03, "width_percent": 0.97}]}, "models": {"data": {"App\\Models\\Admin\\CompanyTypeTranslation": {"value": 70, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyTypeTranslation.php&line=1", "ajax": false, "filename": "CompanyTypeTranslation.php", "line": "?"}}, "App\\Models\\Admin\\Company": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\User": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\CompanyType": {"value": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyType.php&line=1", "ajax": false, "filename": "CompanyType.php", "line": "?"}}, "App\\Models\\Admin\\CompanyTranslation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompanyTranslation.php&line=1", "ajax": false, "filename": "CompanyTranslation.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 148, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/lang/flags/in.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/company?_=1750243896195&columns%5B0%5D%5Bdata%5D=Company&columns%5B0%5D%...", "action_name": "admin.company", "controller_action": "App\\Http\\Controllers\\Backend\\CompanyController@index", "uri": "GET admin/company", "controller": "App\\Http\\Controllers\\Backend\\CompanyController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/company", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCompanyController.php&line=23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/CompanyController.php:23-30</a>", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "duration": "490ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Company</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Company</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"9 characters\">OwnerName</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">OwnerName</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Organization/Country</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Organization/Country</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Establishment Date</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Establishment Date</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Verified</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Verified</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1750243896195</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2133397393 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2133397393\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1895676353 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/admin/company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6IkUxNUJyR09GQXpHbXJSVVFoNm00RkE9PSIsInZhbHVlIjoiYXdlNUpieFBFeHo3b1RoSkVZaGRBQlFpa04xVmxsR2d5Z0kwRzZYOER5Q0VXd3RRS2huVUE5Z1ExQm0wUHZ6a1pkZGdCR2p4SFV6WnVkUlZRYVo1TkhmS05rRjJBaEZkR0U1NFh6Q2hqaFk5SGZGTWZJVDJrblMzRldxMlMxODQiLCJtYWMiOiI3MGQ0MjE3NDNkMTk5ZjhjNjExMTM1ZDYzNTE1NmM0ZGI5OWM1MWE2NWFiYTdiZDUzMmYxNmE1NTVlMGM0YzliIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im03QWdoZndmL0FxT1VLRTJXQ25LZEE9PSIsInZhbHVlIjoiWlBZZmF1dXRwdUg1blFDV0tkcTg5VWVXSURkMmwzWDB3MTdmWURFekoxUkVKcUdsdlZoR0JLOEorSHd0bXNPT0N1Q2VIRHhEOCtMTHVZMnRTdEo0QmFlVlA4NTJyQStwZllneEYxS1ByK3pFakMxcW9VT3ptdkVhRWpwNFNidVIiLCJtYWMiOiJkOGVmM2Y3ZWQ5NTE4MzdjNjNlZjFiNGRhZDk5NjI1ZWIzZGNlYzRhMTYyYWY1MGNjYWRhZDk3ZDljYjI2ZTE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895676353\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-712901497 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qUd4p9n8BUUDYUEq0qgc1rceEYaWteQGcYoDZANF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712901497\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1889355490 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 18 Jun 2025 10:51:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889355490\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2040152561 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3OwPiYWb9g7qYd9NhG1JjMTNndxoL2PuTv8CVSgL</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/storage/lang/flags/in.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040152561\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/company?_=1750243896195&columns%5B0%5D%5Bdata%5D=Company&columns%5B0%5D%...", "action_name": "admin.company", "controller_action": "App\\Http\\Controllers\\Backend\\CompanyController@index"}, "badge": null}}