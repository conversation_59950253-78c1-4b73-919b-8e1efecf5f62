<div class="contact-support-area pt-120 ">
    <div class="container">
        <div class="row g-lg-4 gy-5">
            <div class="col-lg-6">
                <div class="contect-content">
                    <h4><?php echo e($singleWidgetDataShow->getTranslation('title') ?? ' Need Any Help? Contact Us'); ?>

                    </h4>
                    <p><?php echo e(clean($singleWidgetDataShow->getTranslation('description')) ?? ' Alternatively you can also check for the Company email, phone number and address in the official website.'); ?>

                    </p>
                    <div class="support">
                        <div class="icon">
                            <img src="<?php echo e(asset('frontend/assets/images/icon/footer-support-icon.svg')); ?>" alt="">
                        </div>
                        <div class="content">
                            <h5><?php echo e(translate('Support Line')); ?>:</h5>
                            <a
                                href="tel:<?php echo e($singleWidgetDataShow->phone ?? '+************ 3465 '); ?>"><?php echo e($singleWidgetDataShow->phone ?? '+************ 3465 '); ?></a>
                        </div>
                    </div>
                    <div class="service-available">
                        <span><?php echo e(translate('N')); ?>:<?php echo e(translate('B')); ?></span>
                        <p><?php echo e($singleWidgetDataShow->note ?? 'Our Customer Service Available from 9 am to 6 pm (Saturday to Thursday)'); ?>

                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="contact-form form-wrapper ">

                    <form method="post" class="profile-form" action="<?php echo e(route('contact')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-inner mb-25">
                                    <label for="name"><?php echo e(translate('Your Name')); ?> <span>*</span></label>
                                    <div class="input-area">
                                        <img src="<?php echo e(asset('frontend/assets/images/icon/user-2.svg')); ?>"
                                            alt="">
                                        <input type="text" class="<?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required
                                            id="name" name="name" placeholder="<?php echo e(translate('Enter Name')); ?>">
                                    </div>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"
                                            role="alert"><strong><?php echo e($message); ?></strong></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-inner mb-25">
                                    <label for="email"><?php echo e(translate('Email')); ?> <span>*</span></label>
                                    <div class="input-area">
                                        <img src="<?php echo e(asset('frontend/assets/images/icon/email-2.svg')); ?>"
                                            alt="">
                                        <input type="email" class="<?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required
                                            id="email" name="email" placeholder="<?php echo e(translate('Enter Email')); ?>">
                                    </div>
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"
                                            role="alert"><strong><?php echo e($message); ?></strong></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-inner mb-25">
                                    <label for="phone"><?php echo e(translate('Phone')); ?><span>*</span></label>
                                    <div class="input-area">
                                        <img src="<?php echo e(asset('frontend/assets/images/icon/phone-2.svg')); ?>"
                                            alt="">
                                        <input type="text" class="<?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required
                                            id="phone" name="phone" placeholder="<?php echo e(translate('Enter Phone')); ?>">
                                    </div>
                                    <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"
                                            role="alert"><strong><?php echo e($message); ?></strong></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-inner mb-25">
                                    <label for="company_name"><?php echo e(translate('Company Name')); ?>

                                        (<?php echo e(translate('Optional')); ?>)</label>
                                    <div class="input-area">
                                        <img src="<?php echo e(asset('frontend/assets/images/icon/company-2.svg')); ?>"
                                            alt="">
                                        <input type="text" id="company_name" name="company_name" required
                                            placeholder="<?php echo e(translate('Company Name')); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-inner mb-40">
                                    <label for="message"><?php echo e(translate('Message')); ?></label>
                                    <textarea class="<?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="message" required id="message"
                                        placeholder="<?php echo e(translate('Message')); ?>..."></textarea>
                                    <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"
                                            role="alert"><strong><?php echo e($message); ?></strong></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-inner">
                                    <button class="primry-btn-2 lg-btn w-unset"
                                        type="submit"><?php echo e(translate('Send Message')); ?></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/pages/widgets/contact-us.blade.php ENDPATH**/ ?>