<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Admin\PackageAddon;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PackageAddonController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //

        if ($request->ajax()) {

            $data = PackageAddon::latest()->get();

            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn('action', function ($data) {
                    $btn = '<a href="'.route('admin.package.addon.item.index').'" class="eg-btn add--btn"><i class="bi bi-plus editPackage" data-id="'.$data->id.'"></i>  Add Addon Item</a>';

                    $btn = $btn.'<a href="javascript:void(0)" class="eg-btn add--btn btn-gaps"><i class="bi bi-pencil-square editPackageAddon" data-id="'.$data->id.'"></i></a>';
                    // $btn = $btn . '<a href="javascript:void(0)" class="eg-btn delete--btn btn-gaps"><i class="bi bi-trash deletePackage" data-id="' . $data->id . '"></i></a>';
                    if ($data->status == 1) {
                        $btn = $btn.'<a href="javascript:void(0)" class="eg-btn account--btn btn-gaps"><i class="fa fa-arrow-up statusPackage" data-id="'.$data->id.'"></i></a>';
                    } else {
                        $btn = $btn.'<a href="javascript:void(0)" class="eg-btn account--btn btn-gaps warning--btn"><i class="fa fa-arrow-down statusPackage" data-id="'.$data->id.'"></i></a>';
                    }

                    return $btn;
                })

                ->editColumn('Name', function ($data) {
                    return $data->name;
                })
                ->editColumn('Addon Type', function ($data) {
                    return $data->type;
                })

                ->editColumn('Item Qty', function ($data) {
                    return $data->item_qty;
                })

                ->editColumn('Price', function ($data) {
                    return $data->price;
                })

                ->editColumn('Expired Date', function ($data) {
                    return $data->expired_date;
                })

                ->editColumn('Status', function ($data) {
                    if ($data->status == 1) {
                        return '<span class="eg-btn green-light--btn">Active</span>';
                    } else {
                        return '<span class="eg-btn red-light--btn">InActive</span>';
                    }
                })
                ->rawColumns(['action', 'Name', 'Addon Type', 'Item Qty', 'Expired Date', 'Status'])
                ->make(true);
        }

        return view('admin.pages.package.package_addon.index');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $package = PackageAddon::updateOrCreate( ['id' => $request->id],[
            'name' => $request->name,
            'package_id' => $request->package_id,
            'type' => $request->type,
            'item_qty' => $request->item_qty,
            'price' => $request->price,
            'expired_date' => $request->expired_date,
            'status' => $request->status == 'on' ? 1 : 0,
            'featured' => $request->featured == 'on' ? 1 : 0,

        ]);
        if (!$package) {
            return response()->json(['success' => false]);
        }
        return response()->json(['success' => true]);
    }


    public function statusChange(Request $request)
    {
        $packageAddon = PackageAddon::find($request->id);
        $packageAddon->status = $packageAddon->status ? false : true;
        $packageAddon->update();

        if ($packageAddon) {
            return response()->json(['success' => true]);
        }
    }

}
