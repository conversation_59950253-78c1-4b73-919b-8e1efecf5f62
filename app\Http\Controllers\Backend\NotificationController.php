<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;

class NotificationController extends Controller
{
    public function getCookieData()
    {

        $notification = json_decode(request()->cookie('notification'), true);

        return ! empty($notification) ? $notification : [];
    }

    public function CountNotification()
    {
        $notification = json_decode(request()->cookie('notification'), true);

        return ! empty($notification) ? count($notification) : 0;
    }

    public function singleNotification($id)
    {
        $notification = json_decode(request()->cookie('notification'), true);
        unset($notification[$id]);
        $notification = array_values($notification);

        return redirect()->back()->withCookie('notification', json_encode($notification));
    }

    public function allNotification()
    {
        $notification = json_decode(request()->cookie('notification'), true);
        $notification = [];
        return redirect()->back()->with<PERSON><PERSON>ie('notification', json_encode($notification));
    }
}
