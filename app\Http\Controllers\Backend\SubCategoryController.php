<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\JobSubCategoryRequest;
use App\Repositories\Admin\JobSubCategoryRepostitory;
use Illuminate\Http\Request;

class SubCategoryController extends Controller
{
    public function __construct(protected JobSubCategoryRepostitory $jobsubcategory) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->jobsubcategory->content();
        }
        return view('admin.pages.subcategory.index');
    }

    /** new Job Sub Category store
     * ========= store============
     *
     * @param App\Http\Requests\JobSubCategoryRequest
     * @return Response
     */
    public function store(JobSubCategoryRequest $request)
    {
        if (!$this->hasPermissions(['jobsubcategory.add'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jobsubcategory->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $jobSubCategory = $this->jobsubcategory->getById($id);

        $data['subcategory_name'] = $jobSubCategory->getTranslation('subcategory_name', Request()->lang);
        $data['id'] = $jobSubCategory->id;
        $data['lang'] = Request()->lang;
        $data['category_id'] = $jobSubCategory->category_id;
        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['jobsubcategory.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->jobsubcategory->destroy($id);
        return $this->formatResponse($result);

    }

    /** sub category status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['jobsubcategory.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jobsubcategory->statusChange($id);
        return $this->formatResponse($result);

    }

    /** sub category by category id
     * ======= subcategoryByCategoryId ========
     *
     * @param  int id
     * @return Response
     */
    public function subcategoryByCategoryId($categoryId)
    {
        $lang = Request()->lang;
        $result = $this->jobsubcategory->subcategoryByCategoryId($categoryId);
        $translateSubcategory = [];
        foreach ($result['jobsubcategories'] as $key => $subcategory) {
            $translateSubcategory[] = [
                'id' => $subcategory->id,
                'category_id' => $subcategory->category_id,
                'subcategory_name' => $subcategory->getTranslation('subcategory_name', $lang),
            ];
        }
        if ($result['status']) {
            return response()->json([
                'status' => true,
                'jobsubcategories' => $translateSubcategory
            ]);
        }
        return response()->json([
            'status' => false,
            'message' => $result['message']
        ]);
    }
}
