<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\languageLevelRequest;
use App\Repositories\Admin\LanguageLevelRepostitory;
use Illuminate\Http\Request;

class LanguageLevelController extends Controller
{
    //

    public function __construct(protected LanguageLevelRepostitory $languageLevel) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->languageLevel->content();
        }

        return view('admin.pages.language-level.index');
    }

    /** new Job Sub Category store
     * ========= store============
     *
     * @param JobExperienceRequest
     * @return Response
     */
    public function store(languageLevelRequest $request)
    {
        if (!$this->hasPermissions(['languagelevel.add', 'languagelevel.edit'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->languageLevel->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $languageLevel = $this->languageLevel->getById($id);
        $data['name'] = $languageLevel->getTranslation('name', Request()->lang);
        $data['id'] = $languageLevel->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['languagelevel.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->languageLevel->destroy($id);
        return $this->formatResponse($result);

    }

    /** Job Type status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['languagelevel.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->languageLevel->statusChange($id);
        return $this->formatResponse($result);

    }
}
