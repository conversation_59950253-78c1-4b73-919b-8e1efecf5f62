

<?php $__env->startPush('post_styles'); ?>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- ========== Login Area end============= -->
    <div class="register-area pt-120 mb-120">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="form-wrapper">
                        <div class="form-title">
                            <h3><?php echo e(translate('Register Account')); ?></h3>
                            <span></span>
                        </div>
                        <div class="register-tab">
                            <nav>
                                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                    <button class="nav-link active" id="nav-home-tab" data-bs-toggle="tab"
                                        data-bs-target="#nav-home" type="button" role="tab" aria-controls="nav-home"
                                        aria-selected="true"><?php echo e(translate('Candidate')); ?></button>
                                    <button class="nav-link" id="nav-profile-tab" data-bs-toggle="tab"
                                        data-bs-target="#nav-profile" type="button" role="tab"
                                        aria-controls="nav-profile"
                                        aria-selected="false"><?php echo e(translate('Company')); ?></button>
                                </div>
                            </nav>
                            <div class="tab-content" id="nav-tabContent">
                                <div class="tab-pane fade show active" id="nav-home" role="tabpanel"
                                    aria-labelledby="nav-home-tab">
                                    <form method="post" action="<?php echo e(route('candidate.register')); ?>" class="profile-form add-form">
                                        <?php echo csrf_field(); ?>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="firstname"><?php echo e(translate('First Name')); ?> <span>*</span>
                                                    </label>
                                                    <div class="input-area">

                                                        <input class="<?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            type="text" id="candidate_first_name" name="first_name"
                                                            placeholder="<?php echo e(translate('Enter Your First Name')); ?>"
                                                            value="<?php echo e(old('first_name')); ?>"
                                                            >
                                                    </div>
                                                    <span class="text-danger error-text first_name_err"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="lastname"><?php echo e(translate('Last Name')); ?>

                                                        <span>*</span></label>
                                                    <div class="input-area">

                                                        <input class="<?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            type="text" id="candidate_last_name" name="last_name"
                                                            placeholder="<?php echo e(translate('Enter Your Last Name')); ?>"
                                                            value="<?php echo e(old('last_name')); ?>"

                                                            >
                                                    </div>
                                                    <span class="text-danger error-text last_name_err"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="username"> <?php echo e(translate('User Name')); ?> </label>
                                                    <div class="input-area">
                                                        <input class="<?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            type="text" id="candidate_username" name="username"
                                                            placeholder="<?php echo e(translate('Enter Your User Name')); ?>"
                                                            value="<?php echo e(old('username')); ?>">
                                                    </div>

                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="email"><?php echo e(translate('Email')); ?> <span>*</span> </label>
                                                    <div class="input-area">
                                                        <input class="<?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" type="email"
                                                            id="candidate_email" name="email"
                                                            placeholder="<?php echo e(translate('Enter Your Email')); ?>"
                                                            value="<?php echo e(old('email')); ?>"
                                                            >
                                                    </div>
                                                    <span class="text-danger error-text email_err"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="password"> <?php echo e(translate('Password')); ?> <span>*</span>
                                                    </label>
                                                    <input type="password" name="password" id="candidate_password"
                                                        placeholder="<?php echo e(translate('Enter Password')); ?>" />
                                                    <i class="bi bi-eye-slash toggle-password bi-eye"></i>
                                                </div>
                                                <span class="text-danger error-text password_err"></span>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="confirm_password"><?php echo e(translate('Confirm Password')); ?>

                                                        <span>*</span></label>
                                                    <input type="password" name="password_confirmation"
                                                        id="candidate_confirm_password"
                                                        placeholder=" <?php echo e(translate('Enter Confirm Password')); ?>" />
                                                    <i class="bi bi-eye-slash toggle-password bi-eye"></i>
                                                </div>

                                            </div>
                                            <div class="col-md-12">
                                                <div
                                                    class="form-agreement form-inner d-flex justify-content-between flex-wrap">
                                                    <div class="form-group two">
                                                        <input class="<?php $__errorArgs = ['terms_condition'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            type="checkbox" id="html1" name="terms_condition"

                                                            >
                                                        <label for="html1"> <?php echo e(translate('Here, I will agree to')); ?> <a
                                                                href="<?php echo e(isset(getThemeOption('page_setting')['terms_condition_url']) ? getThemeOption('page_setting')['terms_condition_url'] . '?style=list-view' : 'javascript:void(0)'); ?>"><?php echo e(translate('Terms & Conditions')); ?>

                                                            </a> </label>

                                                        <span class="text-danger error-text terms_condition_err"></span>
                                                    </div>
                                                </div>
                                            </div>


                                            <div class="col-md-12">
                                                <div class="form-inner">
                                                    <button class="primry-btn-2" type="submit">
                                                        <?php echo e(translate('Sign Up')); ?></button>
                                                </div>
                                            </div>
                                            <h6> <?php echo e(translate('Already have an account')); ?>? <a
                                                    href="<?php echo e(route('auth.user.login')); ?>"><?php echo e(translate('Login')); ?></a>
                                                <?php echo e(translate('Here')); ?></h6>
                                            <div class="login-difarent-way">
                                                <div class="row g-4">
                                                    <div class="col-md-6">
                                                        <a href="<?php echo e(route('social.login', 'google')); ?>"><img
                                                                src="<?php echo e(asset('frontend/assets/images/icon/google1.svg')); ?>"
                                                                alt=""> <?php echo e(translate(' Log in with Google')); ?>

                                                        </a>
                                                    </div>
                                                    
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="tab-pane fade" id="nav-profile" role="tabpanel"
                                    aria-labelledby="nav-profile-tab">
                                    <form method="post" action="<?php echo e(route('company.register')); ?>"
                                        class="register-form profile-form  add-form">
                                        <?php echo csrf_field(); ?>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="firstname"> <?php echo e(translate('First Name')); ?>

                                                        <span>*</span></label>
                                                    <div class="input-area">

                                                        <input type="text" id="company_first_name" name="first_name"
                                                            placeholder="<?php echo e(translate('Enter Your First Name')); ?>">
                                                    </div>
                                                    <span class="text-danger error-text first_name_err"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="lastname"> <?php echo e(translate('Last Name')); ?>

                                                        <span>*</span></label>
                                                    <div class="input-area">

                                                        <input type="text" id="company_last_name" name="last_name"
                                                            placeholder="<?php echo e(translate('Enter Your Last Name')); ?>">
                                                    </div>
                                                    <span class="text-danger error-text last_name_err"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="username"> <?php echo e(translate('User Name')); ?> </label>
                                                    <div class="input-area">

                                                        <input type="text" id="company_username" name="username"
                                                            placeholder="<?php echo e(translate('Enter Your User Name')); ?>">
                                                    </div>
                                                    <span class="text-danger error-text username_err"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="email"> <?php echo e(translate('Email')); ?> <span>*</span></label>
                                                    <div class="input-area">

                                                        <input type="email" id="company_email" name="email"
                                                            placeholder="<?php echo e(translate('Enter Your Email')); ?>">
                                                    </div>
                                                    <span class="text-danger error-text email_err"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="password"><?php echo e(translate('Password')); ?>

                                                        <span>*</span></label>
                                                    <input type="password" name="password" id="company_password"
                                                        placeholder="<?php echo e(translate('Enter Password')); ?>" />
                                                    <i class="bi bi-eye-slash toggle-password bi-eye"></i>
                                                </div>
                                                <span class="text-danger error-text password_err"></span>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner">
                                                    <label
                                                        for="confirmpassword"><?php echo e(translate('Confirm Password')); ?><span>*</span></label>
                                                    <input type="password" name="password_confirmation"
                                                        id="password_confirmation"
                                                        placeholder="<?php echo e(translate('Enter Confirm Password')); ?>" />
                                                    <i class="bi bi-eye-slash toggle-password bi-eye"></i>
                                                </div>
                                                <span class="text-danger error-text password_confirmation_err"></span>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="company_name"><?php echo e(translate('Company Name')); ?>

                                                        <span>*</span></label>
                                                    <div class="input-area">
                                                        <input type="text" id="company_name" name="company_name"
                                                            placeholder="<?php echo e(translate('Enter Company Name')); ?>">
                                                    </div>
                                                    <span class="text-danger error-text company_name_err"></span>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label
                                                        for="company_size"><?php echo e(translate('Company Size')); ?><span>*</span></label>
                                                    <div class="input-area">
                                                        <input type="text" id="company_size" name="company_size"
                                                            placeholder="<?php echo e(translate('Enter Company Size')); ?>">
                                                    </div>
                                                    <span class="text-danger error-text company_size_err"></span>

                                                </div>
                                            </div>
                                            <div class="col-md-6">

                                                <div class="form-inner mb-25">
                                                    <label
                                                        for="company_type"><?php echo e(translate('Company Type')); ?><span>*</span></label>
                                                    <div class="input-area">
                                                        <input type="text" class="company_type" name="company_type"
                                                            placeholder="<?php echo e(translate('Enter Company Type')); ?>">
                                                    </div>
                                                    <div class="text-danger error-text company_type_err">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <label for="company_website"><?php echo e(translate('Website')); ?> </label>
                                                    <div class="input-area">
                                                        <input type="text" id="company_website" name="company_website"
                                                            placeholder="<?php echo e(translate('Enter Company Website')); ?>">
                                                    </div>
                                                    <span class="text-danger error-text company_website_err"></span>
                                                </div>
                                            </div>


                                            <div class="col-md-12">
                                                <div
                                                    class="form-agreement form-inner d-flex justify-content-between flex-wrap">
                                                    <div class="form-group two">
                                                        <input type="checkbox" id="html" name="terms_condition">
                                                        <label
                                                            for="html"><?php echo e(translate('Here, I will agree company')); ?>

                                                            <a
                                                                href="<?php echo e(isset(getThemeOption('page_setting')['terms_condition_url']) ? getThemeOption('page_setting')['terms_condition_url'] . '?style=list-view' : 'javascript:void(0)'); ?>"><?php echo e(translate('Terms & Conditions')); ?>

                                                            </a>
                                                        </label>

                                                    </div>
                                                    <span class="text-danger error-text terms_condition_err"></span>

                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-inner">
                                                    <button class="primry-btn-2"
                                                        type="submit"><?php echo e(translate('Sign Up')); ?> </button>
                                                </div>
                                            </div>
                                            <h6> <?php echo e(translate('Already have an account')); ?>? <a
                                                    href="<?php echo e(route('auth.user.login')); ?>"> <?php echo e(translate('Login')); ?>

                                                </a><?php echo e(translate('Here')); ?> </h6>

                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- ========== Login Area end============= -->
<?php $__env->stopSection(); ?>
<?php $__env->startPush('post_styles'); ?>
    <?php echo $__env->make('js.front.login', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\jobes-application\resources\views/front/pages/auth/signup.blade.php ENDPATH**/ ?>