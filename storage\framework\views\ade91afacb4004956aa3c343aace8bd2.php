<?php

    $singleWidgetData=[];
    if (!empty($singleWidgetDataShow)) 
        $singleWidgetData= $singleWidgetDataShow->getTranslation('widget_content');

    $blogs= blog('', $singleWidgetData['show_item'] ??  '') ;
    
?>

<!-- ========== Blog Grid Start============= -->
<div class="blog-grid-area pt-120">
    <div class="container">
        <?php if(isset($singleWidgetData['style'])): ?>
            <?php if($singleWidgetData['style'] == 'default'): ?>
                <div class="row g-lg-4 gy-5 justify-content-center">
                    <?php $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-4 col-md-6 col-sm-10 mb-20">
                            <div class="recent-article-wrap">
                                <div class="recent-article-img">

                                    <?php if(fileExists('blog',  $blog->blog_thumbnail) == true && $blog->blog_thumbnail !== ''): ?>
                                        <img class="img-fluid" src="<?php echo e(asset('storage/blog/' . $blog->blog_thumbnail)); ?>"
                                            alt="">
                                    <?php else: ?>
                                        <img class="img-fluid" src="<?php echo e(asset('frontend/assets/images/416-240.png')); ?>"
                                            alt="">
                                    <?php endif; ?>

                                </div>
                                <div class="recent-article-content">
                                    <div class="recent-article-meta">
                                        <div class="publish-area">

                                            <a href="<?php echo e(route('blog.details', $blog->slug)); ?>"><span>
                                                    <?php echo e(customDateFormate($blog->created_at, $format = 'd')); ?></span><?php echo e(customDateFormate($blog->created_at, $format = 'M')); ?></a>

                                        </div>
                                        <ul>

                                            <li><a href="<?php echo e(route('blog.details', $blog->slug)); ?>"><img
                                                        src="<?php echo e(asset('frontend/assets/images/icon/comment.svg')); ?>"
                                                        alt=""> <?php echo e($blog->comments_count); ?> <?php echo e($blog->comments_count > 1 ?translate('Comments') : translate('Comment')); ?></a></li>


                                            <li><a href="<?php echo e(route('blog.details', $blog->slug)); ?>"><img
                                                        src="<?php echo e(asset('frontend/assets/images/icon/user.svg')); ?>"
                                                        alt=""><?php echo e($blog->author_name); ?></a>
                                            </li>
                                        </ul>
                                    </div>
                                    <h4><a href="<?php echo e(route('blog.details', $blog->slug)); ?>"><?php echo e($blog->getTranslation('blog_title')); ?></a>
                                    </h4>
                                    <div class="explore-btn">
                                        <a href="<?php echo e(route('blog.details', $blog->slug)); ?>"><span><img
                                                    src="<?php echo e(asset('frontend/assets/images/icon/explore-elliose.svg')); ?>"
                                                    alt=""></span> <?php echo e(translate('Explore More')); ?></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-12 d-flex justify-content-center pt-20">
                        <div class="pagination-area">
                            <?php echo e($blogs->links('front.vendor.pagination.custom')); ?>

                        </div>
                    </div>

                </div>
            <?php else: ?>
                <input type="hidden" value="all-blog" id="widget_name">
                <input type="hidden" value="standard" id="style">

                <input type="hidden"
                    value="<?php echo e($singleWidgetData['show_item'] ??  ''); ?>"
                    id="item_show">

                <div class="row g-lg-4 gy-5 justify-content-center">
                    <div class="col-lg-8">
                        <div class="eg-specific-area">
                            <?php echo $__env->make('front.pages.loader', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <div id="standard-blog">
                                <?php echo $__env->make('front.pages.blog.standard-content', [
                                    'blogs' =>  $blogs,
                                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="job-sidebar mb-50">
                            <form method="get" class="search-form">
                                <div class="job-widget widget_search mb-20">
                                    <div class="wp-block-search__inside-wrapper ">
                                        <input type="search" id="search_key" class="wp-block-search__input"
                                            name="search_key" placeholder="Search Here">
                                        <button type="submit" class="wp-block-search__button">
                                            <svg width="14" height="14" viewBox="0 0 20 20"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M7.10227 0.0713005C1.983 0.760967 -1.22002 5.91264 0.44166 10.7773C1.13596 12.8 2.60323 14.471 4.55652 15.4476C6.38483 16.3595 8.59269 16.5354 10.5737 15.9151C11.4023 15.6559 12.6011 15.0218 13.2121 14.5126L13.3509 14.3969L16.1281 17.1695C19.1413 20.1735 18.9932 20.0531 19.4237 19.9698C19.6505 19.9281 19.9282 19.6504 19.9699 19.4236C20.0532 18.9932 20.1735 19.1413 17.1695 16.128L14.397 13.3509L14.5127 13.212C14.7858 12.8834 15.2394 12.152 15.4755 11.6614C17.0029 8.48153 16.3271 4.74159 13.7814 2.28379C11.9994 0.561935 9.52304 -0.257332 7.10227 0.0713005ZM9.38418 1.59412C11.0135 1.9135 12.4669 2.82534 13.4666 4.15376C14.0591 4.94062 14.4572 5.82469 14.6793 6.83836C14.8136 7.44471 14.8228 8.75925 14.7025 9.34708C14.3507 11.055 13.4713 12.4622 12.1336 13.4666C11.3467 14.059 10.4627 14.4571 9.44898 14.6793C8.80097 14.8228 7.48644 14.8228 6.83843 14.6793C4.78332 14.2303 3.0985 12.9389 2.20054 11.1337C1.75156 10.2312 1.54328 9.43503 1.49699 8.4445C1.36276 5.62566 3.01055 3.05677 5.6535 1.96904C6.10248 1.7839 6.8014 1.59412 7.28741 1.52932C7.74102 1.46452 8.92595 1.50155 9.38418 1.59412Z">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                            </form>
                            <div class="job-widget style-1 mb-20">
                                <div class="check-box-item">
                                    <h5 class="job-widget-title"><?php echo e(translate('Category')); ?> <span
                                            class="badge badge-success clear-applicatin-filter"><i class="bi bi-x"></i><?php echo e(translate('Clear Filter')); ?></span></h5>
                                    <div class="checkbox-container">
                                        <ul>
                                            <?php $__currentLoopData = BlogCategoryShow(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $categoryCountBlog= CountCategoryBlog($item->id) ;
                                            ?>
                                                <?php if($categoryCountBlog > 0): ?>
                                                    <li>
                                                        <label class="containerss">
                                                            <input type="checkbox" name="categoryId[]"
                                                                id="categoryIdCheckbox" value="<?php echo e($item->id); ?>"
                                                                class="filter-option blog-category">
                                                            <span class="checkmark"></span><span
                                                                class="text"><?php echo e($item->getTranslation('blog_category_name')); ?></span>
                                                            <span
                                                                class="qty">(<?php echo e($categoryCountBlog); ?>)</span>
                                                        </label>
                                                    </li>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>

                                    </div>
                                </div>
                            </div>

                            <div class="job-widget">
                                <h5 class="job-widget-title mb-10"><?php echo e(Translate('Recent Post')); ?></h5>
                                <ul class="recent-activitys">
                                    <?php $__currentLoopData = blog(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li>
                                            <div class="blog-img">
                                                <?php if(fileExists('blog', $item->blog_thumbnail) == true && $item->blog_thumbnail !== ''): ?>
                                                    <img class="img-fluid" src="<?php echo e(asset('storage/blog/' . $item->blog_thumbnail)); ?>" alt="">
                                                <?php else: ?>
                                                    <img class="img-fluid" src="<?php echo e(asset('frontend/assets/images/416-240.png')); ?>" alt="">
                                                <?php endif; ?>

                                            </div>

                                            <div class="content">
                                                <h6><a
                                                        href="<?php echo e(route('blog.details', $item->slug)); ?>"><?php echo e($item->getTranslation('blog_title')); ?></a>
                                                </h6>
                                                <span>
                                                    <img src="<?php echo e(asset('frontend/assets/images/icon/calender2.svg')); ?>"  alt="">
                                                    <?php echo e(customDateFormate($item->created_at)); ?>

                                                </span>
                                            </div>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </div>
                        <div class="job-card">
                            <div class="job-content">
                                <h5><?php echo e(translate('Hey! Do You Looking For Any Jobs')); ?>?</h5>
                                <p><?php echo e(translate('Job agencies may also offer additional services such as resume building')); ?>.</p>
                                <div class="find-job-btn">
                                    <a class="primry-btn-2 lg-btn" target="_blank" href="<?php echo e(isset(getThemeOption('page_setting')['job_list_url']) ? getThemeOption('page_setting')['job_list_url'] : 'javascript:void(0)'); ?>"><?php echo e(translate('Find Job')); ?></a>
                                </div>
                            </div>
                            <div class="job-img">
                                <img src="<?php echo e(asset('frontend/assets/images/blog/find-job.png')); ?>" alt="">
                            </div>
                        </div>
                    </div>
                </div>

            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>
<!-- ========== Blog Grid End============= -->
<?php $__env->startPush('post_scripts'); ?>
    <?php echo $__env->make('js.front.blog-search', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/pages/widgets/all-blog.blade.php ENDPATH**/ ?>