<script>
    (function($) {
        "use strict";
        $(document).on('click', '.languageTranslate', function() {

            let code = $(this).data('code');
            let action = $(this).data('action');
            $.ajax({
                url: action,
                type: "GET",
                dataType: "JSON",
                data: {
                    locale: code
                },
                success: function(data) {
                    if (data.status == true) {
                        toastr["success"](`${data.message}`)
                        window.location.reload();
                    }
                }
            });
        })

        //status
        //========== login-button

        $(document).on("click", '.login-button', function(e) {

            e.preventDefault();
            let id = $(this).data('id');
            let action = $(this).data('action');

            $.ajax({
                url: action,
                type: "POST",
                data: {
                    "id": id,
                    "_token": "<?php echo e(csrf_token()); ?>"
                },
                dataType: "JSON",
                success: function(data) {
                    if (data.status === true) {
                        window.open(data.url, '_blank');
                    } else if (data.status == false) {
                        toastr["error"](`${data.message}`);
                    }
                },
            });
        })
    })(jQuery);
</script>
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/js/common.blade.php ENDPATH**/ ?>