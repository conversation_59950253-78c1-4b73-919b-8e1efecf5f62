<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\LanguageRequest;
use App\Models\Translation;
use App\Repositories\Admin\LanguageRepostiory;
use Illuminate\Http\Request;

class LanguageController extends Controller
{
    public function __construct(protected LanguageRepostiory $language) {}

    /** all  resource get
     * ======= index ========
     *
     * @param Request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        if ($request->ajax()) {
            return $this->language->content($request);
        }

        return view('admin.pages.language.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.pages.language.create');
    }

    /**  new resource store
     * ============ store ===========
     *
     * @param LanguageRequest
     */
    public function store(LanguageRequest $request)
    {

        if (!$this->hasPermissions(['language.add'])) {
            toastr()->error(translate('You have no Permission'));
            return redirect()->route('admin.language.list');
        }
        $result = $this->language->create($request);
        if ($result['status']) {
            toastr()->success($result['message']);
        }

        return redirect()->route('admin.language.list');

    }

    /** specific resourece edit
     * ============ edit ========
     *
     * @param int id
     * @return response
     */
    public function edit($id)
    {
        $language = $this->language->getById($id);

        return view('admin.pages.language.edit', compact('language'));
    }

    /** specific resourece update
     * ========= udpate =====
     *
     * @param mixed  Request
     * @param int id
     * @return response
     */
    public function update(LanguageRequest $request)
    {
        if (!$this->hasPermissions(['language.edit'])) {
            toastr()->error(translate('You have no Permission'));
            return redirect()->route('admin.language.list');
        }
        $result = $this->language->update($request->id, $request);
        if ($result['status']) {
            toastr()->success(translate($result['message']));
        } else {
            toastr()->error(translate($result['message']));
        }
        return redirect()->route('admin.language.list');

    }

    /** specific resource delete by id
     * ======= destroy =======
     *
     * @param int id
     * @return response
     */
    public function destroy($id)
    {
        if (!$this->hasPermissions(['language.delete'])) {
            return $this->permissionDeniedResponse();
        }
        $data = $this->language->delete($id);
        if (!$data) {
            return response()->json([
                'status' => false,
                 'message' => translate('Something Went Wrong')
            ]);
        }
        return response()->json([
            'status' => true,
            'message' => translate( 'Language Delete Successfully')
        ]);
    }

    /** Language type status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['language.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->language->statusChange($id);
        return $this->formatResponse($result);

    }

    /** translate
     *=============== translate =====
     *
     * @param Request
     * @param int id
     * @return Response
     */
    public function translate(Request $request, $id)
    {

        $page_title = translate('Translation');
        $sort_search = null;
        $language = $this->language->getById($id);
        $lang_keys = Translation::where('lang', env('DEFAULT_LANGUAGE', 'en'));
        if ($request->has('search')) {
            $sort_search = $request->search;
            $lang_keys = $lang_keys->where('lang_key', 'like', '%'.$sort_search.'%');
        }
        $lang_keys = $lang_keys->paginate(50);
        return view('admin..pages.language.translation', compact('language', 'lang_keys', 'sort_search', 'page_title'));
    }

    /** Language key Value Store
     * ============= keyValueStore ========
     *
     * @param Request
     * @param int id
     * @return Response
     */
    public function keyValueStore(Request $request, $id)
    {

        if (!$this->hasPermissions(['language.edit'])) {
            toastr()->error(translate('You have no Permission'));
            return back();
        }

        $result = $this->language->valueStore($request, $id);
        if ($result['status']) {
            toastr()->success(translate($result['message']));
        }

        return back();

    }

    /**  Chanage Language
     *============= changeLanguage ==========
     *
     * @param Request
     * @return Response
     */
    public function changeLanguage(Request $request)
    {
        if (!$this->hasPermissions(['language.status'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->language->changeLanguage($request);

        return $this->formatResponse($result);

    }
}
