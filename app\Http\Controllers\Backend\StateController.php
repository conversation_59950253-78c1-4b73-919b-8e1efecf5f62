<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\StateRequest;
use App\Repositories\Admin\StateRepository;
use Illuminate\Http\Request;

class StateController extends Controller
{
    //

    public function __construct(protected StateRepository $state) {}

    /** Display the resource
     *========= index========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->state->content();
        }

        return view('admin.pages.state.index');
    }

    /** new resource store
     * ============ store =======
     *
     * @param Request
     * @return Response
     */
    public function store(StateRequest $request)
    {

        if (!$this->hasPermissions(['state.add', 'state.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->state->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $country = $this->state->getById($id);
        $data['name'] = $country->name;
        $data['id'] = $country->id;
        $data['lang'] = Request()->lang;
        $data['country_id'] = $country->country_id;

        return response()->json($data);
    }

    /** category status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {

        if (!$this->hasPermissions(['state.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->state->statusChange($id);
        return $this->formatResponse($result);

    }
}
