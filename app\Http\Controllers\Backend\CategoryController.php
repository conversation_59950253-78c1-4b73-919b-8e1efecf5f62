<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\JobCategoryRequest;
use App\Repositories\Admin\JobCategoryRepostitory;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function __construct(protected JobCategoryRepostitory $jobcategory) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->jobcategory->content();
        }

        return view('admin.pages.category.index');
    }

    /** new Job Category store
     * ========= store============
     *
     * @param App\Http\Requests\JobCategoryRequest
     * @return Response
     */
    public function store(JobCategoryRequest $request)
    {

        if (!$this->hasPermissions(['jobcategory.add'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->jobcategory->create($request);
        return $this->formatResponse($result);

    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $jobcategory = $this->jobcategory->getById($id);

        $data['category_name'] = $jobcategory->getTranslation('category_name', Request()->lang);
        $data['id'] = $jobcategory->id;
        $data['lang'] = Request()->lang;
        $data['category_image'] = $jobcategory->category_image;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        if (!$this->hasPermissions(['jobcategory.delete'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->jobcategory->destroy($id);

        return $this->formatResponse($result);

    }

    /** category status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['jobcategory.status'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->jobcategory->statusChange($id);
        return $this->formatResponse($result);

    }
}
