<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\FunctionalAreaRequest;
use App\Repositories\Admin\FunctionalAreaRepostitory;
use Illuminate\Http\Request;

class FunctionalAreaController extends Controller
{
    //

    public function __construct(protected FunctionalAreaRepostitory $functionalArea) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->functionalArea->content();
        }

        return view('admin.pages.functional-area.index');
    }

    /** new Job Sub Category store
     * ========= store============
     *
     * @param FunctionalAreaRequest
     * @return Response
     */
    public function store(FunctionalAreaRequest $request)
    {

        $result = $this->functionalArea->create($request);
        return $this->formatResponse($result);


    }

    /** specific Resource  edit by id
     * ==============  edit  ==============
     *
     * @param int id
     * @return Response
     */
    public function edit($id)
    {

        $functionalArea = $this->functionalArea->getById($id);

        $data['name'] = $functionalArea->getTranslation('name', Request()->lang);
        $data['id'] = $functionalArea->id;
        $data['lang'] = Request()->lang;

        return response()->json($data);
    }

    /** specific resource delete
     * ========= delete ==========
     *
     * @param int id
     * @return Response
     */
    public function delete($id)
    {
        $result = $this->functionalArea->destroy($id);
        return $this->formatResponse($result);
    }

    /** functionalArea status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        $result = $this->functionalArea->statusChange($id);
        return $this->formatResponse($result);
    }
}
