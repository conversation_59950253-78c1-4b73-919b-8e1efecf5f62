<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\User;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class CompanyAuthController extends Controller
{
    public function companyRegister()
    {
        return view('front.pages.company.auth.register');
    }

    public function companyLoginSuccess(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'confirm_password' => 'required|min:5|max:12',
            'password' => 'required|min:5|max:12|same:confirm_password',
            'g-recaptcha-response' => function ($attribute, $value, $fail) {
                $secretKey = config('services.recaptcha.secret');
                $response = $value;
                $userIP = $_SERVER['REMOTE_ADDR'];
                $url = "https://www.google.com/recaptcha/api/siteverify?secret=$secretKey&response=$response&remoteip=$userIP";
                $response = \file_get_contents($url);
                $response = json_decode($response);
                //            dd($response);
                if (! $response->success) {
                    Session::flash('g-recaptcha-response', 'Please Check Recaptcha');
                    Session::flash('alert-class', 'alert-danger');
                    $fail($attribute.'Google Recaptcha Failed');
                }
            },
        ]);
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => COMPANY_ROLE,
        ]);
        if ($user) {
            toastr()->success('', translate('Company Create Successfully'), ['positionClass' => 'toast-top-right']);
            return redirect()->route('home');
        }
        toastr()->error('', translate('Something is Wrong'), ['positionClass' => 'toast-top-right']);

        return redirect()->back();
    }
}
