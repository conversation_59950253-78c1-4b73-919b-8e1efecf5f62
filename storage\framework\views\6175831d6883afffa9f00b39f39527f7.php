<!-- ========== header============= -->


<header
    class="header-area <?php echo e(URL::full() == url('login') || URL::full() == url('signup') || request()->is('candidate/*') || request()->is('company/*') ? 'style-2' : 'style-1'); ?>  ">
    <div class="header-main-logo d-lg-block d-none">
        <a href="<?php echo e(url('/')); ?>">
            <?php if(isset(getThemeOption('site_logo')['site_logo'])): ?>
                <?php if(fileExists($folder = 'theme-options', $fileName = getThemeOption('site_logo')['site_logo']) == true &&
                        getThemeOption('site_logo')['site_logo'] !== ''): ?>
                    <img class="img-thumb" alt="logo"
                        src="<?php echo e(asset('storage/theme-options/' . getThemeOption('site_logo')['site_logo'])); ?>" />
                <?php else: ?>
                    <img alt="logo" class="img-fluid" src="<?php echo e(asset('frontend/assets/images/site_logo.png')); ?>">
                <?php endif; ?>
            <?php endif; ?>

        </a>
    </div>

    <div class="menu-topbar-area">
        <?php echo $__env->make('front.layouts.includes.top', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="menu-area">
            <?php echo $__env->make('front.layouts.includes.logo', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <div class="main-menu">
                <div class="mobile-logo-area d-lg-none d-flex justify-content-between align-items-center">
                    <div class="mobile-logo-wrap">
                        <a href="<?php echo e(url('/')); ?>">
                            <?php if(isset(getThemeOption('site_logo')['site_logo'])): ?>
                                <?php if(fileExists($folder = 'theme-options', $fileName = getThemeOption('site_logo')['site_logo']) == true &&
                                        getThemeOption('site_logo')['site_logo'] !== ''): ?>
                                    <img class="img-thumb" alt="logo"
                                        src="<?php echo e(asset('storage/theme-options/' . getThemeOption('site_logo')['site_logo'])); ?>" />
                                <?php else: ?>
                                    <img alt="logo" class="img-fluid"
                                        src="<?php echo e(asset('frontend/assets/images/site_logo.png')); ?>">
                                <?php endif; ?>
                            <?php endif; ?>
                        </a>
                    </div>
                    <div class="menu-close-btn">
                        <i class="bi bi-x-lg"></i>
                    </div>
                </div>

               <?php echo $__env->make('front.layouts.includes.menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
               <div class="for-mobile-menu d-lg-none d-block">
                <?php echo $__env->make('front.layouts.includes.mobile', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                  <?php
                       $header=  getThemeOption('header'.activeLanguage()) ?? 'headeren';
                       $header=  $header ? $header:  getThemeOption('headeren');
                  ?>
                    <div class="social-area">
                        <ul>
                            <?php if(isset($header['social'])): ?>
                                <?php $__currentLoopData = $header['social']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><a href="<?php echo e($social['url']); ?>"><i class="<?php echo e($social['class']); ?>"></i></a></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>

             <?php echo $__env->make('front.layouts.includes.right-menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>
</header>
<!-- ========== header end============= -->
<?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/layouts/includes/header.blade.php ENDPATH**/ ?>