<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Admin\SocialLink;
use Illuminate\Http\Request;

class SocialLinkController extends Controller
{
    public function socialLinkUpdate(Request $request)
    {
        $socialLink = SocialLink::where('id', $request->id)->first();
        if (!$socialLink) {
            Toastr()->error(translate('Something is Wrong'), '', ['positionClass' => 'toast-bottom-right']);
            return redirect()->back();
        }
        $update = $find->update([
            'facebook' => $request->facebook,
            'twitter' => $request->twitter,
            'linkedin' => $request->linkedin,
            'instagram' => $request->instagram,
        ]);
        if ($update) {
            Toastr()->success(translate('Social Update', 'Successfully'), ['positionClass' => 'toast-bottom-right']);
            return redirect()->back();
        }
    }
}
