{"__meta": {"id": "01JXZ1PNYZ712BFQ1S9BPVK1A4", "datetime": "2025-06-17 13:36:50", "utime": **********.655662, "method": "GET", "uri": "/find-job?featured=&page=null&category=&jobtype=&careerlevel=&experience=3,4&daterange=&minsalery=2000&maxsalery=6000&sort=ASC&widget_name=job-list&item_show=14", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.324274, "end": **********.655676, "duration": 0.3314018249511719, "duration_str": "331ms", "measures": [{"label": "Booting", "start": **********.324274, "relative_start": 0, "end": **********.551361, "relative_end": **********.551361, "duration": 0.*****************, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.55137, "relative_start": 0.****************, "end": **********.655677, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.561202, "relative_start": 0.*****************, "end": **********.567755, "relative_end": **********.567755, "duration": 0.006552934646606445, "duration_str": "6.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.653423, "relative_start": 0.****************, "end": **********.653799, "relative_end": **********.653799, "duration": 0.00037598609924316406, "duration_str": "376μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "1x front.pages.jobs.job-section", "param_count": null, "params": [], "start": **********.60899, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.phpfront.pages.jobs.job-section", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fjobs%2Fjob-section.blade.php&line=1", "ajax": false, "filename": "job-section.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.pages.jobs.job-section"}, {"name": "1x front.vendor.pagination.custom", "param_count": null, "params": [], "start": **********.64981, "type": "blade", "hash": "bladeD:\\xampp\\htdocs\\empzio\\resources\\views/front/vendor/pagination/custom.blade.phpfront.vendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}, "render_count": 1, "name_original": "front.vendor.pagination.custom"}]}, "route": {"uri": "GET {any}", "middleware": "web", "controller": "App\\Http\\Controllers\\Frontend\\PagesController@customPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PagesController.php:15-70</a>"}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.010519999999999998, "accumulated_duration_str": "10.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `pages` where `page_route` = 'find-job' limit 1", "type": "query", "params": [], "bindings": ["find-job"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5892968, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:27", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=27", "ajax": false, "filename": "PagesController.php", "line": "27"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 5.513}, {"sql": "select * from `widgets_contents` where `widgets_contents`.`page_id` in (16) and `status` = 1 order by `position` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.591351, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:27", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=27", "ajax": false, "filename": "PagesController.php", "line": "27"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 5.513, "width_percent": 4.373}, {"sql": "select * from `widgets_contents` where (`page_id` = 16 and `widget_slug` = 'job-list') limit 1", "type": "query", "params": [], "bindings": [16, "job-list"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.592834, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Helper.php:1198", "source": {"index": 16, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 1198}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=1198", "ajax": false, "filename": "Helper.php", "line": "1198"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 9.886, "width_percent": 3.992}, {"sql": "select count(*) as aggregate from `jobs` where exists (select * from `job_experiences` inner join `job_job_experience` on `job_experiences`.`id` = `job_job_experience`.`experience_id` where `jobs`.`id` = `job_job_experience`.`job_id` and `experience_id` in ('3', '4')) and (`salary_mode` = 'range' and `min_salary` between '2000' and '6000' or (`max_salary` between '6000' and '6000') or `salary_mode` != 'range') and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `job_deadline` >= '2025-06-17 13:36:50' and `status` = 1", "type": "query", "params": [], "bindings": ["3", "4", "range", "2000", "6000", "6000", "6000", "range", 1, 1, 1, "2025-06-17 13:36:50", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\JobRepository.php", "line": 929}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 98}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.599368, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "JobRepository.php:929", "source": {"index": 16, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\JobRepository.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FJobRepository.php&line=929", "ajax": false, "filename": "JobRepository.php", "line": "929"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 13.878, "width_percent": 9.506}, {"sql": "select `id`, `job_title`, `slug`, `company_id`, `job_deadline`, `job_vacancy`, `job_image`, `salary_mode`, `min_salary`, `max_salary`, `salary_type` from `jobs` where exists (select * from `job_experiences` inner join `job_job_experience` on `job_experiences`.`id` = `job_job_experience`.`experience_id` where `jobs`.`id` = `job_job_experience`.`job_id` and `experience_id` in ('3', '4')) and (`salary_mode` = 'range' and `min_salary` between '2000' and '6000' or (`max_salary` between '6000' and '6000') or `salary_mode` != 'range') and exists (select `id`, `company_name`, `slug`, `status` from `companies` where `jobs`.`company_id` = `companies`.`id` and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1) and `job_deadline` >= '2025-06-17 13:36:50' and `status` = 1 order by `id` asc limit 14 offset 0", "type": "query", "params": [], "bindings": ["3", "4", "range", "2000", "6000", "6000", "6000", "range", 1, 1, 1, "2025-06-17 13:36:50", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\JobRepository.php", "line": 929}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 98}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6014962, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "JobRepository.php:929", "source": {"index": 16, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\JobRepository.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FJobRepository.php&line=929", "ajax": false, "filename": "JobRepository.php", "line": "929"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 23.384, "width_percent": 9.221}, {"sql": "select `job_experiences`.*, `job_job_experience`.`job_id` as `pivot_job_id`, `job_job_experience`.`experience_id` as `pivot_experience_id`, `job_job_experience`.`created_at` as `pivot_created_at`, `job_job_experience`.`updated_at` as `pivot_updated_at` from `job_experiences` inner join `job_job_experience` on `job_experiences`.`id` = `job_job_experience`.`experience_id` where `job_job_experience`.`job_id` in (19, 24, 25) and `experience_id` in ('3', '4')", "type": "query", "params": [], "bindings": ["3", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\JobRepository.php", "line": 929}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 98}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.603735, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "JobRepository.php:929", "source": {"index": 20, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\JobRepository.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FJobRepository.php&line=929", "ajax": false, "filename": "JobRepository.php", "line": "929"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 32.605, "width_percent": 5.798}, {"sql": "select `id`, `company_name`, `slug`, `status` from `companies` where `companies`.`id` in (46, 63, 65) and exists (select * from `users` where `companies`.`user_id` = `users`.`id` and `status` = 1 and `is_email_verified` = 1) and `status` = 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\JobRepository.php", "line": 929}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 98}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Frontend\\PagesController.php", "line": 38}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.606597, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "JobRepository.php:929", "source": {"index": 21, "namespace": null, "name": "app/Repositories/JobRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\JobRepository.php", "line": 929}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FJobRepository.php&line=929", "ajax": false, "filename": "JobRepository.php", "line": "929"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 38.403, "width_percent": 5.798}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 19 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.619325, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 44.202, "width_percent": 5.323}, {"sql": "select * from `users` where `id` = 265 limit 1", "type": "query", "params": [], "bindings": [265], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 21, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 52}], "start": **********.62573, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 49.525, "width_percent": 4.753}, {"sql": "select `jobs`.*, `book_mark_jobs`.`user_id` as `pivot_user_id`, `book_mark_jobs`.`job_id` as `pivot_job_id`, `book_mark_jobs`.`created_at` as `pivot_created_at`, `book_mark_jobs`.`updated_at` as `pivot_updated_at` from `jobs` inner join `book_mark_jobs` on `jobs`.`id` = `book_mark_jobs`.`job_id` where `book_mark_jobs`.`user_id` in (265)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.627925, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Helper.php:83", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=83", "ajax": false, "filename": "Helper.php", "line": "83"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 54.278, "width_percent": 8.08}, {"sql": "select * from `job_job_types` where `job_job_types`.`job_id` = 19 and `job_job_types`.`job_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.630406, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "front.pages.jobs.job-section:72", "source": {"index": 20, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fjobs%2Fjob-section.blade.php&line=72", "ajax": false, "filename": "job-section.blade.php", "line": "72"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 62.357, "width_percent": 4.943}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 24 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.6332982, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 67.3, "width_percent": 4.278}, {"sql": "select `jobs`.*, `book_mark_jobs`.`user_id` as `pivot_user_id`, `book_mark_jobs`.`job_id` as `pivot_job_id`, `book_mark_jobs`.`created_at` as `pivot_created_at`, `book_mark_jobs`.`updated_at` as `pivot_updated_at` from `jobs` inner join `book_mark_jobs` on `jobs`.`id` = `book_mark_jobs`.`job_id` where `book_mark_jobs`.`user_id` in (265)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.636753, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Helper.php:83", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=83", "ajax": false, "filename": "Helper.php", "line": "83"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 71.578, "width_percent": 5.989}, {"sql": "select * from `job_job_types` where `job_job_types`.`job_id` = 24 and `job_job_types`.`job_id` is not null", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.638403, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "front.pages.jobs.job-section:72", "source": {"index": 20, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fjobs%2Fjob-section.blade.php&line=72", "ajax": false, "filename": "job-section.blade.php", "line": "72"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.567, "width_percent": 4.658}, {"sql": "select * from `job_translations` where `job_translations`.`job_id` = 25 and `job_translations`.`job_id` is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, {"index": 21, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.641066, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Job.php:22", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Job.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Job.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=22", "ajax": false, "filename": "Job.php", "line": "22"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 82.224, "width_percent": 4.943}, {"sql": "select `jobs`.*, `book_mark_jobs`.`user_id` as `pivot_user_id`, `book_mark_jobs`.`job_id` as `pivot_job_id`, `book_mark_jobs`.`created_at` as `pivot_created_at`, `book_mark_jobs`.`updated_at` as `pivot_updated_at` from `jobs` inner join `book_mark_jobs` on `jobs`.`id` = `book_mark_jobs`.`job_id` where `book_mark_jobs`.`user_id` in (265)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.644688, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Helper.php:83", "source": {"index": 19, "namespace": null, "name": "app/Helper/Helper.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Helper\\Helper.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHelper%2FHelper.php&line=83", "ajax": false, "filename": "Helper.php", "line": "83"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 87.167, "width_percent": 7.129}, {"sql": "select * from `job_job_types` where `job_job_types`.`job_id` = 25 and `job_job_types`.`job_id` is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.64678, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "front.pages.jobs.job-section:72", "source": {"index": 20, "namespace": "view", "name": "front.pages.jobs.job-section", "file": "D:\\xampp\\htdocs\\empzio\\resources\\views/front/pages/jobs/job-section.blade.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fresources%2Fviews%2Ffront%2Fpages%2Fjobs%2Fjob-section.blade.php&line=72", "ajax": false, "filename": "job-section.blade.php", "line": "72"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 94.297, "width_percent": 5.703}]}, "models": {"data": {"App\\Models\\Admin\\JobJobType": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJobJobType.php&line=1", "ajax": false, "filename": "JobJobType.php", "line": "?"}}, "App\\Models\\Admin\\Job": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJob.php&line=1", "ajax": false, "filename": "Job.php", "line": "?"}}, "App\\Models\\Admin\\JobExperience": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJobExperience.php&line=1", "ajax": false, "filename": "JobExperience.php", "line": "?"}}, "App\\Models\\Admin\\Company": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Admin\\WidgetsContents": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=1", "ajax": false, "filename": "WidgetsContents.php", "line": "?"}}, "App\\Models\\Admin\\Pages": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FPages.php&line=1", "ajax": false, "filename": "Pages.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 17, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/lang/flags/in.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/theme-option\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "265"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/find-job?careerlevel=&category=&daterange=&experience=3%2C4&featured=&item_sho...", "action_name": null, "controller_action": "App\\Http\\Controllers\\Frontend\\PagesController@customPage", "uri": "GET {any}", "controller": "App\\Http\\Controllers\\Frontend\\PagesController@customPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FFrontend%2FPagesController.php&line=15\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PagesController.php:15-70</a>", "middleware": "web", "duration": "335ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1761035702 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str title=\"4 characters\">null</span>\"\n  \"<span class=sf-dump-key>category</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>jobtype</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>careerlevel</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>experience</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3,4</span>\"\n  \"<span class=sf-dump-key>daterange</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>minsalery</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2000</span>\"\n  \"<span class=sf-dump-key>maxsalery</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6000</span>\"\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ASC</span>\"\n  \"<span class=sf-dump-key>widget_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">job-list</span>\"\n  \"<span class=sf-dump-key>item_show</span>\" => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761035702\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-93690483 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-93690483\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1727747030 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/find-job</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6ImZhS1hxaG5ieGRiRVFiYjZQbzVWa1E9PSIsInZhbHVlIjoidFBlNFV1U21xZzFnL052ZUFsd2ZXRDQ4NW5ROExrRjFHdVJNVnZpMGlGeGZvUmlYNngrWXNNbzFlL2cyVHlNVVU4UERPVXRDRTNhNGdEb3ExbTB3RFlzSFhNLy9JMnpYM3V5TU5KY21WK2hyR001NXBoRStSekNjTk1YK3l4WXMiLCJtYWMiOiJmZmYwMzc2OTFkNTM3NWIyZGE0NjFlYTVkODczMDViM2M2YmEwODc2YmJjYTdlZDQzYTA2OTFmMzU5MTRlNWY4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IldseFdwZHNESVRsNVRHZ0VCR243eGc9PSIsInZhbHVlIjoiUlI3OHpSTHY5ZnhuRStpT2VnTlRQeXpoNDlQTlpGclRKTXlHYktMTlkzaW5KWWxsUkNqUHd2SmQ2dklqSHlsKytJZ0g3T1I2T2l6OGI2SCtRcXkybDVPT2VmK2tSdjBKdjFTcXgxWWZEbG9icGd1RHVUc3AzZWRKcmZ6N3lucEQiLCJtYWMiOiJjZjc2NDE2MTVlNzA3M2Y5M2IyNDg3ZmQ0ZTk3OGQ1N2EwZTI1M2FkMWRmYmViMWNiZGZlODMzZjI3MzAzOTFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1727747030\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-97595438 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cxG5K19WWqXXLlQZnahwNoDJ8wr1XrsemGRzohNc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97595438\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-837976703 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:36:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-837976703\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-257916972 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/storage/lang/flags/in.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/theme-option</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>265</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257916972\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/find-job?careerlevel=&category=&daterange=&experience=3%2C4&featured=&item_sho...", "controller_action": "App\\Http\\Controllers\\Frontend\\PagesController@customPage"}, "badge": null}}