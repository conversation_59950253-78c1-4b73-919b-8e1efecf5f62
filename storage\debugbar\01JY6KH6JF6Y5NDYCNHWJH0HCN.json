{"__meta": {"id": "01JY6KH6JF6Y5NDYCNHWJH0HCN", "datetime": "2025-06-20 12:03:06", "utime": **********.448514, "method": "POST", "uri": "/admin/pages/widget-save-by-page", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.101444, "end": **********.448529, "duration": 0.34708499908447266, "duration_str": "347ms", "measures": [{"label": "Booting", "start": **********.101444, "relative_start": 0, "end": **********.362308, "relative_end": **********.362308, "duration": 0.****************, "duration_str": "261ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.362321, "relative_start": 0.****************, "end": **********.448531, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "86.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.373674, "relative_start": 0.****************, "end": **********.378517, "relative_end": **********.378517, "duration": 0.004842996597290039, "duration_str": "4.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.445698, "relative_start": 0.****************, "end": **********.446158, "relative_end": **********.446158, "duration": 0.00045990943908691406, "duration_str": "460μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/pages/widget-save-by-page", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "controller": "App\\Http\\Controllers\\Backend\\PagesController@widgetUpdateByPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=142\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.pages.widget.save", "prefix": "admin/pages", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=142\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/PagesController.php:142-174</a>"}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01433, "accumulated_duration_str": "14.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.395484, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 4.187}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": "middleware", "name": "prevent-back-history", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Middleware\\PreventBackHistory.php", "line": 19}], "start": **********.4055421, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 4.187, "width_percent": 4.327}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": [1, "App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.416881, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 8.514, "width_percent": 3.489}, {"sql": "select * from `widgets_contents` where `ui_card_number` = '348ffac' and `widget_slug` = 'hero-section' limit 1", "type": "query", "params": [], "bindings": ["348ffac", "hero-section"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 152}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4217181, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:152", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=152", "ajax": false, "filename": "PagesController.php", "line": "152"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 12.003, "width_percent": 3.07}, {"sql": "select * from `widget_content_translations` where (`widget_content_id` = 3 and `lang` = 'en') limit 1", "type": "query", "params": [], "bindings": [3, "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 328}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 161}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4235902, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PagesController.php:328", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=328", "ajax": false, "filename": "PagesController.php", "line": "328"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 15.073, "width_percent": 3.21}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'empzio' and table_name = 'widget_content_translations' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 328}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 161}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4250321, "duration": 0.00945, "duration_str": "9.45ms", "memory": 0, "memory_str": null, "filename": "PagesController.php:328", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=328", "ajax": false, "filename": "PagesController.php", "line": "328"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 18.283, "width_percent": 65.946}, {"sql": "update `widget_content_translations` set `widget_content` = '{\\\"banner_tag\\\":\\\"Your Dream Job\\\",\\\"title\\\":\\\"Discover Work <strong>That <span>Works for You<\\\\/span><\\\\/strong>\\\",\\\"description\\\":\\\"Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!\\\",\\\"keyword_one\\\":null,\\\"keyword_two\\\":\\\"Companiesgg\\\",\\\"keyword_three\\\":\\\"Candidatesgg\\\",\\\"keyword_four\\\":\\\"New Jobsgg\\\"}', `widget_content_translations`.`updated_at` = '2025-06-20 12:03:06' where `id` = 47", "type": "query", "params": [], "bindings": ["{\"banner_tag\":\"Your Dream Job\",\"title\":\"Discover Work <strong>That <span>Works for You<\\/span><\\/strong>\",\"description\":\"Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!\",\"keyword_one\":null,\"keyword_two\":\"Companiesgg\",\"keyword_three\":\"Candidatesgg\",\"keyword_four\":\"New Jobsgg\"}", "2025-06-20 12:03:06", 47], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 328}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.43613, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "PagesController.php:328", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/PagesController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\PagesController.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=328", "ajax": false, "filename": "PagesController.php", "line": "328"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 84.229, "width_percent": 15.771}]}, "models": {"data": {"App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Admin\\WidgetsContents": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetsContents.php&line=1", "ajax": false, "filename": "WidgetsContents.php", "line": "?"}}, "App\\Models\\Admin\\WidgetContentTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FWidgetContentTranslation.php&line=1", "ajax": false, "filename": "WidgetContentTranslation.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => widget.edit,\n  target => null,\n  result => true,\n  admin => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-706511544 data-indent-pad=\"  \"><span class=sf-dump-note>widget.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">widget.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706511544\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.420618, "xdebug_link": null}]}, "session": {"_token": "hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/pages/edit/1?lang=en\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/lang/flags/in.png\"\n]", "locale": "en", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/pages/widget-save-by-page", "action_name": "admin.pages.widget.save", "controller_action": "App\\Http\\Controllers\\Backend\\PagesController@widgetUpdateByPage", "uri": "POST admin/pages/widget-save-by-page", "controller": "App\\Http\\Controllers\\Backend\\PagesController@widgetUpdateByPage<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=142\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/pages", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FPagesController.php&line=142\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/PagesController.php:142-174</a>", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "duration": "348ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-965763591 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-965763591\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2128529216 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM</span>\"\n  \"<span class=sf-dump-key>ui_card_number</span>\" => \"<span class=sf-dump-str title=\"7 characters\">348ffac</span>\"\n  \"<span class=sf-dump-key>page_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>widget_slug</span>\" => \"<span class=sf-dump-str title=\"12 characters\">hero-section</span>\"\n  \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>banner_tag</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Your Dream Job</span>\"\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Discover Work &lt;strong&gt;That &lt;span&gt;Works for You&lt;/span&gt;&lt;/strong&gt;</span>\"\n      \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"122 characters\">Browse, apply, and get hired faster with our smart search tools and real-time job alerts. Your career journey starts here!</span>\"\n      \"<span class=sf-dump-key>keyword_one</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>keyword_two</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Companiesgg</span>\"\n      \"<span class=sf-dump-key>keyword_three</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Candidatesgg</span>\"\n      \"<span class=sf-dump-key>keyword_four</span>\" => \"<span class=sf-dump-str title=\"10 characters\">New Jobsgg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128529216\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2141984321 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1580</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary2Qxyo4vAWowlkl4o</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/admin/pages/edit/1?lang=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6Ijh4bHd1UmRGV3Z4TVRsaUJrOEh5d0E9PSIsInZhbHVlIjoiYWx2NUgvOUpTOGh6SUhIN1JHS3ZDemdBVVIzRUdmdDNzMVhDNW5QWGh4aU8xaW9Rd0FxclZRZDcxKzdCNXdKcU0xbzVlVjk2Mjhkc0F5Q1pnNkRPWVo1T0RpM3Z1MURWRVVoSnlrVHd4d2RXRjBNcTlCai9naDQ1MTczUHE2dnoiLCJtYWMiOiI2ZDY0ZTVhOWFiYjc5ZGZiN2YyNGI4ZjRjOGNjYzgwNGI4NDFmMGZhYTY3ZWM1MDRjZWVjNTMxNTk0NGFmYjk0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImtFdFVtdktpMU5pK2VPUk5jbm5iTEE9PSIsInZhbHVlIjoiZlp5Q0YzQ2RjTnppRmJIRy9jdkY3enJ6SlRQNzRScTlORHB0by9ZdlY4QU54amhCc1NoNE42TXI4OWxjVnBjK1VDQXFzTkVMM01lbmFjN0xYUkNJQU50dGtDN1gydHZYajZJcmg4Y2NndXIyYnNma1N3cnI1dm9XaGdOclRJbEYiLCJtYWMiOiIxNmFkYWQxYzZhOWEzZDE1YzUxODE3Yzk5NDdjZTE1YzczYjY0ZGNmODEzYzdkODI1ODBmZDZhYzUwMTc1ZTViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141984321\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-757196062 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">73NVybykaJRblf91BMgO3Gbz1VJmhdzXGQr1QbOg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757196062\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-460998728 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 20 Jun 2025 12:03:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460998728\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1129891660 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hTyl6Y1cRsCRwwNFbXhUuHWrF5pbJdMJhRWLRREM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/admin/pages/edit/1?lang=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/storage/lang/flags/in.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129891660\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/pages/widget-save-by-page", "action_name": "admin.pages.widget.save", "controller_action": "App\\Http\\Controllers\\Backend\\PagesController@widgetUpdateByPage"}, "badge": null}}