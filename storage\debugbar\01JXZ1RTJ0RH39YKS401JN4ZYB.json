{"__meta": {"id": "01JXZ1RTJ0RH39YKS401JN4ZYB", "datetime": "2025-06-17 13:38:00", "utime": **********.897418, "method": "GET", "uri": "/admin/candidate?draw=1&columns%5B0%5D%5Bdata%5D=Candidate&columns%5B0%5D%5Bname%5D=Candidate&columns%5B0%5D%5Bsearchable%5D=true&columns%5B0%5D%5Borderable%5D=true&columns%5B0%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B0%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B1%5D%5Bdata%5D=Profession&columns%5B1%5D%5Bname%5D=Profession&columns%5B1%5D%5Bsearchable%5D=true&columns%5B1%5D%5Borderable%5D=true&columns%5B1%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B1%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B2%5D%5Bdata%5D=Role%2FPosition&columns%5B2%5D%5Bname%5D=Role%2FPosition&columns%5B2%5D%5Bsearchable%5D=true&columns%5B2%5D%5Borderable%5D=true&columns%5B2%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B2%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B3%5D%5Bdata%5D=Experience&columns%5B3%5D%5Bname%5D=Experience&columns%5B3%5D%5Bsearchable%5D=true&columns%5B3%5D%5Borderable%5D=true&columns%5B3%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B3%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B4%5D%5Bdata%5D=Joining+Date&columns%5B4%5D%5Bname%5D=Joining+Date&columns%5B4%5D%5Bsearchable%5D=true&columns%5B4%5D%5Borderable%5D=true&columns%5B4%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B4%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B5%5D%5Bdata%5D=Verified&columns%5B5%5D%5Bname%5D=Verified&columns%5B5%5D%5Bsearchable%5D=true&columns%5B5%5D%5Borderable%5D=true&columns%5B5%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B5%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B6%5D%5Bdata%5D=Status&columns%5B6%5D%5Bname%5D=Status&columns%5B6%5D%5Bsearchable%5D=true&columns%5B6%5D%5Borderable%5D=true&columns%5B6%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B6%5D%5Bsearch%5D%5Bregex%5D=false&columns%5B7%5D%5Bdata%5D=action&columns%5B7%5D%5Bname%5D=action&columns%5B7%5D%5Bsearchable%5D=true&columns%5B7%5D%5Borderable%5D=false&columns%5B7%5D%5Bsearch%5D%5Bvalue%5D=&columns%5B7%5D%5Bsearch%5D%5Bregex%5D=false&order%5B0%5D%5Bcolumn%5D=0&order%5B0%5D%5Bdir%5D=asc&start=0&length=10&search%5Bvalue%5D=&search%5Bregex%5D=false&_=1750167478609", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[13:38:00] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php on line 49", "message_html": null, "is_string": false, "label": "warning", "time": **********.704315, "xdebug_link": null, "collector": "log"}, {"message": "[13:38:00] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php on line 49", "message_html": null, "is_string": false, "label": "warning", "time": **********.707786, "xdebug_link": null, "collector": "log"}, {"message": "[13:38:00] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php on line 49", "message_html": null, "is_string": false, "label": "warning", "time": **********.719169, "xdebug_link": null, "collector": "log"}, {"message": "[13:38:00] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php on line 49", "message_html": null, "is_string": false, "label": "warning", "time": **********.726245, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.356175, "end": **********.897443, "duration": 0.5412681102752686, "duration_str": "541ms", "measures": [{"label": "Booting", "start": **********.356175, "relative_start": 0, "end": **********.617866, "relative_end": **********.617866, "duration": 0.****************, "duration_str": "262ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.617888, "relative_start": 0.*****************, "end": **********.897445, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "280ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.627905, "relative_start": 0.****************, "end": **********.632013, "relative_end": **********.632013, "duration": 0.0041081905364990234, "duration_str": "4.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.893825, "relative_start": 0.****************, "end": **********.894247, "relative_end": **********.894247, "duration": 0.0004220008850097656, "duration_str": "422μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET admin/candidate", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "controller": "App\\Http\\Controllers\\Backend\\CandidateController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCandidateController.php&line=21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "admin.candidate", "prefix": "admin/candidate", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCandidateController.php&line=21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/CandidateController.php:21-28</a>"}, "queries": {"count": 56, "nb_statements": 56, "nb_visible_statements": 56, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03274, "accumulated_duration_str": "32.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.651027, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 0, "width_percent": 1.833}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 235}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Middlewares/RoleMiddleware.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\RoleMiddleware.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": "middleware", "name": "prevent-back-history", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Middleware\\PreventBackHistory.php", "line": 19}], "start": **********.66067, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 1.833, "width_percent": 1.894}, {"sql": "select `candidates`.*, (select count(*) from `apply_jobs` where `candidates`.`id` = `apply_jobs`.`candidate_id`) as `apply_jobs_count` from `candidates` order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 43}, {"index": 16, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 52}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Backend/CandidateController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CandidateController.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.665879, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "CandidateRepository.php:43", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCandidateRepository.php&line=43", "ajax": false, "filename": "CandidateRepository.php", "line": "43"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 3.726, "width_percent": 7.025}, {"sql": "select * from `users` where `users`.`id` in (2, 129, 132, 189, 190, 206, 207, 208, 209, 210, 211, 215, 220, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 259, 266, 269, 280, 283, 386, 431, 432, 433, 434)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 43}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 52}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/CandidateController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CandidateController.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6708412, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "CandidateRepository.php:43", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCandidateRepository.php&line=43", "ajax": false, "filename": "CandidateRepository.php", "line": "43"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 10.751, "width_percent": 2.963}, {"sql": "select * from `professions` where `professions`.`id` in (2, 4, 6, 7, 8, 11, 12, 14, 15, 16, 28, 30, 35)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 43}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 52}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/CandidateController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CandidateController.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.67399, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CandidateRepository.php:43", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCandidateRepository.php&line=43", "ajax": false, "filename": "CandidateRepository.php", "line": "43"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 13.714, "width_percent": 1.68}, {"sql": "select * from `job_roles` where `job_roles`.`id` in (1, 4, 5, 6, 7, 8, 16, 17, 18, 21, 22, 26, 34)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 43}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 52}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Backend/CandidateController.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Http\\Controllers\\Backend\\CandidateController.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.676226, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "CandidateRepository.php:43", "source": {"index": 20, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FRepositories%2FCandidateRepository.php&line=43", "ajax": false, "filename": "CandidateRepository.php", "line": "43"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 15.394, "width_percent": 1.772}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 67 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [67], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.699358, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 17.166, "width_percent": 3.665}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 66 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [66], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.704959, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 20.831, "width_percent": 1.833}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 65 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7083728, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 22.663, "width_percent": 1.741}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 64 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [64], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.712547, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 24.404, "width_percent": 1.68}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 56 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [56], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.71635, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 26.084, "width_percent": 1.649}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 55 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.719974, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 27.734, "width_percent": 1.436}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 52 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7236311, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 29.169, "width_percent": 1.405}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 46 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.727054, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 30.574, "width_percent": 1.374}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 43 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.731279, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 31.949, "width_percent": 1.619}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 42 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.735046, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 33.568, "width_percent": 1.283}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 41 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7384272, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 34.85, "width_percent": 1.344}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 40 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7416792, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 36.194, "width_percent": 1.405}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 39 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.745975, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 37.599, "width_percent": 1.649}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 38 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7495122, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 39.249, "width_percent": 1.436}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 37 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.752876, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 40.684, "width_percent": 1.374}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 36 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7561371, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 42.059, "width_percent": 1.374}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 35 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7592862, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 43.433, "width_percent": 1.374}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 34 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.763307, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 44.808, "width_percent": 3.238}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 33 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.768068, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 48.045, "width_percent": 1.466}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 32 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7714748, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 49.511, "width_percent": 1.252}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 31 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.774643, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 50.764, "width_percent": 1.527}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 30 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.778343, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 52.291, "width_percent": 2.841}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 29 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.78244, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 55.131, "width_percent": 1.466}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 28 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7856898, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 56.597, "width_percent": 1.313}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 27 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.7888482, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 57.911, "width_percent": 1.344}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 26 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.792045, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 59.255, "width_percent": 1.374}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 25 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.79636, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 60.629, "width_percent": 1.619}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 24 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.799909, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 62.248, "width_percent": 1.466}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 23 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.803102, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 63.714, "width_percent": 1.374}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 22 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8064, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 65.089, "width_percent": 1.68}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 21 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.81046, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 66.768, "width_percent": 1.649}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 20 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.81461, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 68.418, "width_percent": 1.619}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 19 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.818038, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 70.037, "width_percent": 1.405}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 18 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.821227, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 71.442, "width_percent": 1.374}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 17 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.824962, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 72.816, "width_percent": 1.741}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 16 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8296008, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 74.557, "width_percent": 1.772}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 15 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.833199, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 76.329, "width_percent": 1.588}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 14 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.836507, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 77.917, "width_percent": 1.466}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 13 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.839756, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 79.383, "width_percent": 1.527}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 12 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.843089, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 80.91, "width_percent": 1.466}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 11 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.847398, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 82.376, "width_percent": 1.68}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 10 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.850742, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 84.056, "width_percent": 1.405}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 9 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.854011, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 85.461, "width_percent": 1.374}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 8 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.857314, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 86.836, "width_percent": 1.405}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 7 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8608549, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 88.241, "width_percent": 1.802}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 6 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8649678, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 90.043, "width_percent": 1.833}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 5 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.8687432, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 91.875, "width_percent": 1.894}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 2 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.872231, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 93.769, "width_percent": 1.985}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 3 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.875637, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 95.754, "width_percent": 2.138}, {"sql": "select * from `candidate_translations` where `candidate_translations`.`candidate_id` = 1 and `candidate_translations`.`candidate_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Repositories/CandidateRepository.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Repositories\\CandidateRepository.php", "line": 90}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\xampp\\htdocs\\empzio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}], "start": **********.879972, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Candidate.php:33", "source": {"index": 20, "namespace": null, "name": "app/Models/Admin/Candidate.php", "file": "D:\\xampp\\htdocs\\empzio\\app\\Models\\Admin\\Candidate.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=33", "ajax": false, "filename": "Candidate.php", "line": "33"}, "connection": "emp<PERSON>", "explain": null, "start_percent": 97.892, "width_percent": 2.108}]}, "models": {"data": {"App\\Models\\Admin\\Candidate": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidate.php&line=1", "ajax": false, "filename": "Candidate.php", "line": "?"}}, "App\\Models\\User": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin\\Profession": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FProfession.php&line=1", "ajax": false, "filename": "Profession.php", "line": "?"}}, "App\\Models\\Admin\\JobRole": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FJobRole.php&line=1", "ajax": false, "filename": "JobRole.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Admin\\CandidateTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FModels%2FAdmin%2FCandidateTranslation.php&line=1", "ajax": false, "filename": "CandidateTranslation.php", "line": "?"}}}, "count": 129, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr", "locale": "en", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/lang/flags/in.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/admin/theme-option\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "adminLogin": "user_log_in", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/candidate?_=1750167478609&columns%5B0%5D%5Bdata%5D=Candidate&columns%5B0...", "action_name": "admin.candidate", "controller_action": "App\\Http\\Controllers\\Backend\\CandidateController@index", "uri": "GET admin/candidate", "controller": "App\\Http\\Controllers\\Backend\\CandidateController@index<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCandidateController.php&line=21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/candidate", "file": "<a href=\"phpstorm://open?file=D%3A%2Fxampp%2Fhtdocs%2Fempzio%2Fapp%2FHttp%2FControllers%2FBackend%2FCandidateController.php&line=21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/CandidateController.php:21-28</a>", "middleware": "web, auth:admin, prevent-back-history, role:SuperAdmin", "duration": "548ms", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1283779032 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Candidate</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Candidate</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Profession</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Profession</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Role/Position</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Role/Position</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Experience</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Experience</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Joining Date</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Joining Date</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Verified</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Verified</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1750167478609</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283779032\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-320367733 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-320367733\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1972042937 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/candidate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,am;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1052 characters\">__stripe_mid=c2829073-0f05-428f-85c8-6fac2f770053e0886b; twk_uuid_658d2c0670c9f2407f83ed78=%7B%22uuid%22%3A%221.PUtPKU7ij9yWHKo0AzXeUarZCuOKLKyBeemi7CKkFvHJvX0KOCwzRhIEuFQ4xJrkLjYs2pn5z2DOBzvM73g8CmD6rZGuscmevgVNFojtQw9BEnVrS%22%2C%22version%22%3A3%2C%22domain%22%3A%22127.0.0.1%22%2C%22ts%22%3A1750074835455%7D; laravel_cookie_consent=1; XSRF-TOKEN=eyJpdiI6InFaSzVWOHp3a3FoS2g5c3l0OW1MdEE9PSIsInZhbHVlIjoiNGpMTEhvSlFIdGZHckVJa1BmK0hlSGRJWUxGcVlyYW15NGJCaXIrTmtWM3VmSG1BaEVyWnk2dVNMM05oZTVYZzRYYlZ6ZEpiTnJvdWJGUi9MRmxWVjZPNE5FakpUNmFtUllOSlJzYTA5QTd3Zk9UYlJSem1TRzdVeUV1WGpLS28iLCJtYWMiOiIxM2E3NDFlMDA5ZmI0YzcxNmM0ZjNlZWRiNjJmNzRjNjliNDE0NmY1ZjQwMDI5MWRiZjZkMzk0YTQ3YjUxZjIwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Inl5bmNYYlZxT2pxSFVrZTFGOHMyUmc9PSIsInZhbHVlIjoiUHQ5NlRHa2xDQ1l5cUdyeXQ2YlR5cWlMOGR3c3FhYnZMOExoU2VQdnIzTURzOTdRVDlFNVpua1YvazV5Y0tENVNDUmhIbWNQNkN2bWFtZ2hKdUNQZEI5eW9ZbVorcXVYR1RZZ3YwOUFCTmtvV3N3SCtsSkhRSUpQWmg3M3hncVgiLCJtYWMiOiI3ODIyNGI3MmFlZDQ3NTc4NzdmZGJlOTQwMjc4ODkxNmJmYjAzZWNiN2U2NDM2ZmMwNmVkMTNmYzJhNzY1ZjVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972042937\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2133707419 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>twk_uuid_658d2c0670c9f2407f83ed78</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_cookie_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cxG5K19WWqXXLlQZnahwNoDJ8wr1XrsemGRzohNc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2133707419\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-63976994 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 13:38:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">POST, GET, PUT, DELETE</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63976994\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1897267406 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">joU1t01lA2HEYgAoRVr8fHdqxz7QPxIEL7YcBoJr</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/storage/lang/flags/in.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/theme-option</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>adminLogin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user_log_in</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1897267406\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/candidate?_=1750167478609&columns%5B0%5D%5Bdata%5D=Candidate&columns%5B0...", "action_name": "admin.candidate", "controller_action": "App\\Http\\Controllers\\Backend\\CandidateController@index"}, "badge": null}}