<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\CandidateRequest;
use App\Models\Admin\Candidate;
use App\Repositories\CandidateRepository;
use Illuminate\Http\Request;

class CandidateController extends Controller
{
    public function __construct(protected CandidateRepository $candidate) {}

    /** display all resource
     *  ========== index ========
     *
     * @param Request
     * @return Response
     */
    public function index(Request $request)
    {

        if ($request->ajax()) {
            return $this->candidate->content();
        }
        return view('admin.pages.candidate.index');
    }

    /** new resource create form
     * ======= create =====
     *
     * @return view;
     */
    public function create()
    {
        return view('admin.pages.candidate.create');
    }

    /** new candidate store
     * ========= store============
     *
     * @param App\Http\Requests\CandidateRequest
     * @return Response
     */
    public function store(CandidateRequest $request)
    {

        if (!$this->hasPermissions(['candidate.add'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->candidate->create($request);
        toastr()->success('', $result['message'], ['positionClass' => 'toast-top-right']);
        return response()->json([
            'status' => $result['status'],
            'url' => $result['status'] ? route('admin.company') : null
        ]);

    }

    /** specific resource edit by id
     * ============ edit =============
     *
     * @param  int id
     * @return Response
     */
    public function edit($id)
    {

        $lang = Request()->lang;
        $candidate = $this->candidate->getById($id);

        return view('admin.pages.candidate.edit', compact('candidate', 'lang'));
    }

    /** specific resource update by id
     * ========= update ===========
     *
     * @param App\Http\Requests\CandidateRequest
     * @return Response
     */
    public function update(CandidateRequest $request)
    {

        if (!$this->hasPermissions(['candidate.edit'])) {
            return $this->permissionDeniedResponse();
        }

        $result = $this->candidate->update($request->id, $request);
        toastr()->success('', $result['message'], ['positionClass' => 'toast-top-right']);
        return response()->json([
            'status' => $result['status'],
            'url' => $result['status'] ? route('admin.candidate') : null
        ]);

    }

    public function profileView($id)
    {
        $candidate = $this->candidate->getById($id);

        // return  $candidate;
        $applyJobs = $candidate->applyJobs()->paginate(10);

        return view('admin.pages.candidate.details', compact('candidate', 'applyJobs'));
    }

    public function delete($id)
    {
        if (!$this->hasPermissions(['candidate.delete'])) {
            return $this->permissionDeniedResponse();
        }
        if (!$id) {
            return redirect()->back();
        }
        $data = Candidate::with('user')->find($id);
        if (!$data) {
            return response()->json(['success' => false]);
        }
        $data->user()->delete();
        $data->delete();
        return response()->json(['success' => true]);

    }

    /** Candidate  status change
     * ======= status Change ========
     *
     * @param  int id
     * @return Response
     */
    public function statusChange($id)
    {
        if (!$this->hasPermissions(['candidate.status'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->candidate->statusChange($id);
        return $this->formatResponse($result);

    }

    /** Email Verified
     * ======= emailVerified ========
     *
     * @param  int id
     * @return Response
     */
    public function emailVerified($id)
    {
        if (!$this->hasPermissions(['candidate.verify'])) {
            return $this->permissionDeniedResponse();
        }
        $result = $this->candidate->emailVerified($id);
        return $this->formatResponse($result);

    }
}
