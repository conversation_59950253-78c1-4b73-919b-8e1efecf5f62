<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;


    /**
     * Check if the user has the required permissions.
     *
     * @param array $permissions
     * @return bool
     */
    protected function hasPermissions(array $permissions): bool
    {
        return collect($permissions)->every(fn($permission) => auth()->user()->can($permission));
    }

    /**
     * Generate a permission denied response.
     *
     * @return JsonResponse
     */
    protected function permissionDeniedResponse(): JsonResponse
    {
        return response()->json([
            'status' => false,
            'message' => translate('You have no Permission')
        ]);
    }

    /**
     * Format the response based on the operation result.
     *
     * @param array $result
     * @return JsonResponse
     */
    protected function formatResponse( $result): JsonResponse
    {
        return response()->json([
            'status' => $result['status'],
            'message' => $result['message']
        ]);
    }
}
