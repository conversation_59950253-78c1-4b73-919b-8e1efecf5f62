<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class JobRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'category_id' => 'required',
            'job_type_id' => 'required',
            'job_title' => 'required',
            'job_location' => 'required',
            'job_vacancy' => 'required',
            'job_experience' => 'required',
            'job_deadline' => 'required',
            'job_description' => 'required',
            'job_gender' => 'required',
            'job_education' => 'required',
            'job_image' => Request()->id ? '' : 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'category_id.required' => 'Category name is required.',
            'job_type_id.required' => 'Job type is required.',
            'job_title.required' => 'Job Title is required.',
            'job_location.required' => 'Job Location is required.',
            'job_vacancy.required' => 'Job vacancy is required.',
            'job_experience.required' => 'Job Expericen is required.',
            'job_deadline.required' => 'Job Deadline is required.',
            'job_description.required' => 'Job description is required.',
            'job_gender.required' => 'Gender is required.',
            'job_education.required' => 'Education is required.',
            'job_image.required' => 'Image is required.',

        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'status' => false,
            'errors' => $validator->errors(),
        ]));
    }
}
