
<?php $__env->startSection('content'); ?>
    <!-- ========== Inner Banner Start============= -->
    <div class="inner-banner">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="banner-content text-center">
                        <h1><?php echo e($singleCompany->company_name); ?></h1>
                        <span></span>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><?php echo e(translate('Home')); ?></a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo e(translate('Company Details')); ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- ========== Inner Banner end============= -->
    <!-- ========== Job Details Start============= -->
    <div class="company-details-area pt-120 mb-120">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="post-thumb">
                        <?php if(fileExists('company', $singleCompany->company_cover_photo) == true &&
                                $singleCompany->company_cover_photo !== ''): ?>
                            <img class="img-fluid"
                                src="<?php echo e(asset('storage/company/' . $singleCompany->company_cover_photo)); ?>"
                                alt="Company Banner Image">
                        <?php else: ?>
                            <img src="<?php echo e(asset('frontend/assets/images/1296-322.png')); ?>" alt="">
                        <?php endif; ?>

                        <div class="company-logo">
                            <?php if(fileExists('company', $singleCompany->company_logo) == true &&
                                    $singleCompany->company_logo !== ''): ?>
                                <img class="img-fluid" src="<?php echo e(asset('storage/company/' . $singleCompany->company_logo)); ?>">
                            <?php else: ?>
                                <img class="img-fluid" src="<?php echo e(asset('frontend/assets/images/Logo.png')); ?>" alt="">
                            <?php endif; ?>

                        </div>
                    </div>
                    <div class="company-area">
                        <div class="company-info">
                            <h4><?php echo e($singleCompany->company_name); ?></h4>
                            <ul>
                                <li><img src="<?php echo e(asset('frontend/assets/images/icon/map-2.svg')); ?>"
                                        alt=""><?php echo e($singleCompany->city_name); ?>,
                                    <?php echo e($singleCompany->country_name); ?> </li>
                                <li><img src="<?php echo e(asset('frontend/assets/images/icon/category-2.svg')); ?>"
                                        alt=""><span class="title"><?php echo e(translate('Category')); ?>: </span> <?php echo e(translate('Software Agency')); ?></li>
                            </ul>
                        </div>
                        <div class="company-contact">
                            <div class="websitet-link">
                                <p><img src="<?php echo e(asset('frontend/assets/images/icon/web-5.svg')); ?>" alt=""><?php echo e(translate('Website
                                    Link')); ?>:</p>
                                <a href="https://<?php echo e($singleCompany->company_website); ?>"
                                    target="_blank"><?php echo e($singleCompany->company_website); ?></a>
                            </div>
                            <div class="social-area">
                                <p><img
                                        src="<?php echo e(asset('frontend/assets/images/icon/share-icon.svg')); ?>"alt=""><?php echo e(translate('Follow Company')); ?>:</p>
                                <ul>

                                    <?php if(isset($singleCompany->company_facebook)): ?>
                                        <li><a href="<?php echo e($singleCompany->company_facebook); ?>"><i
                                                    class="bx bxl-facebook"></i></a></li>
                                    <?php endif; ?>
                                    <?php if(isset($singleCompany->company_twitter)): ?>
                                        <li><a href="<?php echo e($singleCompany->company_twitter); ?>"><i
                                                    class="bx bxl-twitter"></i></a> </li>
                                    <?php endif; ?>

                                    <?php if(isset($singleCompany->company_linkedin)): ?>
                                        <li><a href="<?php echo e($singleCompany->company_linkedin); ?>"><i
                                                    class="bx bxl-linkedin"></i></a></li>
                                    <?php endif; ?>

                                    <?php if(isset($singleCompany->company_instagram)): ?>
                                        <li><a href="<?php echo e($singleCompany->company_instagram); ?>"><i
                                                    class="bx bxl-instagram"></i></a></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="company-details-content">
                                <h5><?php echo e(translate('About Company')); ?></h5>
                                <?php echo clean($singleCompany->company_details); ?>

                                <br><br>

                                <h5><?php echo e(translate('Our Goal')); ?></h5>
                                <?php echo $singleCompany->company_vision; ?>

                                <br><br>
                                <?php if($singleCompany->workAreas->count() > 0): ?>
                                    <h5><?php echo e(translate('Working Area')); ?></h5>

                                    <div class="row pt-20">
                                        <?php $__currentLoopData = $singleCompany->workAreas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="col-sm-6 mb-50">
                                                <div class="working-process">
                                                    <div class="icon">

                                                        <?php if(fileExists( 'company/icons', $item['working_image']) == true && $item['working_image'] !== ''): ?>
                                                            <img class="img-fluid"
                                                                src="<?php echo e(asset('storage/company/icons/' . $item['working_image'])); ?>">
                                                        <?php else: ?>
                                                            <img src="<?php echo e(asset('frontend/assets/images/Logo.png')); ?>"
                                                                alt="">
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="work-content">
                                                        <h6><?php echo e($item['working_field']); ?></h6>
                                                        <p><?php echo e($item['working_description']); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php endif; ?>

                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="company-dt-sb mb-60">
                                <div class="company-overview-area mb-50">
                                    <h5><?php echo e(translate('Company Overview')); ?>:</h5>
                                    <ul>
                                        <li><span><?php echo e(translate('Company Name')); ?>: </span><?php echo e($singleCompany->company_name); ?></li>
                                        <li><span><?php echo e(translate('Company Type')); ?>: </span>
                                            <?php echo e($singleCompany?->companytype?->company_type_name); ?></li>
                                        <li><span><?php echo e(translate('Location')); ?>: </span><?php echo e($singleCompany?->city_name); ?>,
                                            <?php echo e($singleCompany->country_name ?? ''); ?> </li>
                                        <li><span><?php echo e(translate('Member Since')); ?>:
                                            </span><?php echo e(customDateFormate($singleCompany->establishment_date, $format = 'd F, Y')); ?>

                                        </li>
                                        <li><span><?php echo e(translate('Company size')); ?>: </span> <?php echo e($singleCompany->company_size ?? ''); ?></li>
                                        <li><span><?php echo e(translate('Completed Job')); ?>: </span> <?php echo e($singleCompany->jobs_count ?? 0); ?></li>

                                    </ul>
                                </div>
                                <?php

                                   $jobList= getThemeOption('job_list_url')?? null ;
                                ?>
                                <a class="primry-btn-2 lg-btn" href="<?php echo e($jobList ? $jobList ."?company=".$singleCompany->company_name :"#"); ?>"><?php echo e(translate('Job Available')); ?>

                                    <?php echo e($singleCompany->active_jobs_count); ?></a>

                                <a class="primry-btn-1 lg-btn" target="__blank"
                                    href="<?php echo e(getmapLink($singleCompany->iframe_link)); ?>">
                                    <?php echo e(translate('Go Our Site Map')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php if(count($latestJobs) > 0): ?>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="section-title">
                            <h4><?php echo e(translate('Latest Jobs')); ?>:</h4>
                        </div>

                        <?php $__currentLoopData = $latestJobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-12 mb-30">
                                <div class="job-listing-card">
                                    <div class="job-top">
                                        <div class="job-list-content">
                                            <div class="company-area">
                                                <div class="logo">

                                                    <?php if(fileExists('jobs', $job->job_image) == true && $job->job_image !== ''): ?>
                                                        <img src="<?php echo e(asset('storage/jobs/' . $job->job_image)); ?>"
                                                            alt="">
                                                    <?php else: ?>
                                                        <img src="<?php echo e(asset('frontend/assets/images/Logo.png')); ?>"
                                                            alt="">
                                                    <?php endif; ?>

                                                </div>
                                                <div class="company-details">
                                                    <div class="name-location">
                                                        <h5><a
                                                                href="<?php echo e(route('job.details', $job->slug)); ?>"><?php echo e($job->job_title); ?></a>
                                                        </h5>
                                                        <p><?php echo e($singleCompany->company_name); ?> </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="job-discription">
                                                <ul>
                                                    <li>
                                                        <p><span class="title"><?php echo e(translate('Location')); ?>:</span> <?php echo e($job->city_name); ?>,
                                                            <?php echo e($job->country_name); ?></p>
                                                    </li>
                                                    <li>
                                                        <p><span class="title"><?php echo e(translate('Salary')); ?>:</span>
                                                            <?php if($job->salary_mode == 'range'): ?>
                                                                $<?php echo e(dotZeroRemovePrice($job->min_salary)); ?>-$<?php echo e(dotZeroRemovePrice($job->max_salary)); ?> / <span
                                                                    class="time"><?php echo e($job->salary_type); ?></span>
                                                            <?php else: ?>
                                                                <?php echo e(ucfirst($job->salary_mode)); ?>

                                                            <?php endif; ?>

                                                        </p>
                                                    </li>
                                                </ul>
                                                <ul>
                                                    <li>
                                                        <p><span class="title"><?php echo e(translate('Experience')); ?>: </span>
                                                        <p>
                                                            <?php echo e($job->experiences?->implode('name', ',')); ?>. </p>
                                                    </li>
                                                    <li>
                                                        <p><span class="title"><?php echo e(translate('Deadline')); ?>:</span> <span>
                                                                <?php echo e(customDateFormate($job->job_deadline, $patter = 'd F, y')); ?></span>
                                                        </p>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <?php if(Auth::check()): ?>

                                        <?php
                                            $active=checkArrayValue($job->id, userBookBarkJobs())==true? "active":"";
                                        ?>
                                            <div class="bookmark <?php echo e($active); ?>"  data-action="<?php echo e(route('bookmark', $job->id)); ?>">
                                                <i class="bi bi-bookmark"></i>
                                            </div>

                                        <?php else: ?>
                                            <div class="bookmark"  data-action="<?php echo e(route('bookmark', $job->id)); ?>">
                                                <i class="bi bi-bookmark"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="job-type-apply">
                                        <p><img src="assets/images/icon/company-4.svg" alt=""> <?php echo e(translate('Job Applied')); ?>: <span>
                                                <?php echo e($job->job_vacancy); ?>

                                                <?php echo e(translate('Person')); ?></span></p>
                                        <div class="job-type">

                                            <?php $__currentLoopData = $job->jobJobTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($item->type_name == 'Full Time'): ?>
                                                    <span class="light-yellow"><?php echo e($item->type_name); ?> </span>
                                                <?php endif; ?>
                                                <?php if($item->type_name == 'Part Time'): ?>
                                                    <span class="light-purple"><?php echo e($item->type_name); ?> </span>
                                                <?php endif; ?>
                                                <?php if($item->type_name == 'Remote'): ?>
                                                    <span class="light-blue"><?php echo e($item->type_name); ?> </span>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                        </div>
                                        <div class="apply-btn">
                                            <a href="<?php echo e(route('job.details', $job->slug)); ?>"><span><img
                                                        src="assets/images/icon/apply-ellipse.svg"
                                                        alt=""></span><?php echo e(translate('Apply Now')); ?></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <div class="pagination-area">
                            <?php echo e($latestJobs->links('front.vendor.pagination.custom')); ?>

                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <!-- ========== Job Details end=============-->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('front.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\xampp\htdocs\empzio\resources\views/front/pages/company/company-details.blade.php ENDPATH**/ ?>